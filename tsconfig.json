{"extends": "@tsconfig/react-native/tsconfig.json", "compilerOptions": {"strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "noEmit": true, "incremental": true, "tsBuildInfoFile": ".tsbuildinfo", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-native", "baseUrl": ".", "paths": {"*": ["src/*"]}, "types": ["node", "jest"], "lib": ["es2020", "dom"]}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"], "exclude": ["node_modules", "babel.config.js", "metro.config.js"]}