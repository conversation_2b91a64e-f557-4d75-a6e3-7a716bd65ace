#!/usr/bin/env node

/**
 * Phosphor Icons Setup Script for TailorZa
 * 
 * This script helps integrate Phosphor Icons into the React Native app.
 * 
 * Usage:
 * 1. Install @phosphor-icons/react package
 * 2. Run this script to generate the icon registry
 * 
 * Note: This script is now used for updating the icon registry when new icons are needed.
 */

const fs = require('fs');
const path = require('path');

// Icons are now imported from @phosphor-icons/react package
// const ICONS_DIR = path.join(__dirname, '../assets/icons');
const OUTPUT_FILE = path.join(__dirname, '../src/utils/phosphorIconRegistry.tsx');

// Common Phosphor Icons that would be useful for TailorZa
const TAILORZA_ICONS = [
  // Navigation & UI
  'house', 'house-simple', 'house-line', 'house-bold',
  'gear', 'gear-six', 'gear-simple',
  'user', 'user-circle', 'user-plus',
  'magnifying-glass', 'magnifying-glass-plus',
  'plus', 'minus', 'x', 'check',
  
  // Business & Commerce
  'storefront', 'shopping-cart', 'shopping-bag',
  'credit-card', 'money', 'coins', 'bank',
  'receipt', 'invoice', 'calculator',
  
  // Tailoring & Fashion
  'scissors', 'needle', 'ruler', 'tape-measure',
  'tshirt', 'dress', 'pants', 'shoe',
  'mirror', 'hanger', 'iron',
  
  // Data & Analytics
  'chart-line', 'chart-bar', 'chart-pie',
  'trend-up', 'trend-down', 'analytics',
  'database', 'cloud', 'backup',
  
  // Communication
  'phone', 'envelope', 'chat-circle',
  'headset', 'microphone', 'video-camera',
  
  // Status & Actions
  'check-circle', 'warning', 'error',
  'info', 'question', 'exclamation',
  'clock', 'calendar', 'timer',
  
  // Settings & Preferences
  'bell', 'bell-ringing', 'notification',
  'moon', 'sun', 'lightbulb',
  'lock', 'shield', 'key',
  
  // Files & Documents
  'file', 'folder', 'document',
  'image', 'camera', 'upload', 'download',
  
  // Social & People
  'users', 'user-group', 'team',
  'customer', 'staff', 'manager',
  
  // Orders & Inventory
  'package', 'box', 'inventory',
  'order', 'delivery', 'truck',
  
  // Financial
  'profit', 'loss', 'expense',
  'budget', 'tax', 'payment',
  
  // Support & Help
  'help', 'support', 'faq',
  'guide', 'tutorial', 'manual'
];

function generateIconRegistry() {
  console.log('🔧 Generating Phosphor Icons Registry...');
  
  const iconRegistry = `/**
 * Phosphor Icons Registry for TailorZa
 * 
 * This file contains Phosphor Icons integration for the TailorZa app.
 * Icons are imported from @phosphor-icons/react for better performance.
 */

import React from 'react';
import {
  House,
  HouseSimple,
  HouseLine,
  HouseBold,
  Gear,
  GearSix,
  GearSimple,
  User,
  UserCircle,
  UserPlus,
  MagnifyingGlass,
  MagnifyingGlassPlus,
  Plus,
  Minus,
  X,
  Check,
  Storefront,
  ShoppingCart,
  ShoppingBag,
  CreditCard,
  Money,
  Coins,
  Bank,
  Receipt,
  Invoice,
  Calculator,
  Scissors,
  Needle,
  Ruler,
  TapeMeasure,
  TShirt,
  Dress,
  Pants,
  Shoe,
  Mirror,
  Hanger,
  Iron,
  ChartLine,
  ChartBar,
  ChartPie,
  TrendUp,
  TrendDown,
  Analytics,
  Database,
  Cloud,
  Backup,
  Phone,
  Envelope,
  ChatCircle,
  Headset,
  Microphone,
  VideoCamera,
  CheckCircle,
  Warning,
  Error,
  Info,
  Question,
  Exclamation,
  Clock,
  Calendar,
  Timer,
  Bell,
  BellRinging,
  Notification,
  Moon,
  Sun,
  Lightbulb,
  Lock,
  Shield,
  Key,
  File,
  Folder,
  Document,
  Image,
  Camera,
  Upload,
  Download,
  Users,
  UserGroup,
  Team,
  Customer,
  Staff,
  Manager,
  Package,
  Box,
  Inventory,
  Order,
  Delivery,
  Truck,
  Profit,
  Loss,
  Expense,
  Budget,
  Tax,
  Payment,
  Help,
  Support,
  FAQ,
  Guide,
  Tutorial,
  Manual
} from '@phosphor-icons/react';

// Type-safe icon names
export type PhosphorIconName = 
  | 'house' | 'house-simple' | 'house-line' | 'house-bold'
  | 'gear' | 'gear-six' | 'gear-simple'
  | 'user' | 'user-circle' | 'user-plus'
  | 'magnifying-glass' | 'magnifying-glass-plus'
  | 'plus' | 'minus' | 'x' | 'check'
  | 'storefront' | 'shopping-cart' | 'shopping-bag'
  | 'credit-card' | 'money' | 'coins' | 'bank'
  | 'receipt' | 'invoice' | 'calculator'
  | 'scissors' | 'needle' | 'ruler' | 'tape-measure'
  | 'tshirt' | 'dress' | 'pants' | 'shoe'
  | 'mirror' | 'hanger' | 'iron'
  | 'chart-line' | 'chart-bar' | 'chart-pie'
  | 'trend-up' | 'trend-down' | 'analytics'
  | 'database' | 'cloud' | 'backup'
  | 'phone' | 'envelope' | 'chat-circle'
  | 'headset' | 'microphone' | 'video-camera'
  | 'check-circle' | 'warning' | 'error'
  | 'info' | 'question' | 'exclamation'
  | 'clock' | 'calendar' | 'timer'
  | 'bell' | 'bell-ringing' | 'notification'
  | 'moon' | 'sun' | 'lightbulb'
  | 'lock' | 'shield' | 'key'
  | 'file' | 'folder' | 'document'
  | 'image' | 'camera' | 'upload' | 'download'
  | 'users' | 'user-group' | 'team'
  | 'customer' | 'staff' | 'manager'
  | 'package' | 'box' | 'inventory'
  | 'order' | 'delivery' | 'truck'
  | 'profit' | 'loss' | 'expense'
  | 'budget' | 'tax' | 'payment'
  | 'help' | 'support' | 'faq'
  | 'guide' | 'tutorial' | 'manual';

// Icon mapping
const PHOSPHOR_ICONS: Record<PhosphorIconName, React.ComponentType<any>> = {
  // Navigation & UI
  'house': House,
  'house-simple': HouseSimple,
  'house-line': HouseLine,
  'house-bold': HouseBold,
  'gear': Gear,
  'gear-six': GearSix,
  'gear-simple': GearSimple,
  'user': User,
  'user-circle': UserCircle,
  'user-plus': UserPlus,
  'magnifying-glass': MagnifyingGlass,
  'magnifying-glass-plus': MagnifyingGlassPlus,
  'plus': Plus,
  'minus': Minus,
  'x': X,
  'check': Check,
  
  // Business & Commerce
  'storefront': Storefront,
  'shopping-cart': ShoppingCart,
  'shopping-bag': ShoppingBag,
  'credit-card': CreditCard,
  'money': Money,
  'coins': Coins,
  'bank': Bank,
  'receipt': Receipt,
  'invoice': Invoice,
  'calculator': Calculator,
  
  // Tailoring & Fashion
  'scissors': Scissors,
  'needle': Needle,
  'ruler': Ruler,
  'tape-measure': TapeMeasure,
  'tshirt': TShirt,
  'dress': Dress,
  'pants': Pants,
  'shoe': Shoe,
  'mirror': Mirror,
  'hanger': Hanger,
  'iron': Iron,
  
  // Data & Analytics
  'chart-line': ChartLine,
  'chart-bar': ChartBar,
  'chart-pie': ChartPie,
  'trend-up': TrendUp,
  'trend-down': TrendDown,
  'analytics': Analytics,
  'database': Database,
  'cloud': Cloud,
  'backup': Backup,
  
  // Communication
  'phone': Phone,
  'envelope': Envelope,
  'chat-circle': ChatCircle,
  'headset': Headset,
  'microphone': Microphone,
  'video-camera': VideoCamera,
  
  // Status & Actions
  'check-circle': CheckCircle,
  'warning': Warning,
  'error': Error,
  'info': Info,
  'question': Question,
  'exclamation': Exclamation,
  'clock': Clock,
  'calendar': Calendar,
  'timer': Timer,
  
  // Settings & Preferences
  'bell': Bell,
  'bell-ringing': BellRinging,
  'notification': Notification,
  'moon': Moon,
  'sun': Sun,
  'lightbulb': Lightbulb,
  'lock': Lock,
  'shield': Shield,
  'key': Key,
  
  // Files & Documents
  'file': File,
  'folder': Folder,
  'document': Document,
  'image': Image,
  'camera': Camera,
  'upload': Upload,
  'download': Download,
  
  // Social & People
  'users': Users,
  'user-group': UserGroup,
  'team': Team,
  'customer': Customer,
  'staff': Staff,
  'manager': Manager,
  
  // Orders & Inventory
  'package': Package,
  'box': Box,
  'inventory': Inventory,
  'order': Order,
  'delivery': Delivery,
  'truck': Truck,
  
  // Financial
  'profit': Profit,
  'loss': Loss,
  'expense': Expense,
  'budget': Budget,
  'tax': Tax,
  'payment': Payment,
  
  // Support & Help
  'help': Help,
  'support': Support,
  'faq': FAQ,
  'guide': Guide,
  'tutorial': Tutorial,
  'manual': Manual
};

// Phosphor Icon Component
interface PhosphorIconProps {
  name: PhosphorIconName;
  size?: number;
  color?: string;
  weight?: 'thin' | 'light' | 'regular' | 'bold' | 'fill';
  style?: any;
}

export const PhosphorIcon: React.FC<PhosphorIconProps> = ({ 
  name, 
  size = 24, 
  color = '#000000',
  weight = 'regular',
  style 
}) => {
  const IconComponent = PHOSPHOR_ICONS[name];
  
  if (!IconComponent) {
    console.warn(\`Phosphor Icon "\${name}" not found\`);
    return null;
  }

  return (
    <IconComponent 
      size={size} 
      color={color}
      weight={weight}
      style={style} 
    />
  );
};

// Utility functions
export const hasPhosphorIcon = (name: string): name is PhosphorIconName => {
  return name in PHOSPHOR_ICONS;
};

export const getAvailablePhosphorIcons = (): PhosphorIconName[] => {
  return Object.keys(PHOSPHOR_ICONS) as PhosphorIconName[];
};

export default PhosphorIcon;
`;

  fs.writeFileSync(OUTPUT_FILE, iconRegistry);
  console.log('✅ Phosphor Icons Registry generated successfully!');
  console.log(`📁 Output file: ${OUTPUT_FILE}`);
}

function createPackageJsonScript() {
  const packageJsonPath = path.join(__dirname, '../package.json');
  
  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    if (!packageJson.scripts) {
      packageJson.scripts = {};
    }
    
    packageJson.scripts['setup-phosphor'] = 'node scripts/setup-phosphor-icons.js';
    
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    console.log('✅ Added setup-phosphor script to package.json');
  }
}

function main() {
  try {
    generateIconRegistry();
    createPackageJsonScript();
    
    console.log('\n🎉 Phosphor Icons setup complete!');
    console.log('\n📋 Next steps:');
    console.log('1. Install @phosphor-icons/react: npm install @phosphor-icons/react');
    console.log('2. Update your icon registry to use PhosphorIcon component');
    console.log('3. Replace existing icon usage with PhosphorIcon');
    console.log('\n💡 Example usage:');
    console.log('<PhosphorIcon name="house" size={24} color="#007AFF" weight="regular" />');
    
  } catch (error) {
    console.error('❌ Error setting up Phosphor Icons:', error);
  }
}

if (require.main === module) {
  main();
}

module.exports = { generateIconRegistry, createPackageJsonScript }; 