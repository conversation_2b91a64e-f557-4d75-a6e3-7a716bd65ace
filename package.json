{"name": "bakery-management-md3", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "quality:check": "npm run type-check && npm run lint && npm run format:check", "quality:fix": "npm run lint:fix && npm run format", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "build:analyze": "npx expo export --platform web && npx webpack-bundle-analyzer web-build/static/js/*.js", "setup-phosphor": "node scripts/setup-phosphor-icons.js"}, "dependencies": {"@gorhom/bottom-sheet": "^5.1.4", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-native-picker/picker": "^2.11.0", "@react-navigation/native": "^7.1.9", "@react-navigation/stack": "^7.3.2", "crypto-js": "^4.2.0", "expo": "^53.0.20", "expo-barcode-scanner": "^13.0.1", "expo-camera": "~16.1.11", "expo-document-picker": "^13.1.5", "expo-file-system": "^18.1.10", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "^14.1.5", "expo-media-library": "~17.1.6", "expo-secure-store": "~14.2.3", "expo-sharing": "^13.1.5", "expo-sqlite": "^15.2.10", "expo-status-bar": "~2.2.3", "phosphor-react-native": "^3.0.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "^0.79.5", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-html-to-pdf": "^0.12.0", "react-native-image-picker": "^8.2.1", "react-native-pager-view": "6.7.1", "react-native-paper": "^5.14.5", "react-native-print": "^0.11.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-svg": "^15.11.2", "react-native-tab-view": "^4.1.2", "react-native-web": "^0.20.0", "supports-hyperlinks": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@tsconfig/react-native": "^3.0.6", "@types/jest": "^30.0.0", "@types/node": "^24.1.0", "@types/react": "~19.0.10", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.0.0", "eslint-plugin-import": "^2.28.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.1.0", "jest": "~29.7.0", "jest-environment-jsdom": "^30.0.5", "prettier": "^3.0.0", "ts-jest": "^29.4.0", "typescript": "~5.8.3"}, "private": true}