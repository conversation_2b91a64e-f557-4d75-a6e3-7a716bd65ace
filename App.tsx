import React from 'react';
import { View, ActivityIndicator } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { PaperProvider } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

import { createPaperTheme, getFallbackTheme } from './src/theme/paperThemeAdapter';

// Import navigation and contexts
import AppNavigator from './src/navigation/AppNavigator';
import { DataProvider } from './src/context/DataContext';
import { ThemeProvider, useTheme } from './src/context/ThemeContext';
import { FinancialProvider } from './src/context/FinancialContext';
import { AuthProvider } from './src/context/AuthContext';
import NotificationService from './src/services/notificationService';
import LoggingService from './src/services/LoggingService';

import BottomSheetProvider from './src/components/utils/BottomSheetProvider';
import ErrorBoundary from './src/components/utils/ErrorBoundary';
import SessionManager from './src/components/session/SessionManager';
import { navigationRef } from './src/services/NavigationService';





// Root App Component with providers and error boundary
const App: React.FC = () => {

  const handleAppError = (): void => {
    LoggingService.info('App error handled', 'APP');
  };

  const handleAppReload = (): void => {
    LoggingService.info('App reload requested', 'APP');
  };

  if (__DEV__) {
    NotificationService.seedDummyNotifications();
  }



  return (
    <ErrorBoundary
      onRetry={handleAppError}
      onReload={handleAppReload}
    >
      <GestureHandlerRootView style={{ flex: 1 }}>
        <SafeAreaProvider>
          <ErrorBoundary>
            <ThemeProvider>
              <ErrorBoundary>
                <AuthProvider>
                  <ErrorBoundary>
                    <DataProvider>
                      <ErrorBoundary>
                        <FinancialProvider>
                          <AppContent />
                        </FinancialProvider>
                      </ErrorBoundary>
                    </DataProvider>
                  </ErrorBoundary>
                </AuthProvider>
              </ErrorBoundary>
            </ThemeProvider>
          </ErrorBoundary>
        </SafeAreaProvider>
      </GestureHandlerRootView>
    </ErrorBoundary>
  );
};

// Main App Component that uses theme - moved inside provider structure
const AppContent: React.FC = () => {
  const themeContext = useTheme();
  const { isDarkMode, isLoading } = themeContext;

  // Ensure theme is available before rendering
  if (isLoading || !themeContext) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#F8F9FA' }}>
        <ActivityIndicator size="large" color="#1877F2" />
      </View>
    );
  }

  // Create a proper React Native Paper theme using the adapter
  const paperTheme = themeContext ? createPaperTheme(isDarkMode) : getFallbackTheme();

  return (
    <PaperProvider theme={paperTheme}>
      <NavigationContainer ref={navigationRef}>
        <SessionManager>
          <BottomSheetProvider>
            <AppNavigator />
          </BottomSheetProvider>
        </SessionManager>
        <StatusBar style={isDarkMode ? "light" : "dark"} />
      </NavigationContainer>
    </PaperProvider>
  );
};

export default App;