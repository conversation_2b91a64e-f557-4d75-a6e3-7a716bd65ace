# Bottom Sheet Migration Guide

## Overview
This guide shows how to migrate from 15 individual bottom sheet components to a unified system using `UnifiedWrapper.tsx`.

## Current Bottom Sheets
- AddCustomerBottomSheet.tsx
- EditProfileBottomSheet.tsx
- ExpenseBottomSheet.tsx
- OrderBottomSheet.tsx
- OrderDetailsBottomSheet.tsx
- OutletBottomSheet.tsx
- PaymentAnalyticsBottomSheet.tsx
- PDFInvoiceBottomSheet.tsx
- ProductBottomSheet.tsx
- ProfitLossBottomSheet.tsx
- QuickActionsBottomSheet.tsx
- SecuritySettingsBottomSheet.tsx
- TaxSummaryBottomSheet.tsx
- CashReconciliationBottomSheet.tsx

## Migration Strategy

### Phase 1: Simple Replacements (Week 1)
Replace simple bottom sheets that don't require complex forms:

1. **QuickActionsBottomSheet** → `UnifiedWrapper` type="quick-actions"
2. **OrderDetailsBottomSheet** → `UnifiedWrapper` type="order-details"

### Phase 2: Form-based Sheets (Week 2)
Replace form-based bottom sheets:

1. **AddCustomerBottomSheet** → `UnifiedWrapper` type="add-customer"
2. **EditProfileBottomSheet** → `UnifiedWrapper` type="edit-profile"

### Phase 3: Complex Sheets (Week 3)
Replace complex bottom sheets with custom content:

1. **ProductBottomSheet** → Custom content in `UnifiedWrapper`
2. **OrderBottomSheet** → Custom content in `UnifiedWrapper`

### Phase 4: Analytics Sheets (Week 4)
Replace analytics and reporting sheets:

1. **PaymentAnalyticsBottomSheet** → `UnifiedWrapper` type="payment-analytics"
2. **ProfitLossBottomSheet** → `UnifiedWrapper` type="profit-loss"

## Usage Examples

### Before (Individual Component)
```tsx
import AddCustomerBottomSheet from '../components/bottomsheets/AddCustomerBottomSheet';

const MyScreen = () => {
  const customerSheetRef = useRef();
  
  return (
    <>
      <Button onPress={() => customerSheetRef.current?.expand()}>
        Add Customer
      </Button>
      <AddCustomerBottomSheet
        ref={customerSheetRef}
        onSave={handleSaveCustomer}
        onClose={handleClose}
      />
    </>
  );
};
```

### After (Unified Wrapper)
```tsx
import UnifiedWrapper from '../components/bottomsheets/UnifiedWrapper';

const MyScreen = () => {
  const bottomSheetRef = useRef();
  
  return (
    <>
      <Button onPress={() => bottomSheetRef.current?.open()}>
        Add Customer
      </Button>
      <UnifiedWrapper
        ref={bottomSheetRef}
        type="add-customer"
        title="Add Customer"
        mode="add"
        onSave={handleSaveCustomer}
        onClose={handleClose}
      />
    </>
  );
};
```

## Benefits

1. **Reduced Bundle Size**: From 15 components to 1
2. **Consistent API**: Same interface for all bottom sheets
3. **Easier Maintenance**: Single place to update bottom sheet logic
4. **Better Type Safety**: Centralized types and validation
5. **Reusable Logic**: Common patterns shared across all sheets

## Migration Checklist

### ✅ Phase 1 - Completed (Simple Replacements)
- [x] Replace QuickActionsBottomSheet → UnifiedWrapper (TabNavigator)
- [x] Replace OrderDetailsBottomSheet → UnifiedWrapper (OrdersScreen)
- [x] Replace AddCustomerBottomSheet → UnifiedWrapper (CustomersScreen, TabNavigator)
- [x] Replace EditProfileBottomSheet → UnifiedWrapper (ProfileScreen)

### 🔄 Phase 2 - In Progress (Form-based Sheets)
- [ ] Replace SecuritySettingsBottomSheet → UnifiedWrapper (Not currently used)
- [ ] Replace OutletBottomSheet → UnifiedWrapper (OutletManagementScreen)

### 📋 Phase 3 - Planned (Keep Separate - Complex Logic)
- [ ] ProductBottomSheet (Keep separate - complex icon selection)
- [ ] OrderBottomSheet (Keep separate - dynamic calculations)
- [ ] ExpenseBottomSheet (Keep separate - financial validation)
- [ ] CashReconciliationBottomSheet (Keep separate - reconciliation logic)

### 📊 Phase 4 - Analytics Sheets
- [ ] PaymentAnalyticsBottomSheet → UnifiedWrapper
- [ ] ProfitLossBottomSheet → UnifiedWrapper
- [ ] TaxSummaryBottomSheet → UnifiedWrapper
- [ ] PDFInvoiceBottomSheet → UnifiedWrapper

### 🧹 Cleanup Phase
- [ ] Remove migrated bottom sheet files
- [ ] Update remaining imports
- [ ] Test all bottom sheet functionality

## Next Steps

1. Start with Phase 1 (simple replacements)
2. Test thoroughly after each phase
3. Gradually add more content types to `UnifiedWrapper`
4. Consider adding a reducer for more complex state management
5. Add proper form validation and error handling 