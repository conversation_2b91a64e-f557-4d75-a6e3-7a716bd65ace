# Optional Dependencies for Enhanced Features

The inventory management system includes some optional dependencies that enhance functionality but are not required for basic operation.

## Barcode Scanner

To enable camera-based barcode scanning:

```bash
npm install expo-barcode-scanner
```

After installation, restart your development server and the barcode scanner will be fully functional.

**Features enabled:**
- Camera-based barcode scanning
- Real-time barcode recognition
- Automatic item lookup

**Fallback:** Manual barcode entry is always available.

## Charts and Analytics

To enable visual charts and graphs in the analytics screen:

```bash
npm install react-native-chart-kit react-native-svg
```

For iOS, you may also need to run:
```bash
cd ios && pod install
```

**Features enabled:**
- Pie charts for category distribution
- Line charts for monthly trends
- Bar charts for forecast confidence
- Visual inventory analytics

**Fallback:** Text-based data tables and progress bars are used when charts are not available.

## Installation Commands

To install all optional dependencies at once:

```bash
npm install expo-barcode-scanner react-native-chart-kit react-native-svg
```

For iOS projects:
```bash
cd ios && pod install
```

## Feature Availability

| Feature | Without Dependencies | With Dependencies |
|---------|---------------------|-------------------|
| Inventory Management | ✅ Full functionality | ✅ Full functionality |
| Manual Barcode Entry | ✅ Available | ✅ Available |
| Camera Barcode Scan | ❌ Not available | ✅ Available |
| Data Analytics | ✅ Text/tables | ✅ Visual charts |
| Inventory Audits | ✅ Full functionality | ✅ Full functionality |
| Stock Transfers | ✅ Full functionality | ✅ Full functionality |

## Notes

- The inventory system is fully functional without these dependencies
- Optional features gracefully degrade to alternative implementations
- All core business logic remains unchanged
- Installation can be done at any time without data loss