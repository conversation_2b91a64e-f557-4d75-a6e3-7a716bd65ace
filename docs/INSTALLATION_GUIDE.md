# Installation Guide for Enhanced Inventory System

## Quick Start

The inventory management system is ready to use immediately with all core features available. Optional dependencies can be added later for enhanced functionality.

## Core Features (No additional installation required)

✅ **Inventory Management** - Add, edit, delete warehouse items
✅ **Stock Tracking** - Monitor stock levels and transactions  
✅ **Inventory Audits** - Physical count and variance tracking
✅ **Transfer Management** - Move inventory between locations
✅ **Analytics** - Text-based reports and data tables
✅ **Manual Barcode Entry** - Enter barcodes manually
✅ **Alerts & Notifications** - Stock level warnings
✅ **Settings & Configuration** - Customize system behavior

## Optional Enhancements

### 1. Camera Barcode Scanning

**Install:**
```bash
npm install expo-barcode-scanner
```

**Enables:**
- Camera-based barcode scanning
- Real-time barcode recognition
- Faster item lookup and transactions

### 2. Visual Charts & Analytics

**Install:**
```bash
npm install react-native-chart-kit react-native-svg
```

**For iOS projects:**
```bash
cd ios && pod install
```

**Enables:**
- Pie charts for category distribution
- Line charts for trends and forecasting
- Bar charts for comparative analysis
- Enhanced visual analytics dashboard

### 3. Complete Enhanced Experience

**Install all optional dependencies:**
```bash
npm install expo-barcode-scanner react-native-chart-kit react-native-svg
```

**For iOS projects:**
```bash
cd ios && pod install
```

## Post-Installation

1. **Restart your development server** after installing any dependencies
2. **Clear cache if needed:**
   ```bash
   npm start -- --clear
   ```
3. **For iOS:** Run `cd ios && pod install` after installing chart dependencies

## Verification

After installation, verify features are working:

1. **Barcode Scanner:** Navigate to Inventory → Scan → Should show camera interface
2. **Charts:** Navigate to Inventory → Analytics → Should show visual charts
3. **Core Features:** All inventory operations should work regardless of optional dependencies

## Troubleshooting

### Barcode Scanner Issues
- Ensure camera permissions are granted
- Restart the app after installation
- Check that expo-barcode-scanner is properly installed

### Chart Display Issues
- Verify react-native-svg is installed
- For iOS, ensure pod install was run
- Clear cache and restart development server

### General Issues
- Clear node_modules and reinstall: `rm -rf node_modules && npm install`
- Reset Metro cache: `npm start -- --reset-cache`
- For iOS: Clean build folder in Xcode

## Feature Matrix

| Feature | Without Optional Deps | With Barcode Scanner | With Charts | With Both |
|---------|----------------------|---------------------|-------------|-----------|
| Inventory CRUD | ✅ | ✅ | ✅ | ✅ |
| Stock Tracking | ✅ | ✅ | ✅ | ✅ |
| Manual Barcode | ✅ | ✅ | ✅ | ✅ |
| Camera Scanning | ❌ | ✅ | ❌ | ✅ |
| Text Analytics | ✅ | ✅ | ✅ | ✅ |
| Visual Charts | ❌ | ❌ | ✅ | ✅ |
| Audits | ✅ | ✅ | ✅ | ✅ |
| Transfers | ✅ | ✅ | ✅ | ✅ |

## Support

The inventory system is designed to work seamlessly with or without optional dependencies. All core business functionality is available immediately, with enhancements providing improved user experience when installed.