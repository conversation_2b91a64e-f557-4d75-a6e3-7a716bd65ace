import { useSafeAreaInsets } from 'react-native-safe-area-context';

export const useSafeInsets = () => {
  try {
    const insets = useSafeAreaInsets();
    // Ensure we have a valid insets object with all required properties
    if (insets && typeof insets === 'object') {
      return {
        top: insets.top || 0,
        bottom: insets.bottom || 0,
        left: insets.left || 0,
        right: insets.right || 0,
      };
    }
    // Fallback to default values if insets is invalid
    return { top: 0, bottom: 0, left: 0, right: 0 };
  } catch (error) {
    // Fallback to default values if useSafeAreaInsets fails
    // console.warn('SafeAreaInsets failed, using fallback values:', error);
    return { top: 0, bottom: 0, left: 0, right: 0 };
  }
}; 