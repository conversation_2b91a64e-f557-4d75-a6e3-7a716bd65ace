
import { AppError } from '../utils/errorHandler';

import LoggingService from './LoggingService';
import { StorageService } from './storageService';

// Financial interfaces
interface Expense {
  id: string;
  outletId?: string; // Added for multi-outlet support
  category: string;
  amount: number;
  description: string;
  date: string;
  createdAt: string;
  updatedAt: string;
}

interface CashReconciliation {
  id: string;
  outletId?: string; // Added for multi-outlet support
  date: string;
  expectedCash: number;
  actualCash: number;
  difference: number;
  status: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

interface ProfitLossData {
  revenue: number;
  expenses: number;
  grossProfit: number;
  netProfit: number;
  profitMargin: number;
  outletId?: string; // Added for multi-outlet support
  period: {
    startDate: string;
    endDate: string;
  };
}

interface PaymentAnalytics {
  totalTransactions: number;
  totalAmount: number;
  outletId?: string; // Added for multi-outlet support
  paymentMethods: Record<string, {
    count: number;
    amount: number;
    percentage: number;
  }>;
  trends: Array<{
    date: string;
    amount: number;
    method: string;
  }>;
}

interface TaxSummary {
  totalTaxableAmount: number;
  totalTaxCollected: number;
  taxRate: number;
  outletId?: string; // Added for multi-outlet support
  period: {
    startDate: string;
    endDate: string;
  };
  breakdown: Record<string, {
    amount: number;
    tax: number;
  }>;
}

// New interfaces for multi-outlet financial management
interface OutletFinancialMetrics {
  outletId: string;
  outletName: string;
  revenue: number;
  expenses: number;
  profit: number;
  profitMargin: number;
  orderCount: number;
  averageOrderValue: number;
  period: {
    startDate: string;
    endDate: string;
  };
}

interface ConsolidatedFinancialReport {
  totalRevenue: number;
  totalExpenses: number;
  totalProfit: number;
  overallProfitMargin: number;
  outletMetrics: OutletFinancialMetrics[];
  period: {
    startDate: string;
    endDate: string;
  };
}

interface CostAllocation {
  outletId: string;
  outletName: string;
  directCosts: number;
  allocatedCosts: number;
  totalCosts: number;
  allocationMethod: 'revenue' | 'orders' | 'custom';
  period: {
    startDate: string;
    endDate: string;
  };
}

interface ProfitabilityAnalysis {
  outletId: string;
  outletName: string;
  garmentType: string;
  revenue: number;
  costs: number;
  profit: number;
  profitMargin: number;
  orderCount: number;
  period: {
    startDate: string;
    endDate: string;
  };
}

interface ExpenseFilters {
  category?: string;
  outletId?: string; // Added for multi-outlet support
  startDate?: string;
  endDate?: string;
  minAmount?: number;
  maxAmount?: number;
}

interface ReconciliationFilters {
  outletId?: string; // Added for multi-outlet support
  startDate?: string;
  endDate?: string;
  status?: string;
}

/**
 * Financial Service for managing all financial operations with multi-outlet support
 */
export class FinancialService {
  static STORAGE_KEYS = {
    EXPENSES: 'financial_expenses',
    RECONCILIATIONS: 'cash_reconciliations',
    TAX_SETTINGS: 'tax_settings',
    FINANCIAL_SETTINGS: 'financial_settings',
    OUTLET_FINANCIAL_DATA: 'outlet_financial_data',
    COST_ALLOCATIONS: 'cost_allocations',
  };

  /**
   * Expense Management with Multi-Outlet Support
   */
  static async addExpense(expense: Omit<Expense, 'id' | 'createdAt' | 'updatedAt'>): Promise<Expense> {
    try {
      const expenses = await this.getExpenses();
      const newExpense: Expense = {
        id: Date.now().toString(),
        ...expense,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      expenses.push(newExpense);
      await StorageService.set(this.STORAGE_KEYS.EXPENSES, expenses);

      LoggingService.info('Expense added:', 'FINANCIAL', newExpense);
      return newExpense;
    } catch (error) {
      LoggingService.error('Failed to add expense:', 'FINANCIAL', error instanceof Error ? error : undefined);
      throw new AppError('Failed to add expense');
    }
  }

  static async getExpenses(filters: ExpenseFilters = {}): Promise<Expense[]> {
    try {
      const expenses: Expense[] = await StorageService.get(this.STORAGE_KEYS.EXPENSES) || [];

      let filtered = expenses;

      if (filters.outletId) {
        filtered = filtered.filter(e => e.outletId === filters.outletId);
      }

      if (filters.category) {
        filtered = filtered.filter(e => e.category === filters.category);
      }

      if (filters.startDate) {
        filtered = filtered.filter(e => e.date >= filters.startDate!);
      }

      if (filters.endDate) {
        filtered = filtered.filter(e => e.date <= filters.endDate!);
      }

      if (filters.minAmount !== undefined) {
        filtered = filtered.filter(e => e.amount >= filters.minAmount!);
      }

      if (filters.maxAmount !== undefined) {
        filtered = filtered.filter(e => e.amount <= filters.maxAmount!);
      }

      return filtered.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    } catch (error) {
      LoggingService.error('Failed to get expenses:', 'FINANCIAL', error instanceof Error ? error : undefined);
      throw new AppError('Failed to retrieve expenses');
    }
  }

  static async updateExpense(id: string, updates: Partial<Expense>): Promise<Expense> {
    try {
      const expenses = await this.getExpenses();
      const index = expenses.findIndex(e => e.id === id);

      if (index === -1) {
        throw new AppError('Expense not found');
      }

      const updatedExpense: Expense = {
        ...expenses[index],
        ...updates,
        updatedAt: new Date().toISOString(),
      };

      expenses[index] = updatedExpense;
      await StorageService.set(this.STORAGE_KEYS.EXPENSES, expenses);

      LoggingService.info('Expense updated:', updatedExpense as any);
      return updatedExpense;
    } catch (error) {
      LoggingService.error('Failed to update expense:', error as any);
      throw new AppError('Failed to update expense');
    }
  }

  static async deleteExpense(id: string): Promise<void> {
    try {
      const expenses = await this.getExpenses();
      const filtered = expenses.filter(e => e.id !== id);

      if (filtered.length === expenses.length) {
        throw new AppError('Expense not found');
      }

      await StorageService.set(this.STORAGE_KEYS.EXPENSES, filtered);
      LoggingService.info('Expense deleted:', id as any);
    } catch (error) {
      LoggingService.error('Failed to delete expense:', error as any);
      throw new AppError('Failed to delete expense');
    }
  }

  /**
   * Cash Reconciliation with Multi-Outlet Support
   */
  static async performCashReconciliation(reconciliationData: Omit<CashReconciliation, 'id' | 'createdAt' | 'updatedAt'>): Promise<CashReconciliation> {
    try {
      const reconciliations = await this.getCashReconciliations();
      const newReconciliation: CashReconciliation = {
        id: Date.now().toString(),
        ...reconciliationData,
        difference: reconciliationData.actualCash - reconciliationData.expectedCash,
        status: Math.abs(reconciliationData.actualCash - reconciliationData.expectedCash) < 0.01 ? 'balanced' : 'unbalanced',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      reconciliations.push(newReconciliation);
      await StorageService.set(this.STORAGE_KEYS.RECONCILIATIONS, reconciliations);

      LoggingService.info('Cash reconciliation performed:', newReconciliation as any);
      return newReconciliation;
    } catch (error) {
      LoggingService.error('Failed to perform cash reconciliation:', error as any);
      throw new AppError('Failed to perform cash reconciliation');
    }
  }

  static async getCashReconciliations(filters: ReconciliationFilters = {}): Promise<CashReconciliation[]> {
    try {
      const reconciliations: CashReconciliation[] = await StorageService.get(this.STORAGE_KEYS.RECONCILIATIONS) || [];

      let filtered = reconciliations;

      if (filters.outletId) {
        filtered = filtered.filter(r => r.outletId === filters.outletId);
      }

      if (filters.startDate) {
        filtered = filtered.filter(r => r.date >= filters.startDate!);
      }

      if (filters.endDate) {
        filtered = filtered.filter(r => r.date <= filters.endDate!);
      }

      if (filters.status) {
        filtered = filtered.filter(r => r.status === filters.status);
      }

      return filtered.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    } catch (error) {
      LoggingService.error('Failed to get cash reconciliations:', error as any);
      throw new AppError('Failed to retrieve cash reconciliations');
    }
  }

  static async calculateDailyCashExpected(date: string, outletId?: string): Promise<number> {
    try {
      // This would typically integrate with your order/sales data
      // For now, return a placeholder calculation
      const orders: any[] = []; // Would get orders for the date and outlet
      const cashOrders = orders.filter((order: any) => 
        order.paymentMethod === 'cash' && 
        (!outletId || order.outletId === outletId)
      );
      return cashOrders.reduce((total: number, order: any) => total + order.total, 0);
    } catch (error) {
      LoggingService.error('Failed to calculate daily cash expected:', error as any);
      throw new AppError('Failed to calculate expected cash');
    }
  }

  /**
   * Multi-Outlet Financial Reports
   */
  static async generateOutletProfitLossStatement(outletId: string, startDate: string, endDate: string): Promise<ProfitLossData> {
    try {
      // Get revenue data for specific outlet
      const revenue = await this.calculateOutletRevenue(outletId, startDate, endDate);
      
      // Get expenses for specific outlet
      const expenses = await this.getExpenses({ outletId, startDate, endDate });
      const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);

      const grossProfit = revenue - totalExpenses;
      const netProfit = grossProfit; // Simplified - would include taxes, etc.
      const profitMargin = revenue > 0 ? (netProfit / revenue) * 100 : 0;

      const profitLoss: ProfitLossData = {
        revenue,
        expenses: totalExpenses,
        grossProfit,
        netProfit,
        profitMargin,
        outletId,
        period: { startDate, endDate },
      };

      LoggingService.info('Outlet P&L statement generated:', profitLoss as any);
      return profitLoss;
    } catch (error) {
      LoggingService.error('Failed to generate outlet P&L statement:', error as any);
      throw new AppError('Failed to generate outlet profit & loss statement');
    }
  }

  static async generateConsolidatedFinancialReport(startDate: string, endDate: string): Promise<ConsolidatedFinancialReport> {
    try {
      // Get all outlets (this would come from OutletService)
      const outlets = await this.getOutlets();
      const outletMetrics: OutletFinancialMetrics[] = [];

      let totalRevenue = 0;
      let totalExpenses = 0;
      let totalProfit = 0;

      for (const outlet of outlets) {
        const revenue = await this.calculateOutletRevenue(outlet.id, startDate, endDate);
        const expenses = await this.getExpenses({ outletId: outlet.id, startDate, endDate });
        const totalOutletExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);
        const profit = revenue - totalOutletExpenses;
        const profitMargin = revenue > 0 ? (profit / revenue) * 100 : 0;

        const orderCount = await this.getOutletOrderCount(outlet.id, startDate, endDate);
        const averageOrderValue = orderCount > 0 ? revenue / orderCount : 0;

        outletMetrics.push({
          outletId: outlet.id,
          outletName: outlet.name,
          revenue,
          expenses: totalOutletExpenses,
          profit,
          profitMargin,
          orderCount,
          averageOrderValue,
          period: { startDate, endDate },
        });

        totalRevenue += revenue;
        totalExpenses += totalOutletExpenses;
        totalProfit += profit;
      }

      const overallProfitMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;

      const consolidatedReport: ConsolidatedFinancialReport = {
        totalRevenue,
        totalExpenses,
        totalProfit,
        overallProfitMargin,
        outletMetrics,
        period: { startDate, endDate },
      };

      LoggingService.info('Consolidated financial report generated:', consolidatedReport as any);
      return consolidatedReport;
    } catch (error) {
      LoggingService.error('Failed to generate consolidated financial report:', error as any);
      throw new AppError('Failed to generate consolidated financial report');
    }
  }

  static async calculateCostAllocation(startDate: string, endDate: string, allocationMethod: 'revenue' | 'orders' | 'custom' = 'revenue'): Promise<CostAllocation[]> {
    try {
      const outlets = await this.getOutlets();
      const costAllocations: CostAllocation[] = [];

      // Get shared costs (costs without outletId)
      const sharedExpenses = await this.getExpenses({ startDate, endDate });
      const totalSharedCosts = sharedExpenses
        .filter(expense => !expense.outletId)
        .reduce((sum, expense) => sum + expense.amount, 0);

      // Calculate allocation factors
      const allocationFactors = await this.calculateAllocationFactors(outlets, allocationMethod, startDate, endDate);

      for (const outlet of outlets) {
        const directCosts = await this.getExpenses({ outletId: outlet.id, startDate, endDate })
          .then(expenses => expenses.reduce((sum, expense) => sum + expense.amount, 0));

        const allocationFactor = allocationFactors[outlet.id] || 0;
        const allocatedCosts = totalSharedCosts * allocationFactor;

        costAllocations.push({
          outletId: outlet.id,
          outletName: outlet.name,
          directCosts,
          allocatedCosts,
          totalCosts: directCosts + allocatedCosts,
          allocationMethod,
          period: { startDate, endDate },
        });
      }

      LoggingService.info('Cost allocation calculated:', costAllocations as any);
      return costAllocations;
    } catch (error) {
      LoggingService.error('Failed to calculate cost allocation:', error as any);
      throw new AppError('Failed to calculate cost allocation');
    }
  }

  static async generateProfitabilityAnalysis(startDate: string, endDate: string): Promise<ProfitabilityAnalysis[]> {
    try {
      const outlets = await this.getOutlets();
      const profitabilityAnalysis: ProfitabilityAnalysis[] = [];

      // This would integrate with your order data to get garment types
      const garmentTypes = ['shirt', 'pants', 'dress', 'suit', 'other'];

      for (const outlet of outlets) {
        for (const garmentType of garmentTypes) {
          const revenue = await this.calculateOutletGarmentRevenue(outlet.id, garmentType, startDate, endDate);
          const costs = await this.calculateOutletGarmentCosts(outlet.id, garmentType, startDate, endDate);
          const profit = revenue - costs;
          const profitMargin = revenue > 0 ? (profit / revenue) * 100 : 0;
          const orderCount = await this.getOutletGarmentOrderCount(outlet.id, garmentType, startDate, endDate);

          if (orderCount > 0) {
            profitabilityAnalysis.push({
              outletId: outlet.id,
              outletName: outlet.name,
              garmentType,
              revenue,
              costs,
              profit,
              profitMargin,
              orderCount,
              period: { startDate, endDate },
            });
          }
        }
      }

      LoggingService.info('Profitability analysis generated:', profitabilityAnalysis as any);
      return profitabilityAnalysis;
    } catch (error) {
      LoggingService.error('Failed to generate profitability analysis:', error as any);
      throw new AppError('Failed to generate profitability analysis');
    }
  }

  /**
   * Financial Reports (existing methods with outlet support)
   */
  static async generateProfitLossStatement(startDate: string, endDate: string, outletId?: string): Promise<ProfitLossData> {
    try {
      if (outletId) {
        return await this.generateOutletProfitLossStatement(outletId, startDate, endDate);
      }

      // Get revenue data (would integrate with sales/orders)
      const revenue = await this.calculateRevenue(startDate, endDate);
      
      // Get expenses
      const expenses = await this.getExpenses({ startDate, endDate });
      const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);

      const grossProfit = revenue - totalExpenses;
      const netProfit = grossProfit; // Simplified - would include taxes, etc.
      const profitMargin = revenue > 0 ? (netProfit / revenue) * 100 : 0;

      const profitLoss: ProfitLossData = {
        revenue,
        expenses: totalExpenses,
        grossProfit,
        netProfit,
        profitMargin,
        period: { startDate, endDate },
      };

      LoggingService.info('P&L statement generated:', profitLoss as any);
      return profitLoss;
    } catch (error) {
      LoggingService.error('Failed to generate P&L statement:', error as any);
      throw new AppError('Failed to generate profit & loss statement');
    }
  }

  static async getPaymentMethodAnalytics(startDate: string, endDate: string, outletId?: string): Promise<PaymentAnalytics> {
    try {
      // This would integrate with your order/payment data
      // For now, return placeholder data
      const analytics: PaymentAnalytics = {
        totalTransactions: 0,
        totalAmount: 0,
        outletId,
        paymentMethods: {
          cash: { count: 0, amount: 0, percentage: 0 },
          card: { count: 0, amount: 0, percentage: 0 },
          digital: { count: 0, amount: 0, percentage: 0 },
        },
        trends: [],
      };

      LoggingService.info('Payment analytics generated:', analytics as any);
      return analytics;
    } catch (error) {
      LoggingService.error('Failed to get payment method analytics', 'FINANCIAL', error as Error);
      throw new Error('Failed to generate payment analytics');
    }
  }

  static async getTaxSummary(startDate: string, endDate: string, outletId?: string): Promise<TaxSummary> {
    try {
      const revenue = outletId 
        ? await this.calculateOutletRevenue(outletId, startDate, endDate)
        : await this.calculateRevenue(startDate, endDate);
      const taxRate = 0.08; // Would get from settings
      const totalTaxCollected = revenue * taxRate;

      const taxSummary: TaxSummary = {
        totalTaxableAmount: revenue,
        totalTaxCollected,
        taxRate,
        outletId,
        period: { startDate, endDate },
        breakdown: {
          sales: {
            amount: revenue,
            tax: totalTaxCollected,
          },
        },
      };

      LoggingService.info('Tax summary generated:', taxSummary as any);
      return taxSummary;
    } catch (error) {
      LoggingService.error('Failed to get tax summary:', error as any);
      throw new AppError('Failed to generate tax summary');
    }
  }

  /**
   * Helper Methods for Multi-Outlet Support
   */
  private static async getOutlets(): Promise<Array<{ id: string; name: string }>> {
    try {
      // This would integrate with OutletService
      // For now, return placeholder data
      return [
        { id: 'outlet-1', name: 'Main Store' },
        { id: 'outlet-2', name: 'Downtown Branch' },
        { id: 'outlet-3', name: 'Mall Location' },
      ];
    } catch (error) {
      LoggingService.error('Failed to get outlets:', error as any);
      return [];
    }
  }

  private static async calculateOutletRevenue(outletId: string, startDate: string, endDate: string): Promise<number> {
    try {
      // This would integrate with your order/sales data for specific outlet
      // For now, return a placeholder calculation
      return Math.random() * 10000; // Placeholder
    } catch (error) {
      LoggingService.error('Failed to calculate outlet revenue:', error as any);
      return 0;
    }
  }

  private static async calculateOutletGarmentRevenue(outletId: string, garmentType: string, startDate: string, endDate: string): Promise<number> {
    try {
      // This would integrate with your order data for specific outlet and garment type
      return Math.random() * 5000; // Placeholder
    } catch (error) {
      LoggingService.error('Failed to calculate outlet garment revenue:', error as any);
      return 0;
    }
  }

  private static async calculateOutletGarmentCosts(outletId: string, garmentType: string, startDate: string, endDate: string): Promise<number> {
    try {
      // This would integrate with your cost data for specific outlet and garment type
      return Math.random() * 3000; // Placeholder
    } catch (error) {
      LoggingService.error('Failed to calculate outlet garment costs:', error as any);
      return 0;
    }
  }

  private static async getOutletOrderCount(outletId: string, startDate: string, endDate: string): Promise<number> {
    try {
      // This would integrate with your order data
      return Math.floor(Math.random() * 100); // Placeholder
    } catch (error) {
      LoggingService.error('Failed to get outlet order count:', error as any);
      return 0;
    }
  }

  private static async getOutletGarmentOrderCount(outletId: string, garmentType: string, startDate: string, endDate: string): Promise<number> {
    try {
      // This would integrate with your order data
      return Math.floor(Math.random() * 50); // Placeholder
    } catch (error) {
      LoggingService.error('Failed to get outlet garment order count:', error as any);
      return 0;
    }
  }

  private static async calculateAllocationFactors(outlets: Array<{ id: string; name: string }>, method: 'revenue' | 'orders' | 'custom', startDate: string, endDate: string): Promise<Record<string, number>> {
    try {
      const factors: Record<string, number> = {};
      let total = 0;

      if (method === 'revenue') {
        for (const outlet of outlets) {
          const revenue = await this.calculateOutletRevenue(outlet.id, startDate, endDate);
          factors[outlet.id] = revenue;
          total += revenue;
        }
      } else if (method === 'orders') {
        for (const outlet of outlets) {
          const orders = await this.getOutletOrderCount(outlet.id, startDate, endDate);
          factors[outlet.id] = orders;
          total += orders;
        }
      }

      // Normalize factors
      if (total > 0) {
        for (const outletId in factors) {
          factors[outletId] = factors[outletId] / total;
        }
      }

      return factors;
    } catch (error) {
      LoggingService.error('Failed to calculate allocation factors:', error as any);
      return {};
    }
  }

  /**
   * Helper Methods (existing)
   */
  private static async calculateRevenue(startDate: string, endDate: string): Promise<number> {
    try {
      // This would integrate with your order/sales data
      // For now, return a placeholder calculation
      return 0;
    } catch (error) {
      LoggingService.error('Failed to calculate revenue:', error as any);
      return 0;
    }
  }

  /**
   * Settings Management
   */
  static async getFinancialSettings(): Promise<Record<string, any>> {
    try {
      return await StorageService.get(this.STORAGE_KEYS.FINANCIAL_SETTINGS) || {};
    } catch (error) {
      LoggingService.error('Failed to get financial settings:', error as any);
      return {};
    }
  }

  static async updateFinancialSettings(settings: Record<string, any>): Promise<void> {
    try {
      const currentSettings = await this.getFinancialSettings();
      const updatedSettings = { ...currentSettings, ...settings };
      await StorageService.set(this.STORAGE_KEYS.FINANCIAL_SETTINGS, updatedSettings);
      LoggingService.info('Financial settings updated:', updatedSettings as any);
    } catch (error) {
      LoggingService.error('Failed to update financial settings:', error as any);
      throw new AppError('Failed to update financial settings');
    }
  }

  /**
   * Data Export/Import
   */
  static async exportFinancialData(): Promise<{
    expenses: Expense[];
    reconciliations: CashReconciliation[];
    settings: Record<string, any>;
  }> {
    try {
      const [expenses, reconciliations, settings] = await Promise.all([
        this.getExpenses(),
        this.getCashReconciliations(),
        this.getFinancialSettings(),
      ]);

      return { expenses, reconciliations, settings };
    } catch (error) {
      LoggingService.error('Failed to export financial data:', error as any);
      throw new AppError('Failed to export financial data');
    }
  }

  static async importFinancialData(data: {
    expenses?: Expense[];
    reconciliations?: CashReconciliation[];
    settings?: Record<string, any>;
  }): Promise<void> {
    try {
      if (data.expenses) {
        await StorageService.set(this.STORAGE_KEYS.EXPENSES, data.expenses);
      }

      if (data.reconciliations) {
        await StorageService.set(this.STORAGE_KEYS.RECONCILIATIONS, data.reconciliations);
      }

      if (data.settings) {
        await StorageService.set(this.STORAGE_KEYS.FINANCIAL_SETTINGS, data.settings);
      }

      LoggingService.info('Financial data imported successfully');
    } catch (error) {
      LoggingService.error('Failed to import financial data:', error as any);
      throw new AppError('Failed to import financial data');
    }
  }
}