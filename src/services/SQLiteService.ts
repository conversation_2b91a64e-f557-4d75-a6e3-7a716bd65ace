/**
 * Optimized SQLite Service for TailorZa
 * Uses modular database architecture for better maintainability and performance
 */

import { 
  Product, 
  Order, 
  Customer, 
  Outlet,
  Staff
} from '../types';

import { customerSQLiteService } from './database/CustomerSQLiteService';
import { databaseManager } from './database/DatabaseManager';

import { orderSQLiteService } from './database/OrderSQLiteService';
import { productSQLiteService } from './database/ProductSQLiteService';
import { staffSQLiteService } from './database/StaffSQLiteService';
import LoggingService from './LoggingService';


// Legacy interface for backward compatibility
interface DatabaseFilters {
  isActive?: boolean;
  category?: string;
  search?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
}

class SQLiteService {
  private isInitialized: boolean = false;

  /**
   * Initialize the database system
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await databaseManager.initialize();
      this.isInitialized = true;
      LoggingService.info('SQLiteService initialized successfully', 'SQLITE_SERVICE');
    } catch (error) {
      LoggingService.error('Failed to initialize SQLiteService', 'SQLITE_SERVICE', error as Error);
      throw error;
    }
  }

  // ============================================================================
  // CUSTOMER OPERATIONS (Delegated to CustomerSQLiteService)
  // ============================================================================

  async getCustomers(): Promise<Customer[]> {
    await this.ensureInitialized();
    return customerSQLiteService.getCustomers();
  }

  async getCustomerById(id: string): Promise<Customer | null> {
    await this.ensureInitialized();
    return customerSQLiteService.getCustomerById(id);
  }

  async saveCustomer(customer: Partial<Customer> & { name: string }): Promise<Customer> {
    await this.ensureInitialized();
    return customerSQLiteService.saveCustomer(customer);
  }

  async deleteCustomer(id: string): Promise<void> {
    await this.ensureInitialized();
    return customerSQLiteService.deleteCustomer(id);
  }

  // ============================================================================
  // PRODUCT OPERATIONS (Delegated to ProductSQLiteService)
  // ============================================================================

  async getProducts(filters: DatabaseFilters = {}): Promise<Product[]> {
    await this.ensureInitialized();
    return productSQLiteService.getProducts({
      isActive: filters.isActive,
      category: filters.category as any,
      search: filters.search
    });
  }

  async getProductById(id: string): Promise<Product | null> {
    await this.ensureInitialized();
    return productSQLiteService.getProductById(id);
  }

  async saveProduct(product: Partial<Product> & { name: string; price: number }): Promise<Product> {
    await this.ensureInitialized();
    return productSQLiteService.saveProduct(product);
  }

  async deleteProduct(id: string): Promise<void> {
    await this.ensureInitialized();
    return productSQLiteService.deleteProduct(id);
  }

  // ============================================================================
  // ORDER OPERATIONS (Delegated to OrderSQLiteService)
  // ============================================================================

  async getOrders(filters: DatabaseFilters = {}): Promise<Order[]> {
    await this.ensureInitialized();
    return orderSQLiteService.getOrders({
      status: filters.status as any,
      search: filters.search,
      dateFrom: filters.dateFrom,
      dateTo: filters.dateTo
    });
  }

  async getOrderById(id: string): Promise<Order | null> {
    await this.ensureInitialized();
    return orderSQLiteService.getOrderById(id);
  }

  async saveOrder(order: Partial<Order> & { id: string; customerName: string; total: number }): Promise<Order> {
    await this.ensureInitialized();
    return orderSQLiteService.saveOrder(order);
  }

  async deleteOrder(id: string): Promise<void> {
    await this.ensureInitialized();
    return orderSQLiteService.deleteOrder(id);
  }

  // ============================================================================
  // TAILOR SHOP OPERATIONS (Legacy - To be migrated to respective services)
  // ============================================================================

  // Outlet operations
  async getOutlets(filters?: { isActive?: boolean; managerId?: string; search?: string }): Promise<Outlet[]> {
    await this.ensureInitialized();
    // TODO: Implement OutletSQLiteService and delegate here
    LoggingService.warn('Outlet operations not yet migrated to modular architecture', 'SQLITE_SERVICE');
    return [];
  }

  async getOutletById(id: string): Promise<Outlet | null> {
    await this.ensureInitialized();
    // TODO: Implement OutletSQLiteService and delegate here
    LoggingService.warn('Outlet operations not yet migrated to modular architecture', 'SQLITE_SERVICE');
    return null;
  }





  // Staff operations
  async getStaff(filters?: {
    outletId?: string;
    role?: any;
    isActive?: boolean;
    skill?: string;
    availability?: boolean;
    search?: string;
    limit?: number;
    offset?: number;
  }): Promise<Staff[]> {
    await this.ensureInitialized();
    return staffSQLiteService.getStaff(filters);
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /**
   * Ensure the service is initialized
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
  }

  /**
   * Get database statistics
   */
  async getDatabaseStats(): Promise<{
    totalServices: number;
    activeServices: number;
    serviceNames: string[];
  }> {
    await this.ensureInitialized();
    return databaseManager.getDatabaseStats();
  }

  /**
   * Validate database integrity
   */
  async validateDatabaseIntegrity(): Promise<{
    isValid: boolean;
    issues: string[];
    serviceResults: { [serviceName: string]: { isValid: boolean; issues: string[] } };
  }> {
    await this.ensureInitialized();
    return databaseManager.validateIntegrity();
  }

  /**
   * Close all database connections
   */
  async close(): Promise<void> {
    if (this.isInitialized) {
      await databaseManager.close();
      this.isInitialized = false;
    }
  }

  /**
   * Check if service is ready
   */
  isReady(): boolean {
    return this.isInitialized && databaseManager.isReady();
  }

  // ============================================================================
  // MIGRATION METHODS (For backward compatibility)
  // ============================================================================

  /**
   * Migrate from bakery to tailor shop (legacy method)
   */
  async migrateBakeryToTailorShop(): Promise<void> {
    await this.ensureInitialized();
    LoggingService.warn('Migration method called - consider using new modular architecture', 'SQLITE_SERVICE');
    // TODO: Implement migration logic using new services
  }

  /**
   * Clear all data (legacy method)
   */
  async clearAllData(): Promise<void> {
    await this.ensureInitialized();
    LoggingService.warn('Clear data method called - consider using new modular architecture', 'SQLITE_SERVICE');
    // TODO: Implement clear logic using new services
  }

  /**
   * Initialize tailor shop data (legacy method)
   */
  async initializeTailorShopData(): Promise<void> {
    await this.ensureInitialized();
    LoggingService.warn('Initialize tailor shop data method called - consider using new modular architecture', 'SQLITE_SERVICE');
    // TODO: Implement initialization logic using new services
  }
}

// Export singleton instance
export default new SQLiteService();