import React from 'react';

import { AppError } from '../utils/errorHandler';

import LoggingService from './LoggingService';
import { StorageService } from './storageService';

// Notification interfaces
interface NotificationTemplate {
  id: string;
  name: string;
  type: 'sms' | 'email' | 'push';
  subject?: string;
  message: string;
  variables: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface NotificationLog {
  id: string;
  customerId: string;
  customerName: string;
  customerPhone?: string;
  customerEmail?: string;
  type: 'sms' | 'email' | 'push';
  templateId: string;
  templateName: string;
  message: string;
  status: 'pending' | 'sent' | 'failed' | 'delivered';
  orderId?: string;
  appointmentId?: string;
  scheduledFor?: string;
  sentAt?: string;
  deliveredAt?: string;
  errorMessage?: string;
  createdAt: string;
}

interface CustomerNotificationPreferences {
  customerId: string;
  customerName: string;
  smsEnabled: boolean;
  emailEnabled: boolean;
  pushEnabled: boolean;
  orderUpdates: boolean;
  appointmentReminders: boolean;
  paymentReminders: boolean;
  pickupReminders: boolean;
  marketingMessages: boolean;
  createdAt: string;
  updatedAt: string;
}

interface AppointmentReminder {
  id: string;
  customerId: string;
  customerName: string;
  customerPhone?: string;
  customerEmail?: string;
  appointmentId: string;
  appointmentDate: string;
  appointmentTime: string;
  garmentType: string;
  outletId: string;
  outletName: string;
  reminderType: '24h' | '2h' | '30min';
  status: 'pending' | 'sent' | 'cancelled';
  scheduledFor: string;
  sentAt?: string;
  createdAt: string;
}

interface PaymentReminder {
  id: string;
  customerId: string;
  customerName: string;
  customerPhone?: string;
  customerEmail?: string;
  orderId: string;
  orderNumber: string;
  amount: number;
  dueDate: string;
  reminderType: 'due' | 'overdue' | 'final';
  status: 'pending' | 'sent' | 'cancelled';
  scheduledFor: string;
  sentAt?: string;
  createdAt: string;
}

interface PickupReminder {
  id: string;
  customerId: string;
  customerName: string;
  customerPhone?: string;
  customerEmail?: string;
  orderId: string;
  orderNumber: string;
  garmentType: string;
  outletId: string;
  outletName: string;
  readyDate: string;
  reminderType: 'ready' | 'reminder' | 'final';
  status: 'pending' | 'sent' | 'cancelled';
  scheduledFor: string;
  sentAt?: string;
  createdAt: string;
}

interface NotificationSettings {
  smsEnabled: boolean;
  emailEnabled: boolean;
  pushEnabled: boolean;
  smsProvider?: string;
  emailProvider?: string;
  smsApiKey?: string;
  emailApiKey?: string;
  defaultFromEmail?: string;
  defaultFromName?: string;
  businessHours: {
    start: string;
    end: string;
    timezone: string;
  };
  reminderSettings: {
    appointmentReminders: boolean;
    paymentReminders: boolean;
    pickupReminders: boolean;
    appointmentAdvanceNotice: number; // hours
    paymentAdvanceNotice: number; // days
    pickupAdvanceNotice: number; // hours
  };
}

/**
 * Notification Service for managing customer communication
 */
export class NotificationService {
  static STORAGE_KEYS = {
    TEMPLATES: 'notification_templates',
    LOGS: 'notification_logs',
    PREFERENCES: 'customer_notification_preferences',
    APPOINTMENT_REMINDERS: 'appointment_reminders',
    PAYMENT_REMINDERS: 'payment_reminders',
    PICKUP_REMINDERS: 'pickup_reminders',
    SETTINGS: 'notification_settings',
  };

  /**
   * Template Management
   */
  static async createTemplate(template: Omit<NotificationTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<NotificationTemplate> {
    try {
      const templates = await this.getTemplates();
      const newTemplate: NotificationTemplate = {
        id: Date.now().toString(),
        ...template,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      templates.push(newTemplate);
      await StorageService.set(this.STORAGE_KEYS.TEMPLATES, templates);

      LoggingService.info('Notification template created:', 'NOTIFICATION', newTemplate);
      return newTemplate;
    } catch (error) {
      LoggingService.error('Failed to create notification template:', 'NOTIFICATION', error instanceof Error ? error : undefined);
      throw new AppError('Failed to create notification template');
    }
  }

  static async getTemplates(): Promise<NotificationTemplate[]> {
    try {
      return await StorageService.get(this.STORAGE_KEYS.TEMPLATES) || [];
    } catch (error) {
      LoggingService.error('Failed to get notification templates:', 'NOTIFICATION', error instanceof Error ? error : undefined);
      return [];
    }
  }

  static async updateTemplate(id: string, updates: Partial<NotificationTemplate>): Promise<NotificationTemplate> {
    try {
      const templates = await this.getTemplates();
      const index = templates.findIndex(t => t.id === id);

      if (index === -1) {
        throw new AppError('Template not found');
      }

      const updatedTemplate: NotificationTemplate = {
        ...templates[index],
        ...updates,
        updatedAt: new Date().toISOString(),
      };

      templates[index] = updatedTemplate;
      await StorageService.set(this.STORAGE_KEYS.TEMPLATES, templates);

      LoggingService.info('Notification template updated:', 'NOTIFICATION', updatedTemplate);
      return updatedTemplate;
    } catch (error) {
      LoggingService.error('Failed to update notification template:', 'NOTIFICATION', error instanceof Error ? error : undefined);
      throw new AppError('Failed to update notification template');
    }
  }

  static async deleteTemplate(id: string): Promise<void> {
    try {
      const templates = await this.getTemplates();
      const filtered = templates.filter(t => t.id !== id);

      if (filtered.length === templates.length) {
        throw new AppError('Template not found');
      }

      await StorageService.set(this.STORAGE_KEYS.TEMPLATES, filtered);
      LoggingService.info('Notification template deleted:', 'NOTIFICATION', { id });
    } catch (error) {
      LoggingService.error('Failed to delete notification template:', 'NOTIFICATION', error instanceof Error ? error : undefined);
      throw new AppError('Failed to delete notification template');
    }
  }

  /**
   * Customer Notification Preferences
   */
  static async setCustomerPreferences(preferences: Omit<CustomerNotificationPreferences, 'createdAt' | 'updatedAt'>): Promise<CustomerNotificationPreferences> {
    try {
      const allPreferences = await this.getCustomerPreferences();
      const newPreferences: CustomerNotificationPreferences = {
        ...preferences,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const index = allPreferences.findIndex(p => p.customerId === preferences.customerId);
      if (index !== -1) {
        allPreferences[index] = { ...allPreferences[index], ...newPreferences, updatedAt: new Date().toISOString() };
      } else {
        allPreferences.push(newPreferences);
      }

      await StorageService.set(this.STORAGE_KEYS.PREFERENCES, allPreferences);
      LoggingService.info('Customer notification preferences updated:', 'NOTIFICATION', newPreferences);
      return newPreferences;
    } catch (error) {
      LoggingService.error('Failed to set customer preferences:', 'NOTIFICATION', error instanceof Error ? error : undefined);
      throw new AppError('Failed to set customer preferences');
    }
  }

  static async getCustomerPreferences(customerId?: string): Promise<CustomerNotificationPreferences[]> {
    try {
      const preferences: CustomerNotificationPreferences[] = await StorageService.get(this.STORAGE_KEYS.PREFERENCES) || [];
      
      if (customerId) {
        return preferences.filter(p => p.customerId === customerId);
      }
      
      return preferences;
    } catch (error) {
      LoggingService.error('Failed to get customer preferences:', 'NOTIFICATION', error instanceof Error ? error : undefined);
      return [];
    }
  }

  /**
   * Order Status Notifications
   */
  static async sendOrderStatusUpdate(
    customerId: string,
    customerName: string,
    customerPhone: string,
    customerEmail: string,
    orderId: string,
    orderNumber: string,
    status: string,
    outletId: string,
    outletName: string
  ): Promise<NotificationLog> {
    try {
      const preferences = await this.getCustomerPreferences(customerId);
      const customerPrefs = preferences.find(p => p.customerId === customerId);

      if (!customerPrefs?.orderUpdates) {
        throw new AppError('Customer has disabled order updates');
      }

      const template = await this.getTemplateByType('order_status_update');
      if (!template) {
        throw new AppError('Order status update template not found');
      }

      const message = this.replaceTemplateVariables(template.message, {
        customerName,
        orderNumber,
        status,
        outletName,
      });

      const notificationLog: NotificationLog = {
        id: Date.now().toString(),
        customerId,
        customerName,
        customerPhone,
        customerEmail,
        type: 'sms', // Default to SMS for order updates
        templateId: template.id,
        templateName: template.name,
        message,
        status: 'pending',
        orderId,
        createdAt: new Date().toISOString(),
      };

      // Send notification based on customer preferences
      if (customerPrefs.smsEnabled && customerPhone) {
        await this.sendSMS(customerPhone, message);
        notificationLog.status = 'sent';
        notificationLog.sentAt = new Date().toISOString();
      } else if (customerPrefs.emailEnabled && customerEmail) {
        await this.sendEmail(customerEmail, template.subject || 'Order Status Update', message);
        notificationLog.status = 'sent';
        notificationLog.sentAt = new Date().toISOString();
      }

      await this.logNotification(notificationLog);
      LoggingService.info('Order status notification sent:', 'NOTIFICATION', notificationLog);
      return notificationLog;
    } catch (error) {
      LoggingService.error('Failed to send order status update:', 'NOTIFICATION', error instanceof Error ? error : undefined);
      throw new AppError('Failed to send order status update');
    }
  }

  /**
   * Appointment Reminders
   */
  static async scheduleAppointmentReminder(
    customerId: string,
    customerName: string,
    customerPhone: string,
    customerEmail: string,
    appointmentId: string,
    appointmentDate: string,
    appointmentTime: string,
    garmentType: string,
    outletId: string,
    outletName: string,
    reminderType: '24h' | '2h' | '30min' = '24h'
  ): Promise<AppointmentReminder> {
    try {
      const preferences = await this.getCustomerPreferences(customerId);
      const customerPrefs = preferences.find(p => p.customerId === customerId);

      if (!customerPrefs?.appointmentReminders) {
        throw new AppError('Customer has disabled appointment reminders');
      }

      const appointmentDateTime = new Date(`${appointmentDate}T${appointmentTime}`);
      const scheduledFor = this.calculateReminderTime(appointmentDateTime, reminderType);

      const reminder: AppointmentReminder = {
        id: Date.now().toString(),
        customerId,
        customerName,
        customerPhone,
        customerEmail,
        appointmentId,
        appointmentDate,
        appointmentTime,
        garmentType,
        outletId,
        outletName,
        reminderType,
        status: 'pending',
        scheduledFor: scheduledFor.toISOString(),
        createdAt: new Date().toISOString(),
      };

      const reminders = await this.getAppointmentReminders();
      reminders.push(reminder);
      await StorageService.set(this.STORAGE_KEYS.APPOINTMENT_REMINDERS, reminders);

      LoggingService.info('Appointment reminder scheduled:', 'NOTIFICATION', reminder);
      return reminder;
    } catch (error) {
      LoggingService.error('Failed to schedule appointment reminder:', 'NOTIFICATION', error instanceof Error ? error : undefined);
      throw new AppError('Failed to schedule appointment reminder');
    }
  }

  static async getAppointmentReminders(): Promise<AppointmentReminder[]> {
    try {
      return await StorageService.get(this.STORAGE_KEYS.APPOINTMENT_REMINDERS) || [];
    } catch (error) {
      LoggingService.error('Failed to get appointment reminders:', 'NOTIFICATION', error instanceof Error ? error : undefined);
      return [];
    }
  }

  /**
   * Payment Reminders
   */
  static async schedulePaymentReminder(
    customerId: string,
    customerName: string,
    customerPhone: string,
    customerEmail: string,
    orderId: string,
    orderNumber: string,
    amount: number,
    dueDate: string,
    reminderType: 'due' | 'overdue' | 'final' = 'due'
  ): Promise<PaymentReminder> {
    try {
      const preferences = await this.getCustomerPreferences(customerId);
      const customerPrefs = preferences.find(p => p.customerId === customerId);

      if (!customerPrefs?.paymentReminders) {
        throw new AppError('Customer has disabled payment reminders');
      }

      const dueDateTime = new Date(dueDate);
      const scheduledFor = this.calculatePaymentReminderTime(dueDateTime, reminderType);

      const reminder: PaymentReminder = {
        id: Date.now().toString(),
        customerId,
        customerName,
        customerPhone,
        customerEmail,
        orderId,
        orderNumber,
        amount,
        dueDate,
        reminderType,
        status: 'pending',
        scheduledFor: scheduledFor.toISOString(),
        createdAt: new Date().toISOString(),
      };

      const reminders = await this.getPaymentReminders();
      reminders.push(reminder);
      await StorageService.set(this.STORAGE_KEYS.PAYMENT_REMINDERS, reminders);

      LoggingService.info('Payment reminder scheduled:', 'NOTIFICATION', reminder);
      return reminder;
    } catch (error) {
      LoggingService.error('Failed to schedule payment reminder:', 'NOTIFICATION', error instanceof Error ? error : undefined);
      throw new AppError('Failed to schedule payment reminder');
    }
  }

  static async getPaymentReminders(): Promise<PaymentReminder[]> {
    try {
      return await StorageService.get(this.STORAGE_KEYS.PAYMENT_REMINDERS) || [];
      } catch (error) {
      LoggingService.error('Failed to get payment reminders:', 'NOTIFICATION', error instanceof Error ? error : undefined);
      return [];
    }
  }

  /**
   * Pickup Reminders
   */
  static async schedulePickupReminder(
    customerId: string,
    customerName: string,
    customerPhone: string,
    customerEmail: string,
    orderId: string,
    orderNumber: string,
    garmentType: string,
    outletId: string,
    outletName: string,
    readyDate: string,
    reminderType: 'ready' | 'reminder' | 'final' = 'ready'
  ): Promise<PickupReminder> {
    try {
      const preferences = await this.getCustomerPreferences(customerId);
      const customerPrefs = preferences.find(p => p.customerId === customerId);

      if (!customerPrefs?.pickupReminders) {
        throw new AppError('Customer has disabled pickup reminders');
      }

      const readyDateTime = new Date(readyDate);
      const scheduledFor = this.calculatePickupReminderTime(readyDateTime, reminderType);

      const reminder: PickupReminder = {
        id: Date.now().toString(),
        customerId,
        customerName,
        customerPhone,
        customerEmail,
        orderId,
        orderNumber,
        garmentType,
        outletId,
        outletName,
        readyDate,
        reminderType,
        status: 'pending',
        scheduledFor: scheduledFor.toISOString(),
        createdAt: new Date().toISOString(),
      };

      const reminders = await this.getPickupReminders();
      reminders.push(reminder);
      await StorageService.set(this.STORAGE_KEYS.PICKUP_REMINDERS, reminders);

      LoggingService.info('Pickup reminder scheduled:', 'NOTIFICATION', reminder);
      return reminder;
    } catch (error) {
      LoggingService.error('Failed to schedule pickup reminder:', 'NOTIFICATION', error instanceof Error ? error : undefined);
      throw new AppError('Failed to schedule pickup reminder');
    }
  }

  static async getPickupReminders(): Promise<PickupReminder[]> {
    try {
      return await StorageService.get(this.STORAGE_KEYS.PICKUP_REMINDERS) || [];
    } catch (error) {
      LoggingService.error('Failed to get pickup reminders:', 'NOTIFICATION', error instanceof Error ? error : undefined);
      return [];
    }
  }

  /**
   * Customer Feedback Collection
   */
  static async sendFeedbackRequest(
    customerId: string,
    customerName: string,
    customerPhone: string,
    customerEmail: string,
    orderId: string,
    orderNumber: string
  ): Promise<NotificationLog> {
    try {
      const preferences = await this.getCustomerPreferences(customerId);
      const customerPrefs = preferences.find(p => p.customerId === customerId);

      if (!customerPrefs?.marketingMessages) {
        throw new AppError('Customer has disabled marketing messages');
      }

      const template = await this.getTemplateByType('feedback_request');
      if (!template) {
        throw new AppError('Feedback request template not found');
      }

      const message = this.replaceTemplateVariables(template.message, {
        customerName,
        orderNumber,
      });

      const notificationLog: NotificationLog = {
        id: Date.now().toString(),
        customerId,
        customerName,
        customerPhone,
        customerEmail,
        type: 'sms',
        templateId: template.id,
        templateName: template.name,
        message,
        status: 'pending',
        orderId,
        createdAt: new Date().toISOString(),
      };

      if (customerPrefs.smsEnabled && customerPhone) {
        await this.sendSMS(customerPhone, message);
        notificationLog.status = 'sent';
        notificationLog.sentAt = new Date().toISOString();
      } else if (customerPrefs.emailEnabled && customerEmail) {
        await this.sendEmail(customerEmail, template.subject || 'We Value Your Feedback', message);
        notificationLog.status = 'sent';
        notificationLog.sentAt = new Date().toISOString();
      }

      await this.logNotification(notificationLog);
      LoggingService.info('Feedback request sent:', 'NOTIFICATION', notificationLog);
      return notificationLog;
    } catch (error) {
      LoggingService.error('Failed to send feedback request:', 'NOTIFICATION', error instanceof Error ? error : undefined);
      throw new AppError('Failed to send feedback request');
    }
  }

  /**
   * Helper Methods
   */
  private static async getTemplateByType(type: string): Promise<NotificationTemplate | null> {
    try {
      const templates = await this.getTemplates();
      return templates.find(t => t.name.toLowerCase().includes(type.toLowerCase()) && t.isActive) || null;
    } catch (error) {
      LoggingService.error('Failed to get template by type:', 'NOTIFICATION', error instanceof Error ? error : undefined);
      return null;
    }
  }

  private static replaceTemplateVariables(message: string, variables: Record<string, string>): string {
    let processedMessage = message;
    Object.entries(variables).forEach(([key, value]) => {
      processedMessage = processedMessage.replace(new RegExp(`{{${key}}}`, 'g'), value);
    });
    return processedMessage;
  }

  private static calculateReminderTime(appointmentDateTime: Date, reminderType: '24h' | '2h' | '30min'): Date {
    const reminderTime = new Date(appointmentDateTime);
    switch (reminderType) {
      case '24h':
        reminderTime.setHours(reminderTime.getHours() - 24);
        break;
      case '2h':
        reminderTime.setHours(reminderTime.getHours() - 2);
        break;
      case '30min':
        reminderTime.setMinutes(reminderTime.getMinutes() - 30);
        break;
    }
    return reminderTime;
  }

  private static calculatePaymentReminderTime(dueDateTime: Date, reminderType: 'due' | 'overdue' | 'final'): Date {
    const reminderTime = new Date(dueDateTime);
    switch (reminderType) {
      case 'due':
        reminderTime.setDate(reminderTime.getDate() - 1); // 1 day before
        break;
      case 'overdue':
        reminderTime.setDate(reminderTime.getDate() + 1); // 1 day after
        break;
      case 'final':
        reminderTime.setDate(reminderTime.getDate() + 7); // 1 week after
        break;
    }
    return reminderTime;
  }

  private static calculatePickupReminderTime(readyDateTime: Date, reminderType: 'ready' | 'reminder' | 'final'): Date {
    const reminderTime = new Date(readyDateTime);
    switch (reminderType) {
      case 'ready':
        return reminderTime; // Same day
      case 'reminder':
        reminderTime.setDate(reminderTime.getDate() + 3); // 3 days after
        break;
      case 'final':
        reminderTime.setDate(reminderTime.getDate() + 7); // 1 week after
        break;
    }
    return reminderTime;
  }

  private static async sendSMS(phoneNumber: string, message: string): Promise<void> {
    try {
      // This would integrate with an SMS service like Twilio, AWS SNS, etc.
      // For now, just log the SMS
      LoggingService.info('SMS would be sent:', 'NOTIFICATION', { phoneNumber, message });
      
      // Simulate SMS sending
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      LoggingService.error('Failed to send SMS:', 'NOTIFICATION', error instanceof Error ? error : undefined);
      throw new AppError('Failed to send SMS');
    }
  }

  private static async sendEmail(email: string, subject: string, message: string): Promise<void> {
    try {
      // This would integrate with an email service like SendGrid, AWS SES, etc.
      // For now, just log the email
      LoggingService.info('Email would be sent:', 'NOTIFICATION', { email, subject, message });
      
      // Simulate email sending
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      LoggingService.error('Failed to send email:', 'NOTIFICATION', error instanceof Error ? error : undefined);
      throw new AppError('Failed to send email');
    }
  }

  private static async logNotification(notification: NotificationLog): Promise<void> {
    try {
      const logs = await this.getNotificationLogs();
      logs.push(notification);
      await StorageService.set(this.STORAGE_KEYS.LOGS, logs);
    } catch (error) {
      LoggingService.error('Failed to log notification:', 'NOTIFICATION', error instanceof Error ? error : undefined);
    }
  }

  static async getNotificationLogs(): Promise<NotificationLog[]> {
    try {
      return await StorageService.get(this.STORAGE_KEYS.LOGS) || [];
    } catch (error) {
      LoggingService.error('Failed to get notification logs:', 'NOTIFICATION', error instanceof Error ? error : undefined);
      return [];
    }
  }

  /**
   * Settings Management
   */
  static async getNotificationSettings(): Promise<NotificationSettings> {
    try {
      return await StorageService.get(this.STORAGE_KEYS.SETTINGS) || {
        smsEnabled: false,
        emailEnabled: false,
        pushEnabled: false,
        businessHours: {
          start: '09:00',
          end: '18:00',
          timezone: 'UTC',
        },
        reminderSettings: {
          appointmentReminders: true,
          paymentReminders: true,
          pickupReminders: true,
          appointmentAdvanceNotice: 24,
          paymentAdvanceNotice: 1,
          pickupAdvanceNotice: 24,
        },
      };
    } catch (error) {
      LoggingService.error('Failed to get notification settings:', 'NOTIFICATION', error instanceof Error ? error : undefined);
      return {
        smsEnabled: false,
        emailEnabled: false,
        pushEnabled: false,
        businessHours: {
          start: '09:00',
          end: '18:00',
          timezone: 'UTC',
        },
        reminderSettings: {
          appointmentReminders: true,
          paymentReminders: true,
          pickupReminders: true,
          appointmentAdvanceNotice: 24,
          paymentAdvanceNotice: 1,
          pickupAdvanceNotice: 24,
        },
      };
    }
  }

  static async updateNotificationSettings(settings: Partial<NotificationSettings>): Promise<void> {
    try {
      const currentSettings = await this.getNotificationSettings();
      const updatedSettings = { ...currentSettings, ...settings };
      await StorageService.set(this.STORAGE_KEYS.SETTINGS, updatedSettings);
      LoggingService.info('Notification settings updated:', 'NOTIFICATION', updatedSettings);
    } catch (error) {
      LoggingService.error('Failed to update notification settings:', 'NOTIFICATION', error instanceof Error ? error : undefined);
      throw new AppError('Failed to update notification settings');
    }
  }

  /**
   * Seed dummy notifications for development/testing
   */
  static async seedDummyNotifications(): Promise<void> {
    try {
      // console.log('Seeding dummy notifications...');
      
      // Create sample notification templates
      await this.createTemplate({
        name: 'Order Status Update',
        type: 'sms',
        message: 'Hi {{customerName}}, your order {{orderNumber}} status has been updated to {{status}}. Visit {{outletName}} for more details.',
        variables: ['customerName', 'orderNumber', 'status', 'outletName'],
        isActive: true,
      });

      await this.createTemplate({
        name: 'Appointment Reminder',
        type: 'sms',
        message: 'Hi {{customerName}}, reminder for your fitting appointment on {{appointmentDate}} at {{appointmentTime}} for {{garmentType}} at {{outletName}}.',
        variables: ['customerName', 'appointmentDate', 'appointmentTime', 'garmentType', 'outletName'],
        isActive: true,
      });

      await this.createTemplate({
        name: 'Payment Reminder',
        type: 'email',
        subject: 'Payment Reminder - Order {{orderNumber}}',
        message: 'Dear {{customerName}}, this is a reminder that payment of {{amount}} is due for order {{orderNumber}}. Please visit {{outletName}} to complete payment.',
        variables: ['customerName', 'orderNumber', 'amount', 'outletName'],
        isActive: true,
      });

      await this.createTemplate({
        name: 'Pickup Reminder',
        type: 'sms',
        message: 'Hi {{customerName}}, your order {{orderNumber}} is ready for pickup at {{outletName}}. Please collect within 7 days.',
        variables: ['customerName', 'orderNumber', 'outletName'],
        isActive: true,
      });

      await this.createTemplate({
        name: 'Feedback Request',
        type: 'email',
        subject: 'We value your feedback - Order {{orderNumber}}',
        message: 'Dear {{customerName}}, thank you for choosing {{outletName}}. We would love to hear about your experience with order {{orderNumber}}. Please take a moment to share your feedback.',
        variables: ['customerName', 'orderNumber', 'outletName'],
        isActive: true,
      });

      // Create sample customer preferences
      await this.setCustomerPreferences({
        customerId: 'customer-1',
        customerName: 'John Smith',
        smsEnabled: true,
        emailEnabled: true,
        pushEnabled: false,
        orderUpdates: true,
        appointmentReminders: true,
        paymentReminders: true,
        pickupReminders: true,
        marketingMessages: false,
      });

      await this.setCustomerPreferences({
        customerId: 'customer-2',
        customerName: 'Sarah Johnson',
        smsEnabled: true,
        emailEnabled: false,
        pushEnabled: false,
        orderUpdates: true,
        appointmentReminders: true,
        paymentReminders: true,
        pickupReminders: true,
        marketingMessages: false,
      });

      // console.log('Dummy notifications seeded successfully!');
    } catch (error) {
      // console.error('Failed to seed dummy notifications:', error);
    }
  }
}

// Export types for external use
export interface Notification {
  id: string;
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  data?: {
    action?: () => void;
    actionType?: string;
    actionText?: string;
  };
}

// Hook for using notifications in components
export const useNotifications = () => {
  const [notifications, setNotifications] = React.useState<Notification[]>([]);
  const [loading, setLoading] = React.useState(true);

  // Load notifications from storage on mount
  React.useEffect(() => {
    loadNotifications();
  }, []);

  const loadNotifications = async () => {
    try {
      setLoading(true);
      const storedNotifications = await StorageService.get('notifications');
      if (storedNotifications) {
        setNotifications(storedNotifications);
      } else {
        // Initialize with some sample notifications if none exist
        const sampleNotifications: Notification[] = [
          {
            id: '1',
            title: 'Order Status Update',
            message: 'Your order #12345 has been completed and is ready for pickup.',
            timestamp: new Date().toISOString(),
            read: false,
            data: {
              actionText: 'View Order',
              actionType: 'view_order'
            }
          },
          {
            id: '2',
            title: 'Payment Reminder',
            message: 'Payment of $150 is due for order #12345. Please visit our outlet to complete payment.',
            timestamp: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
            read: true,
            data: {
              actionText: 'Make Payment',
              actionType: 'make_payment'
            }
          },
          {
            id: '3',
            title: 'Appointment Reminder',
            message: 'You have an appointment tomorrow at 2:00 PM for fitting.',
            timestamp: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
            read: false,
            data: {
              actionText: 'View Appointment',
              actionType: 'view_appointment'
            }
          }
        ];
        setNotifications(sampleNotifications);
        await StorageService.set('notifications', sampleNotifications);
      }
    } catch (error) {
      LoggingService.error('Failed to load notifications', 'NOTIFICATION', error as Error);
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (id: string) => {
    try {
      const updatedNotifications = notifications.map(n => 
        n.id === id ? { ...n, read: true } : n
      );
      setNotifications(updatedNotifications);
      await StorageService.set('notifications', updatedNotifications);
    } catch (error) {
      LoggingService.error('Failed to mark notification as read', 'NOTIFICATION', error as Error);
    }
  };

  const deleteNotification = async (id: string) => {
    try {
      const updatedNotifications = notifications.filter(n => n.id !== id);
      setNotifications(updatedNotifications);
      await StorageService.set('notifications', updatedNotifications);
    } catch (error) {
      LoggingService.error('Failed to delete notification', 'NOTIFICATION', error as Error);
    }
  };

  const clearAll = async () => {
    try {
      setNotifications([]);
      await StorageService.set('notifications', []);
    } catch (error) {
      LoggingService.error('Failed to clear notifications', 'NOTIFICATION', error as Error);
    }
  };

  const addNotification = async (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    try {
      const newNotification: Notification = {
        ...notification,
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        read: false
      };
      const updatedNotifications = [newNotification, ...notifications];
      setNotifications(updatedNotifications);
      await StorageService.set('notifications', updatedNotifications);
    } catch (error) {
      LoggingService.error('Failed to add notification', 'NOTIFICATION', error as Error);
    }
  };

  return {
    notifications,
    loading,
    markAsRead,
    deleteNotification,
    clearAll,
    addNotification,
    refresh: loadNotifications
  };
};

export default NotificationService;