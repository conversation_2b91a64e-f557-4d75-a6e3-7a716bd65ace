/**
 * Database Schema for TailorZa (Simplified)
 * Handles creation and migration of core database tables only
 */

import * as SQLite from 'expo-sqlite';

import LoggingService from '../LoggingService';

export class DatabaseSchema {
  private db: SQLite.SQLiteDatabase | null = null;

  constructor(db: SQLite.SQLiteDatabase) {
    this.db = db;
  }

  /**
   * Initialize all database tables
   */
  async initializeTables(): Promise<void> {
    try {
      LoggingService.info('Initializing database tables', 'DATABASE_SCHEMA');

      // Create tables in dependency order
      await this.createCustomersTable();
      await this.createProductsTable();
      await this.createOrdersTable();
      await this.createOrderItemsTable();
      await this.createOutletsTable();
      await this.createStaffTable();


      await this.createGarmentOrdersTable();
      await this.createMeasurementsTable();

      LoggingService.info('All database tables initialized successfully', 'DATABASE_SCHEMA');
    } catch (error) {
      LoggingService.error('Failed to initialize database tables', 'DATABASE_SCHEMA', error as Error);
      throw error;
    }
  }

  /**
   * Create customers table
   */
  private async createCustomersTable(): Promise<void> {
    const query = `
      CREATE TABLE IF NOT EXISTS customers (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        email TEXT,
        phone TEXT,
        address TEXT,
        totalOrders INTEGER DEFAULT 0,
        totalSpent REAL DEFAULT 0,
        lastOrderDate TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `;
    await this.executeQuery(query);
  }

  /**
   * Create products table
   */
  private async createProductsTable(): Promise<void> {
    const query = `
      CREATE TABLE IF NOT EXISTS products (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        price REAL NOT NULL,
        category TEXT,
        stock INTEGER DEFAULT 0,
        sku TEXT,
        barcode TEXT,
        image TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `;
    await this.executeQuery(query);
  }

  /**
   * Create orders table
   */
  private async createOrdersTable(): Promise<void> {
    const query = `
      CREATE TABLE IF NOT EXISTS orders (
        id TEXT PRIMARY KEY,
        customerName TEXT NOT NULL,
        customer TEXT NOT NULL,
        email TEXT,
        phone TEXT,
        date TEXT NOT NULL,
        time TEXT NOT NULL,
        status TEXT NOT NULL,
        orderType TEXT NOT NULL,
        subtotal REAL NOT NULL,
        tax REAL DEFAULT 0,
        discount REAL DEFAULT 0,
        total REAL NOT NULL,
        notes TEXT,
        image TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `;
    await this.executeQuery(query);
  }

  /**
   * Create order items table
   */
  private async createOrderItemsTable(): Promise<void> {
    const query = `
      CREATE TABLE IF NOT EXISTS order_items (
        id TEXT PRIMARY KEY,
        orderId TEXT,
        productId TEXT NOT NULL,
        productName TEXT NOT NULL,
        quantity INTEGER NOT NULL,
        price REAL NOT NULL,
        total REAL NOT NULL,
        FOREIGN KEY (orderId) REFERENCES orders (id) ON DELETE CASCADE
      )
    `;
    await this.executeQuery(query);
  }

  /**
   * Create outlets table
   */
  private async createOutletsTable(): Promise<void> {
    const query = `
      CREATE TABLE IF NOT EXISTS outlets (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        address TEXT NOT NULL,
        phone TEXT,
        email TEXT,
        managerId TEXT,
        managerName TEXT,
        is_active INTEGER DEFAULT 1,
        operatingHours TEXT,
        settings TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `;
    await this.executeQuery(query);
  }

  /**
   * Create staff table
   */
  private async createStaffTable(): Promise<void> {
    const query = `
      CREATE TABLE IF NOT EXISTS staff (
        id TEXT PRIMARY KEY,
        employeeId TEXT NOT NULL,
        name TEXT NOT NULL,
        email TEXT,
        phone TEXT,
        role TEXT NOT NULL,
        outletId TEXT NOT NULL,
        is_active INTEGER DEFAULT 1,
        profileImage TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (outletId) REFERENCES outlets (id)
      )
    `;
    await this.executeQuery(query);
  }



  /**
   * Create garment orders table
   */
  private async createGarmentOrdersTable(): Promise<void> {
    const query = `
      CREATE TABLE IF NOT EXISTS garment_orders (
        id TEXT PRIMARY KEY,
        customer_id TEXT NOT NULL,
        customer_name TEXT NOT NULL,
        garment_type TEXT NOT NULL,
        measurements TEXT,
        fabric_id TEXT,
        fabric_name TEXT,
        fabric_quantity REAL,
        fabric_cost REAL,
        labor_cost REAL,
        total_amount REAL,
        status TEXT NOT NULL,
        assigned_to TEXT,
        assigned_to_name TEXT,
        delivery_date TEXT,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (customer_id) REFERENCES customers (id)
      )
    `;
    await this.executeQuery(query);
  }

  /**
   * Create measurements table
   */
  private async createMeasurementsTable(): Promise<void> {
    const query = `
      CREATE TABLE IF NOT EXISTS measurements (
        id TEXT PRIMARY KEY,
        customer_id TEXT NOT NULL,
        customer_name TEXT NOT NULL,
        garment_type TEXT NOT NULL,
        measurements TEXT NOT NULL,
        is_active INTEGER DEFAULT 1,
        version INTEGER DEFAULT 1,
        notes TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (customer_id) REFERENCES customers (id)
      )
    `;
    await this.executeQuery(query);
  }

  /**
   * Execute a database query
   */
  private async executeQuery(query: string): Promise<void> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      await this.db.runAsync(query);
    } catch (error) {
      LoggingService.error('Failed to execute query', 'DATABASE_SCHEMA', error as Error);
      throw error;
    }
  }

  /**
   * Get database version
   */
  async getVersion(): Promise<number> {
    try {
      const result = await this.db?.getFirstAsync('PRAGMA user_version') as { user_version: number } | null;
      return result?.user_version || 0;
    } catch (error) {
      LoggingService.error('Failed to get database version', 'DATABASE_SCHEMA', error as Error);
      return 0;
    }
  }

  /**
   * Set database version
   */
  async setVersion(version: number): Promise<void> {
    try {
      await this.db?.runAsync(`PRAGMA user_version = ${version}`);
    } catch (error) {
      LoggingService.error('Failed to set database version', 'DATABASE_SCHEMA', error as Error);
      throw error;
    }
  }

  /**
   * Initialize sample data for testing
   */
  async initializeSampleData(): Promise<void> {
    try {
      LoggingService.info('Initializing sample data', 'DATABASE_SCHEMA');

      // Check if sample data already exists
      const customerCount = await this.db?.getFirstAsync('SELECT COUNT(*) as count FROM customers') as { count: number } | null;
      if (customerCount && customerCount.count > 0) {
        LoggingService.info('Sample data already exists, skipping initialization', 'DATABASE_SCHEMA');
        return;
      }

      // Insert sample customers
      await this.insertSampleCustomers();
      
      // Insert sample products
      await this.insertSampleProducts();
      
      // Insert sample outlets
      await this.insertSampleOutlets();

      LoggingService.info('Sample data initialized successfully', 'DATABASE_SCHEMA');
    } catch (error) {
      LoggingService.error('Failed to initialize sample data', 'DATABASE_SCHEMA', error as Error);
      throw error;
    }
  }

  /**
   * Insert sample customers
   */
  private async insertSampleCustomers(): Promise<void> {
    const customers = [
      {
        id: 'customer_1',
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        address: '123 Main St, City, State',
        totalOrders: 5,
        totalSpent: 1250.00,
        lastOrderDate: new Date().toISOString(),
        isActive: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 'customer_2',
        name: 'Jane Smith',
        email: '<EMAIL>',
        phone: '+0987654321',
        address: '456 Oak Ave, City, State',
        totalOrders: 3,
        totalSpent: 850.00,
        lastOrderDate: new Date().toISOString(),
        isActive: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    for (const customer of customers) {
      await this.db?.runAsync(`
        INSERT INTO customers (id, name, email, phone, address, totalOrders, totalSpent, lastOrderDate, is_active, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        customer.id, customer.name, customer.email, customer.phone, customer.address,
        customer.totalOrders, customer.totalSpent, customer.lastOrderDate, customer.isActive,
        customer.createdAt, customer.updatedAt
      ]);
    }
  }

  /**
   * Insert sample products
   */
  private async insertSampleProducts(): Promise<void> {
    const products = [
      {
        id: 'product_1',
        name: 'Cotton Fabric',
        description: 'High-quality cotton fabric for shirts',
        price: 25.00,
        category: 'Fabric',
        stock: 100,
        sku: 'FAB001',
        barcode: '1234567890123',
        image: null,
        isActive: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 'product_2',
        name: 'Silk Fabric',
        description: 'Premium silk fabric for dresses',
        price: 45.00,
        category: 'Fabric',
        stock: 50,
        sku: 'FAB002',
        barcode: '1234567890124',
        image: null,
        isActive: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    for (const product of products) {
      await this.db?.runAsync(`
        INSERT INTO products (id, name, description, price, category, stock, sku, barcode, image, is_active, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        product.id, product.name, product.description, product.price, product.category,
        product.stock, product.sku, product.barcode, product.image, product.isActive,
        product.createdAt, product.updatedAt
      ]);
    }
  }

  /**
   * Insert sample outlets
   */
  private async insertSampleOutlets(): Promise<void> {
    const outlets = [
      {
        id: 'outlet_1',
        name: 'Main Shop',
        address: '789 Business Blvd, City, State',
        phone: '+1555123456',
        email: '<EMAIL>',
        managerId: 'staff_1',
        managerName: 'Manager One',
        isActive: 1,
        operatingHours: JSON.stringify({
          monday: '9:00-18:00',
          tuesday: '9:00-18:00',
          wednesday: '9:00-18:00',
          thursday: '9:00-18:00',
          friday: '9:00-18:00',
          saturday: '10:00-16:00',
          sunday: 'Closed'
        }),
        settings: JSON.stringify({
          taxRate: 8.5,
          currency: 'USD',
          timezone: 'America/New_York'
        }),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    for (const outlet of outlets) {
      await this.db?.runAsync(`
        INSERT INTO outlets (id, name, address, phone, email, managerId, managerName, is_active, operatingHours, settings, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        outlet.id, outlet.name, outlet.address, outlet.phone, outlet.email,
        outlet.managerId, outlet.managerName, outlet.isActive, outlet.operatingHours,
        outlet.settings, outlet.createdAt, outlet.updatedAt
      ]);
    }
  }
}