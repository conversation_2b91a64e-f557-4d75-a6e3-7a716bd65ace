/**
 * Database Manager for Coordinating All Database Services
 * Provides a centralized interface for all database operations
 */

import LoggingService from '../LoggingService';

import { BaseSQLiteService } from './BaseSQLiteService';
import { CustomerSQLiteService, customerSQLiteService } from './CustomerSQLiteService';

import { OrderSQLiteService, orderSQLiteService } from './OrderSQLiteService';
import { outletSQLiteService } from './OutletSQLiteService';
import { ProductSQLiteService, productSQLiteService } from './ProductSQLiteService';
import { StaffSQLiteService, staffSQLiteService } from './StaffSQLiteService';


export interface DatabaseServices {
  customer: CustomerSQLiteService;
  product: ProductSQLiteService;
  order: OrderSQLiteService;
  staff: StaffSQLiteService;
  outlet: any; // Using any since OutletSQLiteService is not exported as a class
}

export class DatabaseManager {
  private static instance: DatabaseManager;
  private services: Map<string, BaseSQLiteService> = new Map();
  private isInitialized: boolean = false;

  private constructor() {}

  static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }

  /**
   * Initialize all database services
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      LoggingService.info('Starting database initialization', 'DATABASE_MANAGER');
      
      // Initialize all services (tables will be created by the first service)
      await customerSQLiteService.initialize();
      this.services.set('customer', customerSQLiteService);

      await productSQLiteService.initialize();
      this.services.set('product', productSQLiteService);

      await orderSQLiteService.initialize();
      this.services.set('order', orderSQLiteService);

      await staffSQLiteService.initialize();
      this.services.set('staff', staffSQLiteService);

      await outletSQLiteService.initialize();
      this.services.set('outlet', outletSQLiteService);

      this.isInitialized = true;
      LoggingService.info('Database Manager initialized successfully', 'DATABASE_MANAGER');
    } catch (error) {
      LoggingService.error('Failed to initialize Database Manager', 'DATABASE_MANAGER', error as Error);
      throw error;
    }
  }

  /**
   * Get a specific database service
   */
  getService<T extends BaseSQLiteService>(serviceName: string): T {
    if (!this.isInitialized) {
      throw new Error('Database Manager not initialized. Call initialize() first.');
    }

    const service = this.services.get(serviceName);
    if (!service) {
      throw new Error(`Database service '${serviceName}' not found.`);
    }

    return service as T;
  }

  /**
   * Get all available services
   */
  getServices(): DatabaseServices {
    if (!this.isInitialized) {
      throw new Error('Database Manager not initialized. Call initialize() first.');
    }

    return {
      customer: this.getService<CustomerSQLiteService>('customer'),
      product: this.getService<ProductSQLiteService>('product'),
      order: this.getService<OrderSQLiteService>('order'),
      staff: this.getService<StaffSQLiteService>('staff'),
      outlet: this.getService<any>('outlet'),
    };
  }

  /**
   * Execute a transaction across multiple services
   */
  async executeTransaction<T = any>(
    callback: (services: DatabaseServices) => Promise<T>
  ): Promise<T> {
    if (!this.isInitialized) {
      throw new Error('Database Manager not initialized. Call initialize() first.');
    }

    const services = this.getServices();
    const customerService = services.customer;

    return customerService.executeTransaction(async (db) => {
      return await callback(services);
    });
  }

  /**
   * Check if all services are ready
   */
  isReady(): boolean {
    if (!this.isInitialized) return false;

    for (const service of this.services.values()) {
      if (!service.isReady()) return false;
    }

    return true;
  }

  /**
   * Close all database connections
   */
  async close(): Promise<void> {
    const closePromises = Array.from(this.services.values()).map(service => service.close());
    await Promise.all(closePromises);
    
    this.services.clear();
    this.isInitialized = false;
    
    LoggingService.info('Database Manager closed successfully', 'DATABASE_MANAGER');
  }

  /**
   * Get database statistics
   */
  async getDatabaseStats(): Promise<{
    totalServices: number;
    activeServices: number;
    serviceNames: string[];
  }> {
    if (!this.isInitialized) {
      throw new Error('Database Manager not initialized. Call initialize() first.');
    }

    const serviceNames = Array.from(this.services.keys());
    const activeServices = Array.from(this.services.values()).filter(service => service.isReady()).length;

    return {
      totalServices: this.services.size,
      activeServices,
      serviceNames
    };
  }

  /**
   * Validate database integrity across all services
   */
  async validateIntegrity(): Promise<{
    isValid: boolean;
    issues: string[];
    serviceResults: { [serviceName: string]: { isValid: boolean; issues: string[] } };
  }> {
    if (!this.isInitialized) {
      throw new Error('Database Manager not initialized. Call initialize() first.');
    }

    const serviceResults: { [serviceName: string]: { isValid: boolean; issues: string[] } } = {};
    const allIssues: string[] = [];
    let overallValid = true;

    for (const [serviceName, service] of this.services.entries()) {
      try {
        // Each service should implement its own integrity validation
        // For now, we'll just check if the service is ready
        const isValid = service.isReady();
        const issues = isValid ? [] : [`Service ${serviceName} is not ready`];
        
        serviceResults[serviceName] = { isValid, issues };
        
        if (!isValid) {
          overallValid = false;
          allIssues.push(...issues);
        }
      } catch (error) {
        const errorMessage = `Error validating service ${serviceName}: ${(error as Error).message}`;
        serviceResults[serviceName] = { isValid: false, issues: [errorMessage] };
        allIssues.push(errorMessage);
        overallValid = false;
      }
    }

    return {
      isValid: overallValid,
      issues: allIssues,
      serviceResults
    };
  }
}

// Export singleton instance
export const databaseManager = DatabaseManager.getInstance(); 