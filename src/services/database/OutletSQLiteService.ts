/**
 * OutletSQLiteService - Database operations for outlet management
 * Handles CRUD operations for outlets and outlet inventory
 */

import { 
  Outlet, 
  OutletInventory, 
  CreateOutletData, 
  UpdateOutletData, 
  OutletFilters,
  InventoryTransaction
} from '../../types';
import LoggingService from '../LoggingService';

import { BaseSQLiteService } from './BaseSQLiteService';

interface ParsedOutlet extends Omit<Outlet, 'createdAt' | 'updatedAt'> {
  createdAt: string;
  updatedAt: string;
}

interface ParsedOutletInventory extends Omit<OutletInventory, 'lastUpdated'> {
  lastUpdated: string;
}

class OutletSQLiteService extends BaseSQLiteService {
  constructor() {
    super({
      name: 'tailorza.db',
      version: 1,
      enableLogging: true
    });
  }

  /**
   * Get all outlets with optional filtering
   */
  async getOutlets(filters?: OutletFilters): Promise<Outlet[]> {
    try {
      let query = `
        SELECT 
          id, name, address, phone, email, managerId, managerName, 
          isActive, operatingHours, settings, createdAt, updatedAt
        FROM outlets
        WHERE 1=1
      `;
      const params: any[] = [];

      if (filters?.isActive !== undefined) {
        query += ' AND isActive = ?';
        params.push(filters.isActive ? 1 : 0);
      }

      if (filters?.search) {
        query += ' AND (name LIKE ? OR address LIKE ? OR phone LIKE ?)';
        const searchTerm = `%${filters.search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      query += ' ORDER BY name ASC';

      const rows = await this.executeQuery(query, params);
      return rows.map((row) => this.parseOutletRow(row));
    } catch (error) {
      LoggingService.error('Failed to get outlets', 'OUTLET_SQLITE', error as Error);
      throw error;
    }
  }

  /**
   * Get outlet by ID
   */
  async getOutletById(id: string): Promise<Outlet | null> {
    try {
      const query = `
        SELECT 
          id, name, address, phone, email, managerId, managerName, 
          isActive, operatingHours, settings, createdAt, updatedAt
        FROM outlets
        WHERE id = ?
      `;
      
      const row = await this.executeSingleQuery(query, [id]);
      return row ? this.parseOutletRow(row) : null;
    } catch (error) {
      LoggingService.error('Failed to get outlet by ID', 'OUTLET_SQLITE', error as Error);
      throw error;
    }
  }

  /**
   * Create new outlet
   */
  async createOutlet(outletData: CreateOutletData): Promise<Outlet> {
    try {
      const now = new Date().toISOString();
      const query = `
        INSERT INTO outlets (
          id, name, address, phone, email, managerId, managerName, 
          isActive, operatingHours, settings, createdAt, updatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      const outletId = this.generateId();
      const params = [
        outletId,
        outletData.name,
        outletData.address,
        outletData.phone,
        outletData.email,
        outletData.managerId,
        outletData.managerName,
        outletData.isActive ? 1 : 0,
        this.stringifyJSON(outletData.operatingHours || {}),
        this.stringifyJSON(outletData.settings || {
          taxRate: 0,
          currency: 'USD',
          timezone: 'UTC',
          defaultMeasurementUnit: 'inches'
        }),
        now,
        now
      ];

      await this.executeWrite(query, params);
      
      const newOutlet = await this.getOutletById(outletId);
      if (!newOutlet) {
        throw new Error('Failed to create outlet');
      }

      LoggingService.info(`Created outlet: ${outletData.name}`, 'OUTLET_SQLITE');
      return newOutlet;
    } catch (error) {
      LoggingService.error('Failed to create outlet', 'OUTLET_SQLITE', error as Error);
      throw error;
    }
  }

  /**
   * Update existing outlet
   */
  async updateOutlet(id: string, updates: UpdateOutletData): Promise<Outlet> {
    try {
      const existingOutlet = await this.getOutletById(id);
      if (!existingOutlet) {
        throw new Error('Outlet not found');
      }

      const updateFields: string[] = [];
      const params: any[] = [];

      if (updates.name !== undefined) {
        updateFields.push('name = ?');
        params.push(updates.name);
      }

      if (updates.address !== undefined) {
        updateFields.push('address = ?');
        params.push(updates.address);
      }

      if (updates.phone !== undefined) {
        updateFields.push('phone = ?');
        params.push(updates.phone);
      }

      if (updates.email !== undefined) {
        updateFields.push('email = ?');
        params.push(updates.email);
      }

      if (updates.managerId !== undefined) {
        updateFields.push('managerId = ?');
        params.push(updates.managerId);
      }

      if (updates.managerName !== undefined) {
        updateFields.push('managerName = ?');
        params.push(updates.managerName);
      }

      if (updates.isActive !== undefined) {
        updateFields.push('isActive = ?');
        params.push(updates.isActive ? 1 : 0);
      }

      if (updates.operatingHours !== undefined) {
        updateFields.push('operatingHours = ?');
        params.push(this.stringifyJSON(updates.operatingHours));
      }

      if (updates.settings !== undefined) {
        updateFields.push('settings = ?');
        params.push(this.stringifyJSON(updates.settings));
      }

      if (updateFields.length === 0) {
        return existingOutlet;
      }

      updateFields.push('updatedAt = ?');
      params.push(new Date().toISOString());
      params.push(id);

      const query = `UPDATE outlets SET ${updateFields.join(', ')} WHERE id = ?`;
      await this.executeWrite(query, params);

      const updatedOutlet = await this.getOutletById(id);
      if (!updatedOutlet) {
        throw new Error('Failed to update outlet');
      }

      LoggingService.info(`Updated outlet: ${id}`, 'OUTLET_SQLITE');
      return updatedOutlet;
    } catch (error) {
      LoggingService.error('Failed to update outlet', 'OUTLET_SQLITE', error as Error);
      throw error;
    }
  }

  /**
   * Deactivate outlet
   */
  async deactivateOutlet(id: string): Promise<void> {
    try {
      const query = 'UPDATE outlets SET isActive = 0, updatedAt = ? WHERE id = ?';
      await this.executeWrite(query, [new Date().toISOString(), id]);
      
      LoggingService.info(`Deactivated outlet: ${id}`, 'OUTLET_SQLITE');
    } catch (error) {
      LoggingService.error('Failed to deactivate outlet', 'OUTLET_SQLITE', error as Error);
      throw error;
    }
  }

  /**
   * Get outlet inventory
   */
  async getOutletInventory(outletId: string): Promise<OutletInventory[]> {
    try {
      const query = `
        SELECT 
          id, outletId, itemId, itemType, quantity, reservedQuantity,
          reorderLevel, location, lastUpdated, notes
        FROM outlet_inventory
        WHERE outletId = ?
        ORDER BY lastUpdated DESC
      `;
      
      const rows = await this.executeQuery(query, [outletId]);
      return rows.map((row) => this.parseOutletInventoryRow(row));
    } catch (error) {
      LoggingService.error('Failed to get outlet inventory', 'OUTLET_SQLITE', error as Error);
      throw error;
    }
  }

  /**
   * Update outlet inventory
   */
  async updateOutletInventory(outletId: string, itemId: string, quantity: number): Promise<void> {
    try {
      const now = new Date().toISOString();
      
      // Check if inventory record exists
      const existingQuery = `
        SELECT id FROM outlet_inventory 
        WHERE outletId = ? AND itemId = ?
      `;
      const existing = await this.executeSingleQuery(existingQuery, [outletId, itemId]);

      if (existing) {
        // Update existing record
        const updateQuery = `
          UPDATE outlet_inventory 
          SET quantity = ?, lastUpdated = ?
          WHERE outletId = ? AND itemId = ?
        `;
        await this.executeWrite(updateQuery, [quantity, now, outletId, itemId]);
      } else {
        // Create new record
        const insertQuery = `
          INSERT INTO outlet_inventory (
            id, outletId, itemId, itemType, quantity, reservedQuantity,
            reorderLevel, location, lastUpdated, notes
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
        const inventoryId = this.generateId();
        await this.executeWrite(insertQuery, [
          inventoryId, outletId, itemId, 'fabric', quantity, 0, 10, '', now, ''
        ]);
      }

      LoggingService.info(`Updated outlet inventory: ${outletId} - ${itemId}`, 'OUTLET_SQLITE');
    } catch (error) {
      LoggingService.error('Failed to update outlet inventory', 'OUTLET_SQLITE', error as Error);
      throw error;
    }
  }

  /**
   * Transfer inventory between outlets
   */
  async transferInventory(
    fromOutletId: string, 
    toOutletId: string, 
    itemId: string, 
    quantity: number
  ): Promise<void> {
    try {
      await this.executeTransaction(async () => {
        // Get current inventory levels
        const fromInventory = await this.getOutletInventoryItem(fromOutletId, itemId);
        const toInventory = await this.getOutletInventoryItem(toOutletId, itemId);

        if (!fromInventory || fromInventory.quantity < quantity) {
          throw new Error('Insufficient inventory for transfer');
        }

        // Update source outlet inventory
        const newFromQuantity = fromInventory.quantity - quantity;
        await this.updateOutletInventory(fromOutletId, itemId, newFromQuantity);

        // Update destination outlet inventory
        const newToQuantity = (toInventory?.quantity || 0) + quantity;
        await this.updateOutletInventory(toOutletId, itemId, newToQuantity);

        // Record the transfer transaction
        await this.recordInventoryTransfer(fromOutletId, toOutletId, itemId, quantity);
      });

      LoggingService.info(`Transferred inventory: ${quantity} ${itemId} from ${fromOutletId} to ${toOutletId}`, 'OUTLET_SQLITE');
    } catch (error) {
      LoggingService.error('Failed to transfer inventory', 'OUTLET_SQLITE', error as Error);
      throw error;
    }
  }

  /**
   * Get specific inventory item for outlet
   */
  private async getOutletInventoryItem(outletId: string, itemId: string): Promise<OutletInventory | null> {
    try {
      const query = `
        SELECT 
          id, outletId, itemId, itemType, quantity, reservedQuantity,
          reorderLevel, location, lastUpdated, notes
        FROM outlet_inventory
        WHERE outletId = ? AND itemId = ?
      `;
      
      const row = await this.executeSingleQuery(query, [outletId, itemId]);
      return row ? this.parseOutletInventoryRow(row) : null;
    } catch (error) {
      LoggingService.error('Failed to get outlet inventory item', 'OUTLET_SQLITE', error as Error);
      throw error;
    }
  }

  /**
   * Record inventory transfer transaction
   */
  private async recordInventoryTransfer(
    fromOutletId: string, 
    toOutletId: string, 
    itemId: string, 
    quantity: number
  ): Promise<void> {
    try {
      const query = `
        INSERT INTO inventory_transfers (
          id, fromOutletId, toOutletId, itemId, quantity, 
          transferDate, status, notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `;
      
      const transferId = this.generateId();
      const now = new Date().toISOString();
      
      await this.executeWrite(query, [
        transferId, fromOutletId, toOutletId, itemId, quantity,
        now, 'completed', `Transfer from ${fromOutletId} to ${toOutletId}`
      ]);
    } catch (error) {
      LoggingService.error('Failed to record inventory transfer', 'OUTLET_SQLITE', error as Error);
      throw error;
    }
  }

  /**
   * Get outlet statistics
   */
  async getOutletStats(): Promise<{
    totalOutlets: number;
    activeOutlets: number;
    totalInventory: number;
    lowStockItems: number;
  }> {
    try {
      const outletsQuery = 'SELECT COUNT(*) as total, SUM(CASE WHEN isActive = 1 THEN 1 ELSE 0 END) as active FROM outlets';
      const outletsResult = await this.executeSingleQuery(outletsQuery);

      const inventoryQuery = 'SELECT COUNT(*) as total FROM outlet_inventory';
      const inventoryResult = await this.executeSingleQuery(inventoryQuery);

      const lowStockQuery = `
        SELECT COUNT(*) as count 
        FROM outlet_inventory 
        WHERE quantity <= reorderPoint
      `;
      const lowStockResult = await this.executeSingleQuery(lowStockQuery);

      return {
        totalOutlets: outletsResult?.total || 0,
        activeOutlets: outletsResult?.active || 0,
        totalInventory: inventoryResult?.total || 0,
        lowStockItems: lowStockResult?.count || 0
      };
    } catch (error) {
      LoggingService.error('Failed to get outlet stats', 'OUTLET_SQLITE', error as Error);
      throw error;
    }
  }

  /**
   * Parse outlet row from database
   */
  private parseOutletRow(row: any): Outlet {
    return {
      id: row.id,
      name: row.name,
      address: row.address,
      phone: row.phone,
      email: row.email,
      managerId: row.managerId,
      managerName: row.managerName,
      isActive: Boolean(row.isActive),
      operatingHours: this.parseJSON(row.operatingHours, {
        monday: '09:00-18:00',
        tuesday: '09:00-18:00',
        wednesday: '09:00-18:00',
        thursday: '09:00-18:00',
        friday: '09:00-18:00',
        saturday: '09:00-18:00',
        sunday: 'Closed'
      }),
      settings: this.parseJSON(row.settings, {
        taxRate: 0,
        currency: 'USD',
        timezone: 'UTC',
        defaultMeasurementUnit: 'inches'
      }),
      createdAt: row.createdAt,
      updatedAt: row.updatedAt
    };
  }

  /**
   * Parse outlet inventory row from database
   */
  private parseOutletInventoryRow(row: any): OutletInventory {
    return {
      id: row.id,
      outletId: row.outletId,
      productId: row.productId || row.itemId || '',
      itemId: row.itemId,
      quantity: row.quantity,
      minStock: row.minStock || 0,
      maxStock: row.maxStock || 100,
      lastUpdated: row.lastUpdated,
    };
  }

  /**
   * Record an inventory transaction for an outlet
   */
  async recordInventoryTransaction(transaction: Omit<InventoryTransaction, 'id' | 'timestamp'>): Promise<void> {
    try {
      const query = `
        INSERT INTO inventory_transactions (
          id, itemId, type, quantity, fromLocation, toLocation, orderId, staffId, notes, performedBy, cost, batchNumber, timestamp
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      const id = this.generateId();
      const now = new Date().toISOString();
      await this.executeWrite(query, [
        id,
        transaction.itemId,
        transaction.type,
        transaction.quantity,
        transaction.fromLocation || null,
        transaction.toLocation || null,
        transaction.orderId || null,
        transaction.staffId || null,
        transaction.notes || '',
        transaction.performedBy || 'system',
        transaction.cost || 0,
        transaction.batchNumber || null,
        now
      ]);
      LoggingService.info('Inventory transaction recorded', 'OUTLET_SQLITE');
    } catch (error) {
      LoggingService.error('Failed to record inventory transaction', 'OUTLET_SQLITE', error as Error);
      throw error;
    }
  }

  /**
   * Transfer inventory between outlets (public wrapper)
   */
  async transferInventoryBetweenOutlets(fromOutletId: string, toOutletId: string, itemId: string, quantity: number): Promise<void> {
    return this.transferInventory(fromOutletId, toOutletId, itemId, quantity);
  }


}

// Export singleton instance
export const outletSQLiteService = new OutletSQLiteService(); 