/**
 * Product SQLite Service for Product Database Operations
 * Handles all product-related database operations
 */

import { Product, ProductCategory } from '../../types';
import LoggingService from '../LoggingService';

import { BaseSQLiteService } from './BaseSQLiteService';

interface ParsedProduct {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  stock: number;
  sku: string;
  barcode: string | null;
  image: string | null;
  isActive: number;
  createdAt: string;
  updatedAt: string;
}

export class ProductSQLiteService extends BaseSQLiteService {
  constructor() {
    super({
      name: 'tailorza.db',
      version: 2,
      enableLogging: true
    });
  }

  /**
   * Get all products with optional filters
   */
  async getProducts(filters: {
    isActive?: boolean;
    category?: ProductCategory;
    search?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<Product[]> {
    await this.initialize();

    try {
      let query = 'SELECT * FROM products WHERE 1=1';
      const params: any[] = [];

      if (filters.isActive !== undefined) {
        query += ' AND isActive = ?';
        params.push(this.boolToInt(filters.isActive));
      }

      if (filters.category) {
        query += ' AND category = ?';
        params.push(filters.category);
      }

      if (filters.search) {
        query += ' AND (name LIKE ? OR description LIKE ? OR sku LIKE ?)';
        const searchTerm = `%${filters.search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      query += ' ORDER BY name ASC';

      if (filters.limit) {
        query += ' LIMIT ?';
        params.push(filters.limit);
      }

      if (filters.offset) {
        query += ' OFFSET ?';
        params.push(filters.offset);
      }

      const rows = await this.executeQuery<ParsedProduct>(query, params);
      return rows.map((row) => this.parseProductRow(row));
    } catch (error) {
      LoggingService.error('Failed to get products', 'PRODUCT_DB', error as Error);
      throw error;
    }
  }

  /**
   * Get product by ID
   */
  async getProductById(id: string): Promise<Product | null> {
    await this.initialize();

    try {
      const row = await this.executeSingleQuery<ParsedProduct>(
        'SELECT * FROM products WHERE id = ?',
        [id]
      );

      return row ? this.parseProductRow(row) : null;
    } catch (error) {
      LoggingService.error('Failed to get product by ID', 'PRODUCT_DB', error as Error);
      throw error;
    }
  }

  /**
   * Save product (create or update)
   */
  async saveProduct(product: Partial<Product> & { name: string; price: number }): Promise<Product> {
    await this.initialize();

    try {
      const now = new Date().toISOString();

      if (product.id) {
        // Update existing product
        await this.executeWrite(`
          UPDATE products SET
            name = ?, description = ?, price = ?, category = ?, stock = ?,
            sku = ?, barcode = ?, image = ?, isActive = ?, updatedAt = ?
          WHERE id = ?
        `, [
          product.name,
          product.description || '',
          product.price,
          product.category || 'Cakes',
          product.stock || 0,
          product.sku || '',
          product.barcode || null,
          product.image || null,
          this.boolToInt(product.isActive || true),
          now,
          product.id
        ]);

        return { ...product, updatedAt: now } as Product;
      } else {
        // Create new product
        const id = this.generateId('product');
        const result = await this.executeWrite(`
          INSERT INTO products (
            id, name, description, price, category, stock, sku, barcode, image, isActive, createdAt, updatedAt
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          id,
          product.name,
          product.description || '',
          product.price,
          product.category || 'Cakes',
          product.stock || 0,
          product.sku || '',
          product.barcode || null,
          product.image || null,
          this.boolToInt(product.isActive || true),
          now,
          now
        ]);

        return {
          ...product,
          id,
          createdAt: now,
          updatedAt: now
        } as Product;
      }
    } catch (error) {
      LoggingService.error('Failed to save product', 'PRODUCT_DB', error as Error);
      throw error;
    }
  }

  /**
   * Delete product
   */
  async deleteProduct(id: string): Promise<void> {
    await this.initialize();

    try {
      await this.executeWrite('DELETE FROM products WHERE id = ?', [id]);
      LoggingService.info(`Product deleted: ${id}`, 'PRODUCT_DB');
    } catch (error) {
      LoggingService.error('Failed to delete product', 'PRODUCT_DB', error as Error);
      throw error;
    }
  }

  /**
   * Update product stock
   */
  async updateStock(id: string, newStock: number): Promise<void> {
    await this.initialize();

    try {
      await this.executeWrite(`
        UPDATE products SET stock = ?, updatedAt = ? WHERE id = ?
      `, [newStock, new Date().toISOString(), id]);
    } catch (error) {
      LoggingService.error('Failed to update product stock', 'PRODUCT_DB', error as Error);
      throw error;
    }
  }

  /**
   * Search products by name, description, or SKU
   */
  async searchProducts(searchTerm: string, limit: number = 10): Promise<Product[]> {
    await this.initialize();

    try {
      const query = `
        SELECT * FROM products 
        WHERE (name LIKE ? OR description LIKE ? OR sku LIKE ?) AND isActive = 1
        ORDER BY name ASC
        LIMIT ?
      `;
      const searchPattern = `%${searchTerm}%`;
      const rows = await this.executeQuery<ParsedProduct>(query, [searchPattern, searchPattern, searchPattern, limit]);
      
      return rows.map((row) => this.parseProductRow(row));
    } catch (error) {
      LoggingService.error('Failed to search products', 'PRODUCT_DB', error as Error);
      throw error;
    }
  }

  /**
   * Get products by category
   */
  async getProductsByCategory(category: ProductCategory): Promise<Product[]> {
    await this.initialize();

    try {
      const rows = await this.executeQuery<ParsedProduct>(
        'SELECT * FROM products WHERE category = ? AND isActive = 1 ORDER BY name ASC',
        [category]
      );
      
      return rows.map((row) => this.parseProductRow(row));
    } catch (error) {
      LoggingService.error('Failed to get products by category', 'PRODUCT_DB', error as Error);
      throw error;
    }
  }

  /**
   * Get low stock products
   */
  async getLowStockProducts(threshold: number = 10): Promise<Product[]> {
    await this.initialize();

    try {
      const rows = await this.executeQuery<ParsedProduct>(
        'SELECT * FROM products WHERE stock <= ? AND isActive = 1 ORDER BY stock ASC',
        [threshold]
      );
      
      return rows.map((row) => this.parseProductRow(row));
    } catch (error) {
      LoggingService.error('Failed to get low stock products', 'PRODUCT_DB', error as Error);
      throw error;
    }
  }

  /**
   * Get product statistics
   */
  async getProductStats(): Promise<{
    totalProducts: number;
    activeProducts: number;
    lowStockProducts: number;
    productsByCategory: Array<{ category: ProductCategory; count: number }>;
    totalValue: number;
  }> {
    await this.initialize();

    try {
      const allProducts = await this.getProducts();
      const activeProducts = allProducts.filter(p => p.isActive);
      const lowStockProducts = allProducts.filter(p => p.stock <= 10);

      // Group by category
      const categoryCounts: { [key: string]: number } = {};
      for (const product of activeProducts) {
        categoryCounts[product.category] = (categoryCounts[product.category] || 0) + 1;
      }

      const productsByCategory = Object.entries(categoryCounts).map(([category, count]) => ({
        category: category as ProductCategory,
        count
      }));

      const totalValue = activeProducts.reduce((sum, product) => sum + (product.price * product.stock), 0);

      return {
        totalProducts: allProducts.length,
        activeProducts: activeProducts.length,
        lowStockProducts: lowStockProducts.length,
        productsByCategory,
        totalValue
      };
    } catch (error) {
      LoggingService.error('Failed to get product stats', 'PRODUCT_DB', error as Error);
      throw error;
    }
  }

  /**
   * Parse product row from database
   */
  private parseProductRow(row: ParsedProduct): Product {
    return {
      id: row.id,
      name: row.name,
      description: row.description,
      price: row.price,
      category: row.category as ProductCategory,
      stock: row.stock,
      sku: row.sku,
      barcode: row.barcode || undefined,
      image: row.image || undefined,
      isActive: this.intToBool(row.isActive),
      createdAt: row.createdAt,
      updatedAt: row.updatedAt
    };
  }
}

// Export singleton instance
export const productSQLiteService = new ProductSQLiteService(); 