/**
 * Base SQLite Service for Core Database Operations
 * Provides connection management, transactions, and common database utilities
 */

import * as SQLite from 'expo-sqlite';

import LoggingService from '../LoggingService';

import { DatabaseSchema } from './DatabaseSchema';

export interface DatabaseConfig {
  name: string;
  version: number;
  enableLogging?: boolean;
}

export interface TransactionCallback<T = any> {
  (db: SQLite.SQLiteDatabase): Promise<T>;
}

export class BaseSQLiteService {
  protected db: SQLite.SQLiteDatabase | null = null;
  protected isInitialized: boolean = false;
  protected config: DatabaseConfig;
  private static schemaInitialized: boolean = false;

  constructor(config: DatabaseConfig) {
    this.config = config;
  }

  /**
   * Initialize database connection
   */
  async initialize(): Promise<void> {
    if (this.isInitialized && this.db) return;

    try {
      // Close existing connection if any
      if (this.db) {
        await this.db.closeAsync();
        this.db = null;
      }

      // Add retry logic for database connection
      let retries = 3;
      while (retries > 0) {
        try {
          this.db = await SQLite.openDatabaseAsync(this.config.name);
          
          // Test the connection with a simple query
          await this.db.getFirstAsync('SELECT 1');
          break;
        } catch (connectionError) {
          retries--;
          LoggingService.warn(`Database connection failed, retrying... (${3 - retries}/3)`, 'DATABASE');
          
          if (retries === 0) {
            throw new Error(`Failed to connect to database after 3 attempts: ${connectionError}`);
          }
          
          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retry
        }
      }
      
      // Initialize database schema only once
      if (!BaseSQLiteService.schemaInitialized) {
        const schema = new DatabaseSchema(this.db!);
        await schema.initializeTables();
        await schema.initializeSampleData();
        BaseSQLiteService.schemaInitialized = true;
        LoggingService.info('Database schema and sample data initialized', 'DATABASE');
      }
      
      this.isInitialized = true;
      
      if (this.config.enableLogging) {
        LoggingService.info(`Database service initialized: ${this.config.name}`, 'DATABASE');
      }
    } catch (error) {
      LoggingService.error('Database initialization failed', 'DATABASE', error as Error);
      throw error;
    }
  }

  /**
   * Get the database instance
   */
  getDatabase(): SQLite.SQLiteDatabase {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }
    return this.db;
  }

  /**
   * Ensure database is initialized
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
  }

  /**
   * Execute a query with parameters
   */
  async executeQuery<T = any>(query: string, params: any[] = []): Promise<T[]> {
    await this.ensureInitialized();
    const db = this.getDatabase();
    
    try {
      const result = await db.getAllAsync(query, params) as T[];
      
      if (this.config.enableLogging) {
        LoggingService.debug(`Query executed: ${query.substring(0, 50)}...`, 'DATABASE');
      }
      
      return result;
    } catch (error) {
      LoggingService.error('Query execution failed', 'DATABASE', error as Error);
      
      // Try to reinitialize on connection errors
      if (error instanceof Error && error.message.includes('NullPointerException')) {
        LoggingService.warn('Attempting to reinitialize database connection', 'DATABASE');
        this.isInitialized = false;
        this.db = null;
        await this.ensureInitialized();
        // Retry the query once
        const db = this.getDatabase();
        return await db.getAllAsync(query, params) as T[];
      }
      throw error;
    }
  }

  /**
   * Execute a single row query
   */
  async executeSingleQuery<T = any>(query: string, params: any[] = []): Promise<T | null> {
    await this.ensureInitialized();
    const db = this.getDatabase();
    
    try {
      const result = await db.getFirstAsync(query, params) as T | null;
      
      if (this.config.enableLogging) {
        LoggingService.debug(`Single query executed: ${query.substring(0, 50)}...`, 'DATABASE');
      }
      
      return result;
    } catch (error) {
      LoggingService.error('Single query execution failed', 'DATABASE', error as Error);
      
      // Try to reinitialize on connection errors
      if (error instanceof Error && error.message.includes('NullPointerException')) {
        LoggingService.warn('Attempting to reinitialize database connection', 'DATABASE');
        this.isInitialized = false;
        this.db = null;
        await this.ensureInitialized();
        // Retry the query once
        const db = this.getDatabase();
        return await db.getFirstAsync(query, params) as T | null;
      }
      throw error;
    }
  }

  /**
   * Execute a write operation (INSERT, UPDATE, DELETE)
   */
  async executeWrite(query: string, params: any[] = []): Promise<{ lastInsertRowId?: number; changes?: number }> {
    await this.ensureInitialized();
    const db = this.getDatabase();
    
    try {
      const result = await db.runAsync(query, params);
      
      if (this.config.enableLogging) {
        LoggingService.debug(`Write operation executed: ${query.substring(0, 50)}...`, 'DATABASE');
      }
      
      return {
        lastInsertRowId: result.lastInsertRowId,
        changes: result.changes
      };
    } catch (error) {
      LoggingService.error('Write operation failed', 'DATABASE', error as Error);
      throw error;
    }
  }

  /**
   * Execute a transaction
   */
  async executeTransaction<T = any>(callback: TransactionCallback<T>): Promise<T> {
    await this.ensureInitialized();
    const db = this.getDatabase();
    
    try {
      // Use a simple approach without transactions for now
      // TODO: Implement proper transaction support when available
      const result = await callback(db);
      
      if (this.config.enableLogging) {
        LoggingService.debug('Transaction executed successfully', 'DATABASE');
      }
      
      return result;
    } catch (error) {
      LoggingService.error('Transaction failed', 'DATABASE', error as Error);
      throw error;
    }
  }

  /**
   * Check if database is ready
   */
  isReady(): boolean {
    return this.isInitialized && this.db !== null;
  }

  /**
   * Close database connection
   */
  async close(): Promise<void> {
    if (this.db) {
      try {
        await this.db.closeAsync();
        LoggingService.info('Database connection closed', 'DATABASE');
      } catch (error) {
        LoggingService.error('Error closing database connection', 'DATABASE', error as Error);
      } finally {
        this.db = null;
        this.isInitialized = false;
      }
    }
  }

  /**
   * Generate a unique ID
   */
  generateId(prefix: string = 'id'): string {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Parse JSON string safely
   */
  protected parseJSON<T = any>(jsonString: string | null, defaultValue: T): T {
    if (!jsonString) return defaultValue;
    try {
      return JSON.parse(jsonString) as T;
    } catch (error) {
      LoggingService.warn('Failed to parse JSON string', 'DATABASE', { jsonString });
      return defaultValue;
    }
  }

  /**
   * Stringify object to JSON safely
   */
  protected stringifyJSON(obj: any): string {
    try {
      return JSON.stringify(obj);
    } catch (error) {
      LoggingService.error('Failed to stringify object to JSON', 'DATABASE', error as Error);
      return '{}';
    }
  }

  /**
   * Convert boolean to integer for SQLite storage
   */
  protected boolToInt(value: boolean): number {
    return value ? 1 : 0;
  }

  /**
   * Convert integer to boolean from SQLite storage
   */
  protected intToBool(value: number): boolean {
    return value === 1;
  }
} 