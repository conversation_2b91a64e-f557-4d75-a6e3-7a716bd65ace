/**
 * Staff SQLite Service for Staff Database Operations
 * Handles all staff-related database operations
 */

import { 
  Staff, 
  StaffRole, 
  SkillLevel, 
  PaymentStructureType,
  StaffWorkload,
  StaffPerformance
} from '../../types';
import LoggingService from '../LoggingService';

import { BaseSQLiteService } from './BaseSQLiteService';

interface ParsedStaff {
  id: string;
  employeeId: string;
  name: string;
  email: string;
  phone: string;
  role: string;
  outletId: string;
  skills: string;
  experience: string;
  performance: string;
  workload: string;
  paymentStructure: string;
  availability: string;
  isActive: number;
  profileImage: string | null;
  emergencyContact: string;
  documents: string;
  createdAt: string;
  updatedAt: string;
}

export class StaffSQLiteService extends BaseSQLiteService {
  constructor() {
    super({
      name: 'tailorza.db',
      version: 2,
      enableLogging: true
    });
  }

  /**
   * Get all staff with optional filters
   */
  async getStaff(filters?: {
    outletId?: string;
    role?: StaffRole;
    isActive?: boolean;
    skill?: string;
    availability?: boolean;
    search?: string;
    limit?: number;
    offset?: number;
  }): Promise<Staff[]> {
    await this.initialize();

    try {
      let query = `
        SELECT 
          s.*,
          o.name as outlet_name
        FROM staff s
        LEFT JOIN outlets o ON s.outlet_id = o.id
        WHERE 1=1
      `;
      const params: any[] = [];

      if (filters?.outletId) {
        query += ' AND s.outlet_id = ?';
        params.push(filters.outletId);
      }

      if (filters?.role) {
        query += ' AND s.role = ?';
        params.push(filters.role);
      }

      if (filters?.isActive !== undefined) {
        query += ' AND s.is_active = ?';
        params.push(this.boolToInt(filters.isActive));
      }

      if (filters?.search) {
        query += ' AND (s.name LIKE ? OR s.email LIKE ? OR s.phone LIKE ?)';
        const searchTerm = `%${filters.search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      query += ' ORDER BY s.name ASC';

      if (filters?.limit) {
        query += ' LIMIT ?';
        params.push(filters.limit);
      }

      if (filters?.offset) {
        query += ' OFFSET ?';
        params.push(filters.offset);
      }

      const rows = await this.executeQuery<ParsedStaff>(query, params);
      let staff = rows.map((row) => this.parseStaffRow(row));

      // Apply skill filter after parsing
      if (filters?.skill) {
        staff = staff.filter(s => 
          s.skills?.some(skill => 
            skill.name.toLowerCase().includes(filters.skill!.toLowerCase())
          )
        );
      }

      // Apply availability filter after parsing
      if (filters?.availability) {
        staff = staff.filter(s => {
          const workload = s.workload;
          return (workload?.scheduledHours || 0) < (workload?.availableHours || 0);
        });
      }

      return staff;
    } catch (error) {
      LoggingService.error('Failed to get staff', 'STAFF_DB', error as Error);
      throw error;
    }
  }

  /**
   * Get staff by ID
   */
  async getStaffById(id: string): Promise<Staff | null> {
    await this.initialize();

    try {
      const row = await this.executeSingleQuery<ParsedStaff>(
        'SELECT * FROM staff WHERE id = ?',
        [id]
      );

      return row ? this.parseStaffRow(row) : null;
    } catch (error) {
      LoggingService.error('Failed to get staff by ID', 'STAFF_DB', error as Error);
      throw error;
    }
  }

  /**
   * Create new staff member
   */
  async createStaff(staffData: {
    name: string;
    email: string;
    phone: string;
    role: StaffRole;
    outletId: string;
    skills: any[];
    experience: any;
    paymentStructure: any;
    emergencyContact: any;
    profileImage?: string;
  }): Promise<Staff> {
    await this.initialize();

    try {
      const id = this.generateId('staff');
      const employeeId = this.generateEmployeeId();
      const now = new Date().toISOString();

      // Initialize default workload and performance
      const defaultWorkload: StaffWorkload = {
        currentOrders: 0,
        maxCapacity: 5,
        capacity: 5,
        availableHours: 40,
        scheduledHours: 0,
        overtimeHours: 0,
        efficiency: 100,
        lastUpdated: new Date().toISOString()
      };

      const defaultPerformance: StaffPerformance = {
        averageCompletionTime: 0,
        qualityRating: 5,
        customerSatisfactionRating: 5,
        ordersCompleted: 0,
        totalOrdersCompleted: 0,
        onTimeDeliveryRate: 100,
        reworkRate: 0,
        goals: [],
        lastUpdated: new Date().toISOString()
      };

      const defaultAvailability = {
        workingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        workingHours: { start: '09:00', end: '17:00' },
        breaks: [],
        timeOff: []
      };

      await this.executeWrite(`
        INSERT INTO staff (
          id, employee_id, name, email, phone, role, outlet_id, skills,
          experience, performance, workload, payment_structure, availability,
          is_active, profile_image, emergency_contact, documents, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        id,
        employeeId,
        staffData.name,
        staffData.email,
        staffData.phone,
        staffData.role,
        staffData.outletId,
        this.stringifyJSON(staffData.skills),
        this.stringifyJSON(staffData.experience),
        this.stringifyJSON(defaultPerformance),
        this.stringifyJSON(defaultWorkload),
        this.stringifyJSON(staffData.paymentStructure),
        this.stringifyJSON(defaultAvailability),
        1, // isActive
        staffData.profileImage || null,
        this.stringifyJSON(staffData.emergencyContact),
        this.stringifyJSON([]), // documents
        now,
        now
      ]);

      LoggingService.info(`Staff member created: ${id}`, 'STAFF_DB');

      return {
        id,
        employeeId,
        name: staffData.name,
        email: staffData.email,
        phone: staffData.phone,
        role: staffData.role,
        outletId: staffData.outletId,
        skills: staffData.skills,
        experience: staffData.experience,
        performance: defaultPerformance,
        workload: defaultWorkload,
        paymentStructure: staffData.paymentStructure,
        availability: defaultAvailability,
        isActive: true,
        profileImage: staffData.profileImage,
        emergencyContact: staffData.emergencyContact,
        documents: [],
        createdAt: now,
        updatedAt: now
      };
    } catch (error) {
      LoggingService.error('Failed to create staff member', 'STAFF_DB', error as Error);
      throw error;
    }
  }

  /**
   * Update staff member
   */
  async updateStaff(id: string, updates: Partial<Staff>): Promise<Staff> {
    await this.initialize();

    try {
      const now = new Date().toISOString();
      const currentStaff = await this.getStaffById(id);
      
      if (!currentStaff) {
        throw new Error('Staff member not found');
      }

      // Prepare update fields
      const updateFields: string[] = [];
      const updateParams: any[] = [];

      if (updates.name !== undefined) {
        updateFields.push('name = ?');
        updateParams.push(updates.name);
      }

      if (updates.email !== undefined) {
        updateFields.push('email = ?');
        updateParams.push(updates.email);
      }

      if (updates.phone !== undefined) {
        updateFields.push('phone = ?');
        updateParams.push(updates.phone);
      }

      if (updates.role !== undefined) {
        updateFields.push('role = ?');
        updateParams.push(updates.role);
      }

      if (updates.outletId !== undefined) {
        updateFields.push('outlet_id = ?');
        updateParams.push(updates.outletId);
      }

      if (updates.skills !== undefined) {
        updateFields.push('skills = ?');
        updateParams.push(this.stringifyJSON(updates.skills));
      }

      if (updates.experience !== undefined) {
        updateFields.push('experience = ?');
        updateParams.push(this.stringifyJSON(updates.experience));
      }

      if (updates.performance !== undefined) {
        updateFields.push('performance = ?');
        updateParams.push(this.stringifyJSON(updates.performance));
      }

      if (updates.workload !== undefined) {
        updateFields.push('workload = ?');
        updateParams.push(this.stringifyJSON(updates.workload));
      }

      if (updates.paymentStructure !== undefined) {
        updateFields.push('payment_structure = ?');
        updateParams.push(this.stringifyJSON(updates.paymentStructure));
      }

      if (updates.availability !== undefined) {
        updateFields.push('availability = ?');
        updateParams.push(this.stringifyJSON(updates.availability));
      }

      if (updates.isActive !== undefined) {
        updateFields.push('is_active = ?');
        updateParams.push(this.boolToInt(updates.isActive));
      }

      if (updates.profileImage !== undefined) {
        updateFields.push('profile_image = ?');
        updateParams.push(updates.profileImage);
      }

      if (updates.emergencyContact !== undefined) {
        updateFields.push('emergency_contact = ?');
        updateParams.push(this.stringifyJSON(updates.emergencyContact));
      }

      if (updates.documents !== undefined) {
        updateFields.push('documents = ?');
        updateParams.push(this.stringifyJSON(updates.documents));
      }

      updateFields.push('updated_at = ?');
      updateParams.push(now);

      if (updateFields.length === 0) {
        return currentStaff;
      }

      await this.executeWrite(`
        UPDATE staff 
        SET ${updateFields.join(', ')}
        WHERE id = ?
      `, [...updateParams, id]);

      LoggingService.info(`Staff member updated: ${id}`, 'STAFF_DB');

      return {
        ...currentStaff,
        ...updates,
        updatedAt: now
      };
    } catch (error) {
      LoggingService.error('Failed to update staff member', 'STAFF_DB', error as Error);
      throw error;
    }
  }

  /**
   * Delete staff member
   */
  async deleteStaff(id: string): Promise<void> {
    await this.initialize();

    try {
      await this.executeWrite('DELETE FROM staff WHERE id = ?', [id]);
      LoggingService.info(`Staff member deleted: ${id}`, 'STAFF_DB');
    } catch (error) {
      LoggingService.error('Failed to delete staff member', 'STAFF_DB', error as Error);
      throw error;
    }
  }

  /**
   * Update staff workload
   */
  async updateStaffWorkload(staffId: string, workload: Partial<StaffWorkload>): Promise<void> {
    await this.initialize();

    try {
      const currentStaff = await this.getStaffById(staffId);
      if (!currentStaff) {
        throw new Error('Staff member not found');
      }

      const currentWorkload = currentStaff.workload || {
        currentOrders: 0,
        maxCapacity: 5,
        efficiency: 100,
        availableHours: 40,
        scheduledHours: 0,
        overtimeHours: 0,
        capacity: 5,
        lastUpdated: new Date().toISOString()
      };
      const updatedWorkload: StaffWorkload = {
        ...currentWorkload,
        ...workload,
        lastUpdated: new Date().toISOString()
      };
      await this.updateStaff(staffId, { workload: updatedWorkload });
    } catch (error) {
      LoggingService.error('Failed to update staff workload', 'STAFF_DB', error as Error);
      throw error;
    }
  }

  /**
   * Update staff performance
   */
  async updateStaffPerformance(staffId: string, performance: Partial<StaffPerformance>): Promise<void> {
    await this.initialize();

    try {
      const currentStaff = await this.getStaffById(staffId);
      if (!currentStaff) {
        throw new Error('Staff member not found');
      }

      const currentPerformance = currentStaff.performance || {
        qualityRating: 5,
        customerSatisfactionRating: 5,
        averageCompletionTime: 0,
        totalOrdersCompleted: 0,
        ordersCompleted: 0,
        onTimeDeliveryRate: 100,
        reworkRate: 0,
        goals: [],
        lastUpdated: new Date().toISOString()
      };
      const updatedPerformance: StaffPerformance = {
        ...currentPerformance,
        ...performance,
        lastUpdated: new Date().toISOString()
      };
      await this.updateStaff(staffId, { performance: updatedPerformance });
    } catch (error) {
      LoggingService.error('Failed to update staff performance', 'STAFF_DB', error as Error);
      throw error;
    }
  }

  /**
   * Get available staff for a specific skill
   */
  async getAvailableStaff(outletId: string, skill?: string): Promise<Staff[]> {
    await this.initialize();

    try {
      const filters: any = { outletId, isActive: true };
      if (skill) {
        filters.skill = skill;
      }
      filters.availability = true;

      return await this.getStaff(filters);
    } catch (error) {
      LoggingService.error('Failed to get available staff', 'STAFF_DB', error as Error);
      throw error;
    }
  }

  /**
   * Get staff by role
   */
  async getStaffByRole(role: StaffRole): Promise<Staff[]> {
    await this.initialize();

    try {
      return await this.getStaff({ role, isActive: true });
    } catch (error) {
      LoggingService.error('Failed to get staff by role', 'STAFF_DB', error as Error);
      throw error;
    }
  }

  /**
   * Get staff performance statistics
   */
  async getStaffPerformanceStats(): Promise<{
    totalStaff: number;
    activeStaff: number;
    averageQualityRating: number;
    averageCustomerSatisfaction: number;
    averageCompletionTime: number;
    staffByRole: Array<{ role: StaffRole; count: number; avgRating: number }>;
    topPerformers: Staff[];
  }> {
    await this.initialize();

    try {
      const allStaff = await this.getStaff({ isActive: true });
      
      const totalStaff = allStaff.length;
      const activeStaff = allStaff.filter(s => s.isActive).length;

      const totalQualityRating = allStaff.reduce((sum, staff) => sum + (staff.performance?.qualityRating || 0), 0);
      const averageQualityRating = totalStaff > 0 ? totalQualityRating / totalStaff : 0;

      const totalCustomerSatisfaction = allStaff.reduce((sum, staff) => sum + (staff.performance?.customerSatisfactionRating || 0), 0);
      const averageCustomerSatisfaction = totalStaff > 0 ? totalCustomerSatisfaction / totalStaff : 0;

      const totalCompletionTime = allStaff.reduce((sum, staff) => sum + (staff.performance?.averageCompletionTime || 0), 0);
      const averageCompletionTime = totalStaff > 0 ? totalCompletionTime / totalStaff : 0;

      // Group by role
      const roleCounts: { [key in StaffRole]?: { count: number; totalRating: number } } = {};
      for (const staff of allStaff) {
        if (!roleCounts[staff.role]) {
          roleCounts[staff.role] = { count: 0, totalRating: 0 };
        }
        roleCounts[staff.role]!.count++;
        roleCounts[staff.role]!.totalRating += staff.performance?.qualityRating || 0;
      }

      const staffByRole = Object.entries(roleCounts).map(([role, data]) => ({
        role: role as StaffRole,
        count: data.count,
        avgRating: data.count > 0 ? data.totalRating / data.count : 0
      }));

      // Get top performers (top 5 by quality rating)
      const topPerformers = allStaff
        .sort((a, b) => (b.performance?.qualityRating || 0) - (a.performance?.qualityRating || 0))
        .slice(0, 5);

      return {
        totalStaff,
        activeStaff,
        averageQualityRating,
        averageCustomerSatisfaction,
        averageCompletionTime,
        staffByRole,
        topPerformers
      };
    } catch (error) {
      LoggingService.error('Failed to get staff performance stats', 'STAFF_DB', error as Error);
      throw error;
    }
  }

  /**
   * Generate unique employee ID
   */
  private generateEmployeeId(): string {
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substr(2, 3).toUpperCase();
    return `EMP${timestamp}${random}`;
  }

  /**
   * Parse staff row from database
   */
  private parseStaffRow(row: ParsedStaff): Staff {
    return {
      id: row.id,
      employeeId: row.employeeId,
      name: row.name,
      email: row.email,
      phone: row.phone,
      role: row.role as StaffRole,
      outletId: row.outletId,
      skills: this.parseJSON(row.skills, []),
      experience: this.parseJSON(row.experience, {
        totalYears: 0,
        previousEmployers: [],
        specializations: [],
        achievements: [],
        trainingCompleted: []
      }),
      performance: this.parseJSON(row.performance, {
        averageCompletionTime: 0,
        qualityRating: 5,
        customerSatisfactionRating: 5,
        ordersCompleted: 0,
        totalOrdersCompleted: 0,
        onTimeDeliveryRate: 100,
        reworkRate: 0,
        goals: [],
        lastUpdated: new Date().toISOString()
      }),
      workload: this.parseJSON(row.workload, {
        currentOrders: 0,
        maxCapacity: 5,
        capacity: 5,
        availableHours: 40,
        scheduledHours: 0,
        overtimeHours: 0,
        efficiency: 100,
        lastUpdated: new Date().toISOString()
      }),
      paymentStructure: this.parseJSON(row.paymentStructure, {
        type: 'hourly' as PaymentStructureType,
        rate: 0,
        bonusStructure: undefined,
        benefits: [],
        paymentSchedule: 'weekly'
      }),
      availability: this.parseJSON(row.availability, {
        workingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        workingHours: { start: '09:00', end: '17:00' },
        breaks: [],
        timeOff: []
      }),
      isActive: this.intToBool(row.isActive),
      profileImage: row.profileImage || undefined,
      emergencyContact: this.parseJSON(row.emergencyContact, {
        name: '',
        relationship: '',
        phone: '',
        email: '',
        address: ''
      }),
      documents: this.parseJSON(row.documents, []),
      createdAt: row.createdAt,
      updatedAt: row.updatedAt
    };
  }
}

// Export singleton instance
export const staffSQLiteService = new StaffSQLiteService(); 