/**
 * Order SQLite Service for Order Database Operations
 * Handles all order-related database operations
 */

import { Order, OrderItem, OrderStatus, OrderType } from '../../types';
import LoggingService from '../LoggingService';

import { BaseSQLiteService } from './BaseSQLiteService';

interface ParsedOrder {
  id: string;
  customerName: string;
  customer: string;
  email: string;
  phone: string;
  date: string;
  time: string;
  status: string;
  orderType: string;
  subtotal: number;
  tax: number;
  discount: number;
  total: number;
  notes: string | null;
  image: string | null;
  items: string | null;
  createdAt: string;
  updatedAt: string;
}

export class OrderSQLiteService extends BaseSQLiteService {
  constructor() {
    super({
      name: 'tailorza.db',
      version: 2,
      enableLogging: true
    });
  }

  /**
   * Get all orders with optional filters
   */
  async getOrders(filters: {
    status?: OrderStatus;
    orderType?: OrderType;
    dateFrom?: string;
    dateTo?: string;
    customerName?: string;
    search?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<Order[]> {
    await this.initialize();

    try {
      let query = 'SELECT * FROM orders WHERE 1=1';
      const params: any[] = [];

      if (filters.status) {
        query += ' AND status = ?';
        params.push(filters.status);
      }

      if (filters.orderType) {
        query += ' AND orderType = ?';
        params.push(filters.orderType);
      }

      if (filters.dateFrom) {
        query += ' AND date >= ?';
        params.push(filters.dateFrom);
      }

      if (filters.dateTo) {
        query += ' AND date <= ?';
        params.push(filters.dateTo);
      }

      if (filters.customerName) {
        query += ' AND customerName LIKE ?';
        params.push(`%${filters.customerName}%`);
      }

      if (filters.search) {
        query += ' AND (customerName LIKE ? OR email LIKE ? OR phone LIKE ?)';
        const searchTerm = `%${filters.search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      query += ' ORDER BY date DESC, time DESC';

      if (filters.limit) {
        query += ' LIMIT ?';
        params.push(filters.limit);
      }

      if (filters.offset) {
        query += ' OFFSET ?';
        params.push(filters.offset);
      }

      const rows = await this.executeQuery<ParsedOrder>(query, params);
      return rows.map((row) => this.parseOrderRow(row));
    } catch (error) {
      LoggingService.error('Failed to get orders', 'ORDER_DB', error as Error);
      throw error;
    }
  }

  /**
   * Get order by ID
   */
  async getOrderById(id: string): Promise<Order | null> {
    await this.initialize();

    try {
      const row = await this.executeSingleQuery<ParsedOrder>(
        'SELECT * FROM orders WHERE id = ?',
        [id]
      );

      return row ? this.parseOrderRow(row) : null;
    } catch (error) {
      LoggingService.error('Failed to get order by ID', 'ORDER_DB', error as Error);
      throw error;
    }
  }

  /**
   * Save order (create or update)
   */
  async saveOrder(order: Partial<Order> & { id: string; customerName: string; total: number }): Promise<Order> {
    await this.initialize();

    try {
      const now = new Date().toISOString();

      if (order.id) {
        // Update existing order
        await this.executeWrite(`
          UPDATE orders SET
            customerName = ?, customer = ?, email = ?, phone = ?, date = ?, time = ?,
            status = ?, orderType = ?, subtotal = ?, tax = ?, discount = ?, total = ?,
            notes = ?, image = ?, items = ?, updatedAt = ?
          WHERE id = ?
        `, [
          order.customerName,
          order.customer || '',
          order.email || '',
          order.phone || '',
          order.date || new Date().toISOString().split('T')[0],
          order.time || new Date().toTimeString().split(' ')[0],
          order.status || 'pending',
          order.orderType || 'takeaway',
          order.subtotal || 0,
          order.tax || 0,
          order.discount || 0,
          order.total,
          order.notes || null,
          order.image || null,
          this.stringifyJSON(order.items || []),
          now,
          order.id
        ]);

        return { ...order, updatedAt: now } as Order;
      } else {
        // Create new order
        const result = await this.executeWrite(`
          INSERT INTO orders (
            id, customerName, customer, email, phone, date, time, status, orderType,
            subtotal, tax, discount, total, notes, image, items, createdAt, updatedAt
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          order.id,
          order.customerName,
          order.customer || '',
          order.email || '',
          order.phone || '',
          order.date || new Date().toISOString().split('T')[0],
          order.time || new Date().toTimeString().split(' ')[0],
          order.status || 'pending',
          order.orderType || 'takeaway',
          order.subtotal || 0,
          order.tax || 0,
          order.discount || 0,
          order.total,
          order.notes || null,
          order.image || null,
          this.stringifyJSON(order.items || []),
          now,
          now
        ]);

        return {
          ...order,
          createdAt: now,
          updatedAt: now
        } as Order;
      }
    } catch (error) {
      LoggingService.error('Failed to save order', 'ORDER_DB', error as Error);
      throw error;
    }
  }

  /**
   * Delete order
   */
  async deleteOrder(id: string): Promise<void> {
    await this.initialize();

    try {
      await this.executeWrite('DELETE FROM orders WHERE id = ?', [id]);
      LoggingService.info(`Order deleted: ${id}`, 'ORDER_DB');
    } catch (error) {
      LoggingService.error('Failed to delete order', 'ORDER_DB', error as Error);
      throw error;
    }
  }

  /**
   * Update order status
   */
  async updateOrderStatus(id: string, status: OrderStatus): Promise<void> {
    await this.initialize();

    try {
      await this.executeWrite(`
        UPDATE orders SET status = ?, updatedAt = ? WHERE id = ?
      `, [status, new Date().toISOString(), id]);
    } catch (error) {
      LoggingService.error('Failed to update order status', 'ORDER_DB', error as Error);
      throw error;
    }
  }

  /**
   * Get orders by status
   */
  async getOrdersByStatus(status: OrderStatus): Promise<Order[]> {
    await this.initialize();

    try {
      const rows = await this.executeQuery<ParsedOrder>(
        'SELECT * FROM orders WHERE status = ? ORDER BY date DESC, time DESC',
        [status]
      );
      
      return rows.map((row) => this.parseOrderRow(row));
    } catch (error) {
      LoggingService.error('Failed to get orders by status', 'ORDER_DB', error as Error);
      throw error;
    }
  }

  /**
   * Get orders by customer
   */
  async getOrdersByCustomer(customerId: string): Promise<Order[]> {
    await this.initialize();

    try {
      const rows = await this.executeQuery<ParsedOrder>(
        'SELECT * FROM orders WHERE customer = ? ORDER BY date DESC, time DESC',
        [customerId]
      );
      
      return rows.map((row) => this.parseOrderRow(row));
    } catch (error) {
      LoggingService.error('Failed to get orders by customer', 'ORDER_DB', error as Error);
      throw error;
    }
  }

  /**
   * Get orders for today
   */
  async getTodayOrders(): Promise<Order[]> {
    await this.initialize();

    try {
      const today = new Date().toISOString().split('T')[0];
      const rows = await this.executeQuery<ParsedOrder>(
        'SELECT * FROM orders WHERE date = ? ORDER BY time DESC',
        [today]
      );
      
      return rows.map((row) => this.parseOrderRow(row));
    } catch (error) {
      LoggingService.error('Failed to get today orders', 'ORDER_DB', error as Error);
      throw error;
    }
  }

  /**
   * Get order statistics
   */
  async getOrderStats(dateFrom?: string, dateTo?: string): Promise<{
    totalOrders: number;
    totalRevenue: number;
    ordersByStatus: Array<{ status: OrderStatus; count: number; revenue: number }>;
    ordersByType: Array<{ type: OrderType; count: number; revenue: number }>;
    averageOrderValue: number;
  }> {
    await this.initialize();

    try {
      let query = 'SELECT * FROM orders WHERE 1=1';
      const params: any[] = [];

      if (dateFrom) {
        query += ' AND date >= ?';
        params.push(dateFrom);
      }

      if (dateTo) {
        query += ' AND date <= ?';
        params.push(dateTo);
      }

      const orders = await this.executeQuery<ParsedOrder>(query, params);
      const parsedOrders = orders.map((order) => this.parseOrderRow(order));

      const totalOrders = parsedOrders.length;
      const totalRevenue = parsedOrders.reduce((sum, order) => sum + order.total, 0);
      const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

      // Group by status
      const statusCounts: { [key in OrderStatus]?: { count: number; revenue: number } } = {};
      for (const order of parsedOrders) {
        if (!statusCounts[order.status]) {
          statusCounts[order.status] = { count: 0, revenue: 0 };
        }
        statusCounts[order.status]!.count++;
        statusCounts[order.status]!.revenue += order.total;
      }

      const ordersByStatus = Object.entries(statusCounts).map(([status, data]) => ({
        status: status as OrderStatus,
        count: data.count,
        revenue: data.revenue
      }));

      // Group by type
      const typeCounts: { [key in OrderType]?: { count: number; revenue: number } } = {};
      for (const order of parsedOrders) {
        if (!typeCounts[order.orderType]) {
          typeCounts[order.orderType] = { count: 0, revenue: 0 };
        }
        typeCounts[order.orderType]!.count++;
        typeCounts[order.orderType]!.revenue += order.total;
      }

      const ordersByType = Object.entries(typeCounts).map(([type, data]) => ({
        type: type as OrderType,
        count: data.count,
        revenue: data.revenue
      }));

      return {
        totalOrders,
        totalRevenue,
        ordersByStatus,
        ordersByType,
        averageOrderValue
      };
    } catch (error) {
      LoggingService.error('Failed to get order stats', 'ORDER_DB', error as Error);
      throw error;
    }
  }

  /**
   * Parse order row from database
   */
  private parseOrderRow(row: ParsedOrder): Order {
    let items: OrderItem[] = [];
    
    try {
      if (row.items) {
        items = this.parseJSON(row.items, []);
      }
    } catch (error) {
      LoggingService.warn('Failed to parse order items', 'ORDER_DB', error as Error);
      items = [];
    }

    return {
      id: row.id,
      customerName: row.customerName,
      customer: row.customer,
      email: row.email,
      phone: row.phone,
      date: row.date,
      time: row.time,
      status: row.status as OrderStatus,
      orderType: row.orderType as OrderType,
      subtotal: row.subtotal,
      tax: row.tax,
      discount: row.discount,
      total: row.total,
      notes: row.notes || undefined,
      image: row.image || undefined,
      items,
      createdAt: row.createdAt,
      updatedAt: row.updatedAt
    };
  }
}

// Export singleton instance
export const orderSQLiteService = new OrderSQLiteService(); 