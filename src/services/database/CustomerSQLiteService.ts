/**
 * Customer SQLite Service for Customer Database Operations
 * Handles all customer-related database operations
 */

import { Customer } from '../../types';
import LoggingService from '../LoggingService';

import { BaseSQLiteService } from './BaseSQLiteService';

interface ParsedCustomer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  totalOrders: number;
  totalSpent: number;
  lastOrderDate: string | null;
  isActive: number;
  createdAt: string;
  updatedAt: string;
}

export class CustomerSQLiteService extends BaseSQLiteService {
  constructor() {
    super({
      name: 'tailorza.db',
      version: 2,
      enableLogging: true
    });
  }

  /**
   * Get all customers with optional filters
   */
  async getCustomers(filters: {
    isVIP?: boolean;
    search?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<Customer[]> {
    await this.initialize();

    try {
      let query = 'SELECT * FROM customers WHERE 1=1';
      const params: any[] = [];

      if (filters.isVIP !== undefined) {
        // Note: isVIP is not a column in the customers table
        // This filter is ignored for now
        LoggingService.warn('isVIP filter not supported in current schema', 'CUSTOMER_DB');
      }

      if (filters.search) {
        query += ' AND (name LIKE ? OR email LIKE ? OR phone LIKE ?)';
        const searchTerm = `%${filters.search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      query += ' ORDER BY name ASC';

      if (filters.limit) {
        query += ' LIMIT ?';
        params.push(filters.limit);
      }

      if (filters.offset) {
        query += ' OFFSET ?';
        params.push(filters.offset);
      }

      const rows = await this.executeQuery<ParsedCustomer>(query, params);
      return rows.map((row) => this.parseCustomerRow(row));
    } catch (error) {
      LoggingService.error('Failed to get customers', 'CUSTOMER_DB', error as Error);
      throw error;
    }
  }

  /**
   * Get customer by ID
   */
  async getCustomerById(id: string): Promise<Customer | null> {
    await this.initialize();

    try {
      const row = await this.executeSingleQuery<ParsedCustomer>(
        'SELECT * FROM customers WHERE id = ?',
        [id]
      );

      return row ? this.parseCustomerRow(row) : null;
    } catch (error) {
      LoggingService.error('Failed to get customer by ID', 'CUSTOMER_DB', error as Error);
      throw error;
    }
  }

  /**
   * Save customer (create or update)
   */
  async saveCustomer(customer: Partial<Customer> & { name: string }): Promise<Customer> {
    await this.initialize();

    try {
      const now = new Date().toISOString();

      if (customer.id) {
        // Update existing customer
        await this.executeWrite(`
          UPDATE customers SET
            name = ?, email = ?, phone = ?, address = ?,
            totalOrders = ?, totalSpent = ?, lastOrderDate = ?, isActive = ?, updatedAt = ?
          WHERE id = ?
        `, [
          customer.name,
          customer.email || '',
          customer.phone || '',
          customer.address || '',
          customer.totalOrders || 0,
          customer.totalSpent || 0,
          customer.lastOrderDate || null,
          this.boolToInt(customer.isActive || true),
          now,
          customer.id
        ]);

        return { ...customer, updatedAt: now } as Customer;
      } else {
        // Create new customer
        const id = this.generateId('customer');
        const result = await this.executeWrite(`
          INSERT INTO customers (
            id, name, email, phone, address, totalOrders, totalSpent, lastOrderDate, isActive, createdAt, updatedAt
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          id,
          customer.name,
          customer.email || '',
          customer.phone || '',
          customer.address || '',
          customer.totalOrders || 0,
          customer.totalSpent || 0,
          customer.lastOrderDate || null,
          this.boolToInt(customer.isActive || true),
          now,
          now
        ]);

        return {
          ...customer,
          id,
          createdAt: now,
          updatedAt: now
        } as Customer;
      }
    } catch (error) {
      LoggingService.error('Failed to save customer', 'CUSTOMER_DB', error as Error);
      throw error;
    }
  }

  /**
   * Delete customer
   */
  async deleteCustomer(id: string): Promise<void> {
    await this.initialize();

    try {
      await this.executeWrite('DELETE FROM customers WHERE id = ?', [id]);
      LoggingService.info(`Customer deleted: ${id}`, 'CUSTOMER_DB');
    } catch (error) {
      LoggingService.error('Failed to delete customer', 'CUSTOMER_DB', error as Error);
      throw error;
    }
  }

  /**
   * Update customer statistics
   */
  async updateCustomerStats(id: string, stats: {
    totalOrders?: number;
    totalSpent?: number;
  }): Promise<void> {
    await this.initialize();

    try {
      const updateFields: string[] = [];
      const params: any[] = [];

      if (stats.totalOrders !== undefined) {
        updateFields.push('totalOrders = ?');
        params.push(stats.totalOrders);
      }

      if (stats.totalSpent !== undefined) {
        updateFields.push('totalSpent = ?');
        params.push(stats.totalSpent);
      }

      if (updateFields.length > 0) {
        updateFields.push('updatedAt = ?');
        params.push(new Date().toISOString());
        params.push(id);

        await this.executeWrite(`
          UPDATE customers SET ${updateFields.join(', ')} WHERE id = ?
        `, params);
      }
    } catch (error) {
      LoggingService.error('Failed to update customer stats', 'CUSTOMER_DB', error as Error);
      throw error;
    }
  }

  /**
   * Search customers by name, email, or phone
   */
  async searchCustomers(searchTerm: string, limit: number = 10): Promise<Customer[]> {
    await this.initialize();

    try {
      const query = `
        SELECT * FROM customers 
        WHERE name LIKE ? OR email LIKE ? OR phone LIKE ?
        ORDER BY name ASC
        LIMIT ?
      `;
      const searchPattern = `%${searchTerm}%`;
      const rows = await this.executeQuery<ParsedCustomer>(query, [searchPattern, searchPattern, searchPattern, limit]);
      
      return rows.map((row) => this.parseCustomerRow(row));
    } catch (error) {
      LoggingService.error('Failed to search customers', 'CUSTOMER_DB', error as Error);
      throw error;
    }
  }

  /**
   * Get VIP customers (customers with high total spent)
   */
  async getVIPCustomers(): Promise<Customer[]> {
    await this.initialize();

    try {
      // Since there's no isVIP column, we'll consider customers with high total spent as VIP
      const rows = await this.executeQuery<ParsedCustomer>(
        'SELECT * FROM customers WHERE totalSpent > 1000 ORDER BY totalSpent DESC'
      );
      
      return rows.map((row) => this.parseCustomerRow(row));
    } catch (error) {
      LoggingService.error('Failed to get VIP customers', 'CUSTOMER_DB', error as Error);
      throw error;
    }
  }

  /**
   * Parse customer row from database
   */
  private parseCustomerRow(row: ParsedCustomer): Customer {
    return {
      id: row.id,
      name: row.name,
      email: row.email,
      phone: row.phone,
      address: row.address,
      totalOrders: row.totalOrders,
      totalSpent: row.totalSpent,
      lastOrderDate: row.lastOrderDate || undefined,
      isActive: this.intToBool(row.isActive),
      createdAt: row.createdAt,
      updatedAt: row.updatedAt
    };
  }
}

// Export singleton instance
export const customerSQLiteService = new CustomerSQLiteService(); 