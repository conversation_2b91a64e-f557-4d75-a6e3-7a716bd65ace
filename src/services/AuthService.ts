import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

import LoggingService from './LoggingService';
import SecurityService from './SecurityService';

/**
 * AuthService - Comprehensive authentication and session management
 * 
 * Features:
 * - Session management with secure tokens
 * - Automatic session expiration
 * - Secure storage of authentication data
 * - Session validation and refresh
 * - Multi-device session tracking
 * - Comprehensive audit logging
 * 
 * @class AuthService
 * @version 1.0.0
 */

export interface UserSession {
  userId: string;
  username: string;
  email?: string;
  role: 'admin' | 'user' | 'manager';
  sessionId: string;
  accessToken: string;
  refreshToken?: string;
  expiresAt: number;
  createdAt: number;
  lastActivity: number;
  deviceInfo: {
    platform: string;
    deviceId: string;
    appVersion: string;
  };
  permissions: string[];
}

export interface LoginCredentials {
  username: string;
  password: string;
  rememberMe?: boolean;
}

export interface SessionValidationResult {
  isValid: boolean;
  session?: UserSession;
  reason?: 'expired' | 'invalid' | 'not_found' | 'revoked';
}

class AuthService {
  private static instance: AuthService;
  private currentSession: UserSession | null = null;
  private sessionCheckInterval: ReturnType<typeof setInterval> | null = null;
  private readonly SESSION_KEY = 'user_session';
  private readonly REFRESH_TOKEN_KEY = 'refresh_token';
  private readonly SESSION_DURATION = 24 * 60 * 60 * 1000; // 24 hours
  private readonly REFRESH_THRESHOLD = 2 * 60 * 60 * 1000; // 2 hours before expiry

  private constructor() {
    this.initializeSessionMonitoring();
  }

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * Initialize session monitoring
   */
  private initializeSessionMonitoring(): void {
    // Check session validity every 5 minutes
    this.sessionCheckInterval = setInterval(() => {
      this.validateCurrentSession();
    }, 5 * 60 * 1000);

    LoggingService.info('Session monitoring initialized', 'AUTH');
  }

  /**
   * Generate a secure session ID
   */
  private generateSessionId(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2);
    return `session_${timestamp}_${random}`;
  }

  /**
   * Generate device information
   */
  private getDeviceInfo(): UserSession['deviceInfo'] {
    return {
      platform: Platform.OS,
      deviceId: `${Platform.OS}_${Date.now()}`, // In real app, use device-specific ID
      appVersion: '1.0.0', // Should come from app config
    };
  }

  /**
   * Create a new user session
   */
  private async createSession(
    userId: string,
    username: string,
    email?: string,
    role: UserSession['role'] = 'user'
  ): Promise<UserSession> {
    const now = Date.now();
    const sessionId = this.generateSessionId();
    const accessToken = SecurityService.encrypt(`${userId}_${sessionId}_${now}`);
    
    const session: UserSession = {
      userId,
      username,
      email,
      role,
      sessionId,
      accessToken,
      expiresAt: now + this.SESSION_DURATION,
      createdAt: now,
      lastActivity: now,
      deviceInfo: this.getDeviceInfo(),
      permissions: this.getDefaultPermissions(role),
    };

    // Store session securely
    await this.storeSession(session);
    this.currentSession = session;

    LoggingService.info(`Session created for user: ${username}`, 'AUTH', {
      userId,
      sessionId,
      role,
      expiresAt: new Date(session.expiresAt).toISOString(),
    });

    SecurityService.auditLog('SESSION_CREATED', {
      userId,
      username,
      sessionId,
      deviceInfo: session.deviceInfo,
    });

    return session;
  }

  /**
   * Get default permissions based on role
   */
  private getDefaultPermissions(role: UserSession['role']): string[] {
    const permissions = {
      admin: [
        'read:all',
        'write:all',
        'delete:all',
        'manage:users',
        'manage:settings',
        'view:analytics',
        'export:data',
      ],
      manager: [
        'read:all',
        'write:orders',
        'write:products',
        'write:customers',
        'view:analytics',
        'export:data',
      ],
      user: [
        'read:orders',
        'read:products',
        'read:customers',
        'write:orders',
      ],
    };

    return permissions[role] || permissions.user;
  }

  /**
   * Store session securely
   */
  private async storeSession(session: UserSession): Promise<void> {
    try {
      const encryptedSession = SecurityService.encrypt(JSON.stringify(session));
      await SecurityService.secureStore(this.SESSION_KEY, encryptedSession);
      
      if (session.refreshToken) {
        await SecurityService.secureStore(this.REFRESH_TOKEN_KEY, session.refreshToken);
      }

      LoggingService.info('Session stored securely', 'AUTH');
    } catch (error) {
      LoggingService.error('Failed to store session', 'AUTH', error as Error);
      throw new Error('Failed to store session securely');
    }
  }

  /**
   * Retrieve stored session
   */
  private async retrieveStoredSession(): Promise<UserSession | null> {
    try {
      const encryptedSession = await SecurityService.secureRetrieve(this.SESSION_KEY);
      if (!encryptedSession) {
        return null;
      }

      const sessionData = SecurityService.decrypt(encryptedSession);
      const session: UserSession = JSON.parse(sessionData);

      LoggingService.info('Session retrieved from storage', 'AUTH');
      return session;
    } catch (error) {
      LoggingService.error('Failed to retrieve session', 'AUTH', error as Error);
      return null;
    }
  }

  /**
   * Validate session
   */
  private validateSession(session: UserSession): SessionValidationResult {
    const now = Date.now();

    // Check if session is expired
    if (session.expiresAt <= now) {
      return {
        isValid: false,
        reason: 'expired',
      };
    }

    // Check if session is too old (security measure)
    const maxSessionAge = 7 * 24 * 60 * 60 * 1000; // 7 days
    if (now - session.createdAt > maxSessionAge) {
      return {
        isValid: false,
        reason: 'expired',
      };
    }

    // Session is valid
    return {
      isValid: true,
      session,
    };
  }

  /**
   * Update session activity
   */
  private async updateSessionActivity(session: UserSession): Promise<void> {
    session.lastActivity = Date.now();
    await this.storeSession(session);
    this.currentSession = session;
  }

  /**
   * Login user with credentials
   */
  public async login(credentials: LoginCredentials): Promise<UserSession> {
    try {
      LoggingService.info(`Login attempt for user: ${credentials.username}`, 'AUTH');

      // In a real app, this would validate against a backend
      // For now, we'll simulate authentication
      const isValidCredentials = await this.validateCredentials(credentials);
      
      if (!isValidCredentials) {
        SecurityService.auditLog('LOGIN_FAILED', {
          username: credentials.username,
          reason: 'invalid_credentials',
        });
        throw new Error('Invalid credentials');
      }

      // Create session for authenticated user
      const session = await this.createSession(
        `user_${Date.now()}`, // In real app, this would come from backend
        credentials.username,
        `${credentials.username}@example.com`, // In real app, this would come from backend
        'user' // In real app, this would come from backend
      );

      LoggingService.info(`Login successful for user: ${credentials.username}`, 'AUTH');
      SecurityService.auditLog('LOGIN_SUCCESS', {
        userId: session.userId,
        username: session.username,
        sessionId: session.sessionId,
      });

      return session;
    } catch (error) {
      LoggingService.error('Login failed', 'AUTH', error as Error);
      throw error;
    }
  }

  /**
   * Validate credentials (mock implementation)
   */
  private async validateCredentials(credentials: LoginCredentials): Promise<boolean> {
    // Mock validation - in real app, this would call backend API
    // For demo purposes, accept any non-empty credentials
    return credentials.username.length > 0 && credentials.password.length > 0;
  }

  /**
   * Logout user and clear session
   */
  public async logout(): Promise<void> {
    try {
      const currentSession = this.currentSession || await this.retrieveStoredSession();
      
      if (currentSession) {
        LoggingService.info(`Logout initiated for user: ${currentSession.username}`, 'AUTH');
        
        SecurityService.auditLog('LOGOUT_INITIATED', {
          userId: currentSession.userId,
          username: currentSession.username,
          sessionId: currentSession.sessionId,
          sessionDuration: Date.now() - currentSession.createdAt,
        });
      }

      // Clear all session data
      await this.clearSessionData();
      
      // Clear current session
      this.currentSession = null;

      LoggingService.info('Logout completed successfully', 'AUTH');
      
      if (currentSession) {
        SecurityService.auditLog('LOGOUT_SUCCESS', {
          userId: currentSession.userId,
          username: currentSession.username,
          sessionId: currentSession.sessionId,
        });
      }
    } catch (error) {
      LoggingService.error('Logout failed', 'AUTH', error as Error);
      throw new Error('Failed to logout completely');
    }
  }

  /**
   * Clear all session data
   */
  private async clearSessionData(): Promise<void> {
    try {
      // Remove session data from secure storage
      await AsyncStorage.multiRemove([
        this.SESSION_KEY,
        this.REFRESH_TOKEN_KEY,
        'auth_token',
        'user_preferences',
        'temp_session_data',
      ]);

      // Clear any cached authentication data
      await SecurityService.secureStore(this.SESSION_KEY, '');
      await SecurityService.secureStore(this.REFRESH_TOKEN_KEY, '');

      LoggingService.info('Session data cleared successfully', 'AUTH');
    } catch (error) {
      LoggingService.error('Failed to clear session data', 'AUTH', error as Error);
      throw error;
    }
  }

  /**
   * Get current session
   */
  public async getCurrentSession(): Promise<UserSession | null> {
    try {
      // Return cached session if available and valid
      if (this.currentSession) {
        const validation = this.validateSession(this.currentSession);
        if (validation.isValid) {
          await this.updateSessionActivity(this.currentSession);
          return this.currentSession;
        }
      }

      // Try to retrieve session from storage
      const storedSession = await this.retrieveStoredSession();
      if (storedSession) {
        const validation = this.validateSession(storedSession);
        if (validation.isValid) {
          await this.updateSessionActivity(storedSession);
          return storedSession;
        } else {
          // Session is invalid, clear it
          await this.clearSessionData();
          LoggingService.warn(`Session invalid: ${validation.reason}`, 'AUTH');
        }
      }

      return null;
    } catch (error) {
      LoggingService.error('Failed to get current session', 'AUTH', error as Error);
      return null;
    }
  }

  /**
   * Check if user is authenticated
   */
  public async isAuthenticated(): Promise<boolean> {
    const session = await this.getCurrentSession();
    return session !== null;
  }

  /**
   * Validate current session
   */
  private async validateCurrentSession(): Promise<void> {
    try {
      const session = await this.getCurrentSession();
      if (!session) {
        LoggingService.info('No valid session found during validation', 'AUTH');
        return;
      }

      // Check if session needs refresh
      const now = Date.now();
      const timeUntilExpiry = session.expiresAt - now;
      
      if (timeUntilExpiry <= this.REFRESH_THRESHOLD) {
        LoggingService.info('Session approaching expiry, attempting refresh', 'AUTH');
        await this.refreshSession(session);
      }
    } catch (error) {
      LoggingService.error('Session validation failed', 'AUTH', error as Error);
    }
  }

  /**
   * Refresh session
   */
  private async refreshSession(session: UserSession): Promise<UserSession> {
    try {
      // Extend session expiry
      const now = Date.now();
      session.expiresAt = now + this.SESSION_DURATION;
      session.lastActivity = now;
      
      // Generate new access token
      session.accessToken = SecurityService.encrypt(`${session.userId}_${session.sessionId}_${now}`);
      
      await this.storeSession(session);
      this.currentSession = session;

      LoggingService.info(`Session refreshed for user: ${session.username}`, 'AUTH');
      SecurityService.auditLog('SESSION_REFRESHED', {
        userId: session.userId,
        sessionId: session.sessionId,
        newExpiresAt: new Date(session.expiresAt).toISOString(),
      });

      return session;
    } catch (error) {
      LoggingService.error('Session refresh failed', 'AUTH', error as Error);
      throw error;
    }
  }

  /**
   * Force logout (for security purposes)
   */
  public async forceLogout(reason: string = 'security'): Promise<void> {
    try {
      const currentSession = this.currentSession || await this.retrieveStoredSession();
      
      if (currentSession) {
        SecurityService.auditLog('FORCE_LOGOUT', {
          userId: currentSession.userId,
          username: currentSession.username,
          sessionId: currentSession.sessionId,
          reason,
        });
      }

      await this.logout();
      LoggingService.warn(`Force logout executed: ${reason}`, 'AUTH');
    } catch (error) {
      LoggingService.error('Force logout failed', 'AUTH', error as Error);
      throw error;
    }
  }

  /**
   * Get session info for debugging
   */
  public async getSessionInfo(): Promise<{
    isAuthenticated: boolean;
    session?: Partial<UserSession>;
    timeUntilExpiry?: number;
  }> {
    try {
      const session = await this.getCurrentSession();
      
      if (!session) {
        return { isAuthenticated: false };
      }

      const now = Date.now();
      return {
        isAuthenticated: true,
        session: {
          userId: session.userId,
          username: session.username,
          role: session.role,
          sessionId: session.sessionId,
          createdAt: session.createdAt,
          lastActivity: session.lastActivity,
          expiresAt: session.expiresAt,
          deviceInfo: session.deviceInfo,
        },
        timeUntilExpiry: session.expiresAt - now,
      };
    } catch (error) {
      LoggingService.error('Failed to get session info', 'AUTH', error as Error);
      return { isAuthenticated: false };
    }
  }

  /**
   * Cleanup on app termination
   */
  public cleanup(): void {
    if (this.sessionCheckInterval) {
      clearInterval(this.sessionCheckInterval);
      this.sessionCheckInterval = null;
    }
    LoggingService.info('AuthService cleanup completed', 'AUTH');
  }
}

export default AuthService.getInstance();