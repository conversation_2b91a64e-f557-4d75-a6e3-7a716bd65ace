import AsyncStorage from '@react-native-async-storage/async-storage';


import { STORAGE_KEYS } from '../config/constants';
import { StorageError } from '../utils/errorHandler';

import LoggingService from './LoggingService';

// Storage interfaces
interface StorageInfo {
  totalKeys: number;
  totalSize: number;
  keyInfo: Array<{
    key: string;
    size: number;
  }>;
  cacheSize: number;
}

interface BackupData {
  data: any;
  timestamp: number;
  version: string;
}

/**
 * Enhanced storage service with error handling and caching
 */
export class StorageService {
  private static cache = new Map<string, any>();
  private static cacheExpiry = new Map<string, number>();
  private static defaultTTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Get data from storage with caching
   */
  static async get<T = any>(key: string, useCache: boolean = true, ttl: number = this.defaultTTL): Promise<T | null> {
    try {
      // Check cache first
      if (useCache && this.cache.has(key)) {
        const expiry = this.cacheExpiry.get(key);
        if (expiry && Date.now() < expiry) {
          LoggingService.debug(`Cache hit for key: ${key}`, 'STORAGE');
          return this.cache.get(key);
        } else {
          // Cache expired
          this.cache.delete(key);
          this.cacheExpiry.delete(key);
        }
      }

      const value = await AsyncStorage.getItem(key);
      const parsedValue: T | null = value ? JSON.parse(value) : null;

      // Update cache
      if (useCache && parsedValue !== null) {
        this.cache.set(key, parsedValue);
        this.cacheExpiry.set(key, Date.now() + ttl);
      }

      LoggingService.debug(`Storage get for key: ${key}`, 'STORAGE', { found: parsedValue !== null });
      return parsedValue;
    } catch (error) {
      LoggingService.error(`Failed to get data for key: ${key}`, 'STORAGE', error as Error);
      throw new StorageError(`Failed to retrieve data: ${key}`);
    }
  }

  /**
   * Set data to storage with caching
   */
  static async set(key: string, value: any, updateCache: boolean = true): Promise<boolean> {
    try {
      const serializedValue = JSON.stringify(value);
      await AsyncStorage.setItem(key, serializedValue);

      // Update cache
      if (updateCache) {
        this.cache.set(key, value);
        this.cacheExpiry.set(key, Date.now() + this.defaultTTL);
      }

      // console.log(`Storage set for key: ${key}`, null);
      return true;
    } catch (error) {
      // console.error(`Failed to set data for key: ${key}`, error as any);
      throw new StorageError(`Failed to save data: ${key}`);
    }
  }

  /**
   * Remove data from storage and cache
   */
  static async remove(key: string): Promise<boolean> {
    try {
      await AsyncStorage.removeItem(key);
      this.cache.delete(key);
      this.cacheExpiry.delete(key);
      
      // console.log(`Storage remove for key: ${key}`, null);
      return true;
    } catch (error) {
      // console.error(`Failed to remove data for key: ${key}`, error as any);
      throw new StorageError(`Failed to remove data: ${key}`);
    }
  }

  /**
   * Clear all storage and cache
   */
  static async clear(): Promise<boolean> {
    try {
      await AsyncStorage.clear();
      this.cache.clear();
      this.cacheExpiry.clear();
      
      // console.info('Storage cleared', null);
      return true;
    } catch (error) {
      // console.error('Failed to clear storage', error as any);
      throw new StorageError('Failed to clear storage');
    }
  }

  /**
   * Get multiple keys at once
   */
  static async getMultiple(keys: string[]): Promise<Record<string, any>> {
    try {
      const results = await AsyncStorage.multiGet(keys);
      const data: Record<string, any> = {};
      
      results.forEach(([key, value]) => {
        data[key] = value ? JSON.parse(value) : null;
      });

      // console.log(`Storage getMultiple for keys:`, keys as any);
      return data;
    } catch (error) {
      // console.error('Failed to get multiple keys', error as any);
      throw new StorageError('Failed to retrieve multiple data');
    }
  }

  /**
   * Set multiple key-value pairs at once
   */
  static async setMultiple(keyValuePairs: Array<[string, any]>): Promise<boolean> {
    try {
      const serializedPairs: Array<[string, string]> = keyValuePairs.map(([key, value]) => [
        key,
        JSON.stringify(value)
      ]);
      
      await AsyncStorage.multiSet(serializedPairs);
      
      // Update cache
      keyValuePairs.forEach(([key, value]) => {
        this.cache.set(key, value);
        this.cacheExpiry.set(key, Date.now() + this.defaultTTL);
      });

      // console.log('Storage setMultiple completed', null);
      return true;
    } catch (error) {
      // console.error('Failed to set multiple keys', error as any);
      throw new StorageError('Failed to save multiple data');
    }
  }

  /**
   * Check if key exists
   */
  static async exists(key: string): Promise<boolean> {
    try {
      const value = await AsyncStorage.getItem(key);
      return value !== null;
    } catch (error) {
      // console.error(`Failed to check existence for key: ${key}`, error as any);
      return false;
    }
  }

  /**
   * Get storage size information
   */
  static async getStorageInfo(): Promise<StorageInfo> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const values = await AsyncStorage.multiGet(keys);
      
      let totalSize = 0;
      const keyInfo = values.map(([key, value]) => {
        const size = value ? value.length : 0;
        totalSize += size;
        return { key, size };
      });

      return {
        totalKeys: keys.length,
        totalSize,
        keyInfo,
        cacheSize: this.cache.size,
      };
    } catch (error) {
      // console.error('Failed to get storage info', error as any);
      throw new StorageError('Failed to get storage information');
    }
  }

  /**
   * Clear expired cache entries
   */
  static clearExpiredCache(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    this.cacheExpiry.forEach((expiry, key) => {
      if (expiry < now) {
        expiredKeys.push(key);
      }
    });

    expiredKeys.forEach(key => {
      this.cache.delete(key);
      this.cacheExpiry.delete(key);
    });

    if (expiredKeys.length > 0) {
      // console.log(`Cleared ${expiredKeys.length} expired cache entries`, null);
    }
  }

  /**
   * Backup data to a specific key
   */
  static async backup(sourceKey: string, backupKey: string): Promise<boolean> {
    try {
      const data = await this.get(sourceKey, false);
      if (data) {
        const backupData: BackupData = {
          data,
          timestamp: Date.now(),
          version: '1.0'
        };
        await this.set(backupKey, backupData, false);
        // console.info(`Backup created: ${sourceKey} -> ${backupKey}`, null);
        return true;
      }
      return false;
    } catch (error) {
      // console.error(`Failed to backup ${sourceKey}`, error as any);
      throw new StorageError(`Failed to create backup`);
    }
  }

  /**
   * Restore data from backup
   */
  static async restore(backupKey: string, targetKey: string): Promise<boolean> {
    try {
      const backup = await this.get<BackupData>(backupKey, false);
      if (backup && backup.data) {
        await this.set(targetKey, backup.data, false);
        // console.info(`Data restored: ${backupKey} -> ${targetKey}`, null);
        return true;
      }
      return false;
    } catch (error) {
      // console.error(`Failed to restore from ${backupKey}`, error as any);
      throw new StorageError(`Failed to restore backup`);
    }
  }
}

// Convenience methods for specific data types
export const BakeryStorage = {
  async getBakeryData(): Promise<any> {
    return StorageService.get(STORAGE_KEYS.BAKERY_DATA);
  },

  async setBakeryData(data: any): Promise<boolean> {
    return StorageService.set(STORAGE_KEYS.BAKERY_DATA, data);
  },

  async getSettings(): Promise<any> {
    return StorageService.get(STORAGE_KEYS.SETTINGS);
  },

  async setSettings(settings: any): Promise<boolean> {
    return StorageService.set(STORAGE_KEYS.SETTINGS, settings);
  },

  async getTheme(): Promise<boolean | null> {
    return StorageService.get<boolean>(STORAGE_KEYS.THEME);
  },

  async setTheme(isDarkMode: boolean): Promise<boolean> {
    return StorageService.set(STORAGE_KEYS.THEME, isDarkMode);
  },
};