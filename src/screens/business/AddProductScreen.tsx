import { useNavigation } from '@react-navigation/native';
import React, { useState } from 'react';
import { ScrollView, View, StyleSheet, Alert, Image, TouchableOpacity } from 'react-native';
import {
  Text,
  Surface,
  Chip,
  Menu,
} from 'react-native-paper';


import ImagePicker from '../../components/forms/ImagePicker';
import AppBar from '../../components/navigation/AppBar';
import Button from '../../components/ui/Button';
import Dropdown from '../../components/ui/Dropdown';
import Switch from '../../components/ui/Switch';
import TextInput from '../../components/ui/TextInput';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import navigationService from '../../services/NavigationService';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY } from '../../theme/theme';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

interface AddProductScreenProps {
  route?: {
    params?: {
      product?: any;
      barcode?: string;
    };
  };
}

interface ProductData {
  name: string;
  description: string;
  price: string;
  cost: string;
  category: string;
  stock: string;
  sku: string;
  barcode: string;
  images: string[];
  isActive: boolean;
  isFeatured: boolean;
  tags: string[];
}

const AddProductScreen: React.FC<AddProductScreenProps> = ({ route }) => {
  const theme = useTheme();
  const { actions } = useData();
  const navigation = useNavigation();

  // Get product from route params for editing
  const editingProduct = route?.params?.product;
  const isEditing = !!editingProduct;

  // Product form state
  const [productData, setProductData] = useState<ProductData>({
    name: editingProduct?.name || '',
    description: editingProduct?.description || '',
    price: editingProduct?.price?.toString() || '',
    cost: editingProduct?.cost?.toString() || '',
    category: editingProduct?.category || '',
    stock: editingProduct?.stock?.toString() || '',
    sku: editingProduct?.sku || '',
    barcode: editingProduct?.barcode || route?.params?.barcode || '',
    images: editingProduct?.images || editingProduct?.image ? [editingProduct.image] : [],
    isActive: editingProduct?.isActive ?? true,
    isFeatured: editingProduct?.isFeatured ?? false,
    tags: editingProduct?.tags || [],
  });

  const [newTag, setNewTag] = useState<string>('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Predefined categories
  const categories = [
    'Cakes', 'Pastries', 'Bread', 'Cookies', 'Beverages', 'Desserts', 'Seasonal'
  ];

  // Auto-generate SKU and Barcode when product name changes (only for new products)
  React.useEffect(() => {
    if (!isEditing && productData.name && !productData.sku) {
      const generateSKU = (): string => {
        const prefix = productData.category ? productData.category.substring(0, 3).toUpperCase() : 'PRD';
        const timestamp = Date.now().toString().slice(-6);
        const nameCode = productData.name.replace(/[^a-zA-Z0-9]/g, '').substring(0, 3).toUpperCase();
        return `${prefix}-${nameCode}-${timestamp}`;
      };

      const generateBarcode = (): string => {
        // Generate a 13-digit EAN-13 barcode
        const timestamp = Date.now().toString();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `${timestamp.slice(-7)}${random}000`;
      };

      setProductData(prev => ({
        ...prev,
        sku: generateSKU(),
        barcode: productData.barcode || generateBarcode()
      }));
    }
  }, [productData.name, productData.category, isEditing, productData.barcode]);

  const validateNumber = (val: string): boolean => !isNaN(Number(val)) && Number(val) > 0;
  const validateNonNegative = (val: string): boolean => !isNaN(Number(val)) && Number(val) >= 0;

  const handleInputChange = (field: keyof ProductData, value: string | boolean | string[]): void => {
    setProductData(prev => ({ ...prev, [field]: value }));
    
    if (typeof value === 'string') {
      let error = '';
      if (field === 'name' && !value.trim()) error = 'Product name is required';
      if (field === 'price' && !validateNumber(value)) error = 'Enter a valid price';
      if (field === 'stock' && !validateNonNegative(value)) error = 'Stock must be 0 or more';
      if (field === 'category' && !value.trim()) error = 'Category is required';
      setErrors(prev => ({ ...prev, [field]: error }));
    }
  };

  const handleImageChange = (index: number, imageUri: string): void => {
    setProductData(prev => {
      const newImages = [...prev.images];
      if (imageUri) {
        newImages[index] = imageUri;
      } else {
        // Remove image at this index
        newImages.splice(index, 1);
      }
      return { ...prev, images: newImages };
    });
  };

  const addTag = (): void => {
    if (newTag.trim() && !productData.tags.includes(newTag.trim())) {
      setProductData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string): void => {
    setProductData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleSave = (): void => {
    try {
      const productToSave = {
        ...productData,
        price: parseFloat(productData.price),
        cost: productData.cost ? parseFloat(productData.cost) : 0,
        stock: parseInt(productData.stock),
        updatedAt: new Date().toISOString(),
      };

      if (isEditing) {
        // Update existing product
        const updatedProduct = {
          ...editingProduct,
          ...productToSave,
        };
        actions.updateProduct(updatedProduct);

        LoggingService.info('Product updated successfully', 'PRODUCT', { productId: editingProduct.id });

        Alert.alert(
          'Success',
          'Product updated successfully!',
          [
            {
              text: 'Done',
              onPress: () => navigation.goBack(),
              style: 'default'
            }
          ]
        );
      } else {
        // Add new product
        const newProduct = {
          id: Date.now().toString(),
          ...productToSave,
          images: productToSave.images || [],
          createdAt: new Date().toISOString(),
        };

        actions.addProduct(newProduct as any);

        LoggingService.info('Product added successfully', 'PRODUCT', { productName: newProduct.name });

        Alert.alert(
          'Success',
          'Product added successfully!',
          [
            {
              text: 'Add Another',
              onPress: () => {
                setProductData({
                  name: '',
                  description: '',
                  price: '',
                  cost: '',
                  category: '',
                  stock: '',
                  sku: '',
                  barcode: '',
                  images: [],
                  isActive: true,
                  isFeatured: false,
                  tags: [],
                });
              }
            },
            {
              text: 'Done',
              onPress: () => navigation.goBack(),
              style: 'default'
            }
          ]
        );
      }
    } catch (error) {
      LoggingService.error(`Failed to ${isEditing ? 'update' : 'add'} product`, 'PRODUCT', error as Error);
      Alert.alert('Error', `Failed to ${isEditing ? 'update' : 'add'} product. Please try again.`);
    }
  };

  const renderTagSection = () => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
        Tags
      </Text>

      <TextInput
        label="Add tag"
        value={newTag}
        onChangeText={setNewTag}
        onSubmitEditing={addTag}
        style={styles.tagInput}
        rightAffix="+"
        onRightPress={addTag}
        minLength={0}
        maxLength={50}
        validate={() => ''}
        error=""
        onBlur={() => {}}
      />

      <View style={styles.tagsContainer}>
        {productData.tags.map((tag, index) => (
          <Chip
            key={index}
            onClose={() => removeTag(tag)}
            style={styles.tag}
          >
            {tag}
          </Chip>
        ))}
      </View>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <AppBar
        title={isEditing ? "Edit Product" : "Add Product"}
        onBackPress={() => navigation.goBack()}
        actions={[
          {
            text: "Save",
            onPress: handleSave,
            color: theme.colors.primary,
          },
        ]}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Product Details Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Product Details
          </Text>
          
          <TextInput
            label="Product Name"
            value={productData.name}
            onChangeText={(value: string) => handleInputChange('name', value)}
            type="text"
            required
            minLength={1}
            maxLength={100}
            validate={() => ''}
            error={errors.name}
            onBlur={() => {}}
            style={styles.input}
          />

          <TextInput
            label="Description"
            value={productData.description}
            onChangeText={(value: string) => handleInputChange('description', value)}
            type="text"
            minLength={0}
            maxLength={500}
            validate={() => ''}
            error=""
            onBlur={() => {}}
            style={styles.input}
            multiline
            numberOfLines={3}
          />

          <View style={styles.row}>
            <TextInput
              label="Price"
              value={productData.price}
              onChangeText={(value: string) => handleInputChange('price', value)}
              type="number"
              required
              minLength={1}
              maxLength={10}
              validate={() => ''}
              error={errors.price}
              onBlur={() => {}}
              style={{ ...styles.input, ...styles.halfWidth }}
              rightAffix="৳"
            />

            <TextInput
              label="Cost"
              value={productData.cost}
              onChangeText={(value: string) => handleInputChange('cost', value)}
              type="number"
              minLength={0}
              maxLength={10}
              validate={() => ''}
              error={errors.cost}
              onBlur={() => {}}
              style={{ ...styles.input, ...styles.halfWidth }}
              rightAffix="৳"
            />
          </View>
        </View>

        {/* Category Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Category
          </Text>
          
          <View style={styles.categoryRow}>
            <Dropdown
              placeholder="Select Category"
              value={productData.category}
              options={categories.map(cat => ({ label: cat, value: cat }))}
              onValueChange={(value) => handleInputChange('category', value)}
              error={errors.category}
              flex={1}
              style={{ marginBottom: 0 }}
            />

            <TouchableOpacity
              style={[styles.addCategoryButton, { backgroundColor: theme.colors.primary }]}
              onPress={() => {
                Alert.prompt(
                  'Add New Category',
                  'Enter the name of the new category:',
                  [
                    { text: 'Cancel', style: 'cancel' },
                    {
                      text: 'Add',
                      onPress: (newCategory) => {
                        if (newCategory && newCategory.trim()) {
                          const trimmedCategory = newCategory.trim();
                          if (!categories.includes(trimmedCategory)) {
                            // Add to categories array
                            categories.push(trimmedCategory);
                            // Set as selected category
                            handleInputChange('category', trimmedCategory);
                            LoggingService.info('New category added', 'PRODUCT', { category: trimmedCategory });
                          } else {
                            Alert.alert('Category Exists', 'This category already exists.');
                          }
                        }
                      }
                    }
                  ],
                  'plain-text'
                );
              }}
            >
              <PhosphorIcon name="plus" size={20} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Inventory Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Inventory
          </Text>

          <View style={styles.row}>
            <TextInput
              label="Stock Quantity"
              value={productData.stock}
              onChangeText={(value: string) => handleInputChange('stock', value)}
              type="number"
              required
              minLength={0}
              maxLength={10}
              validate={() => ''}
              error={errors.stock}
              onBlur={() => {}}
              style={{ ...styles.input, ...styles.halfWidth }}
            />

            <TextInput
              label="SKU"
              value={productData.sku}
              onChangeText={(value: string) => handleInputChange('sku', value)}
              type="text"
              minLength={0}
              maxLength={50}
              validate={() => ''}
              error=""
              onBlur={() => {}}
              style={{ ...styles.input, ...styles.halfWidth }}
            />
          </View>

          <TextInput
            label="Barcode"
            value={productData.barcode}
            onChangeText={(value: string) => handleInputChange('barcode', value)}
            type="text"
            minLength={0}
            maxLength={50}
            validate={() => ''}
            error=""
            onBlur={() => {}}
            style={styles.input}
          />
        </View>

        {/* Settings Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Settings
          </Text>

          <View style={styles.switchRow}>
            <View style={styles.switchLabel}>
              <Text style={[styles.switchTitle, { color: theme.colors.onSurface }]}>Active Product</Text>
              <Text style={[styles.switchSubtitle, { color: theme.colors.onSurfaceVariant }]}>
                Available for sale
              </Text>
            </View>
            <Switch
              value={productData.isActive}
              onValueChange={(value: boolean) => handleInputChange('isActive', value)}
            />
          </View>

          <View style={styles.switchRow}>
            <View style={styles.switchLabel}>
              <Text style={[styles.switchTitle, { color: theme.colors.onSurface }]}>Featured Product</Text>
              <Text style={[styles.switchSubtitle, { color: theme.colors.onSurfaceVariant }]}>
                Highlight in recommendations
              </Text>
            </View>
            <Switch
              value={productData.isFeatured}
              onValueChange={(value: boolean) => handleInputChange('isFeatured', value)}
            />
          </View>
        </View>

        {/* Tags Section */}
        {renderTagSection()}

        {/* Image Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Product Images (Up to 3)
          </Text>
          <View style={styles.imageContainer}>
            <View style={styles.imagesContainer}>
              {[0, 1, 2].map((index) => (
                <View key={index} style={styles.imageWrapper}>
                  <ImagePicker
                    onImageSelected={(imageUri: string) => handleImageChange(index, imageUri)}
                    currentImage={productData.images[index] || null}
                    placeholder={`Image ${index + 1}`}
                    size="medium"
                    maxWidth={800}
                    maxHeight={800}
                    optimizationQuality={0.85}
                    enableOptimization={true}
                  />
                </View>
              ))}
            </View>
          </View>
        </View>

        {/* Bottom Save Button */}
        <View style={styles.bottomButtonContainer}>
          <Button
            variant="primary"
            size="md"
            onPress={handleSave}
            style={styles.bottomSaveButton}
          >
            {isEditing ? 'Update Product' : 'Save Product'}
          </Button>
        </View>

        {/* Bottom spacing */}
        <View style={{ height: 32 }} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  section: {
    marginBottom: SPACING.xl,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: TYPOGRAPHY.fontWeight.semibold,
    marginBottom: SPACING.md,
  },
  input: {
    marginBottom: SPACING.md,
  },
  row: {
    flexDirection: 'row',
    gap: SPACING.md,
  },
  halfWidth: {
    flex: 1,
  },
  errorText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    marginTop: -SPACING.xs,
    marginBottom: SPACING.sm,
  },
  categoryRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.sm,
  },
  addCategoryButton: {
    width: 56,
    height: 56,
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  switchLabel: {
    flex: 1,
  },
  switchTitle: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: TYPOGRAPHY.fontWeight.medium,
  },
  switchSubtitle: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    marginTop: 2,
  },
  tagInput: {
    marginBottom: SPACING.md,

  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.xs,
  },
  tag: {
    marginBottom: SPACING.xs,
  },
  imageContainer: {
    alignItems: 'center',
  },
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.md,
    marginTop: SPACING.sm,
  },
  imageWrapper: {
    flex: 1,
    minWidth: 100,
    maxWidth: 120,
  },
  bottomButtonContainer: {
    marginTop: SPACING.xl,
    marginBottom: SPACING.lg,
    alignItems: 'center',
  },
  bottomSaveButton: {
    width: '100%',
    alignSelf: 'center',
  },
});

export default AddProductScreen;