// @ts-nocheck
import { LinearGradient } from 'expo-linear-gradient';
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { View, ScrollView, StyleSheet, Alert, Linking, RefreshControl } from 'react-native';
import { Text, Surface, IconButton } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';


import OrderCard from '../../components/cards/OrderCard';
import Button from '../../components/ui/Button';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import navigationService from '../../services/NavigationService';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

const CustomerDetailsScreen = ({ route, navigation }) => {
  const theme = useTheme();
  const styles = useMemo(() => createStyles(theme), [theme]);
  const { state } = useData();
  const insets = useSafeAreaInsets();
  
  const [customer, setCustomer] = useState(route?.params?.customer);
  const [customerOrders, setCustomerOrders] = useState([]);
  const [customerMeasurements, setCustomerMeasurements] = useState([]);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    const focusListener = navigation.addListener('focus', () => {
      const currentCustomer = route?.params?.customer;
      if (currentCustomer && state.customers) {
        const updatedCustomer = state.customers.find(c => c.id === currentCustomer.id);
        const finalCustomer = updatedCustomer || currentCustomer;
        setCustomer(finalCustomer);

        const ordersForCustomer = (state.orders || [])
            .filter(order => String(order.customerId) === String(finalCustomer.id))
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        setCustomerOrders(ordersForCustomer);

        const measurementsForCustomer = (state.measurements || [])
            .filter(m => String(m.customerId) === String(finalCustomer.id))
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        setCustomerMeasurements(measurementsForCustomer);
      }
    });
    return focusListener;
  }, [navigation, route?.params?.customer, state.customers, state.orders, state.measurements]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  }, []);
  
  const formatCurrency = (amount) => `${Math.round(amount || 0).toLocaleString()}`;

  if (!customer) {
    return <View style={[styles.container, { backgroundColor: theme?.colors?.background }]}><Text>Loading...</Text></View>;
  }
  
  const { name, phone, email, address, createdAt, isVIP } = customer;

  const totalAmount = useMemo(() => customerOrders.reduce((sum, order) => sum + (order.total || order.totalAmount || 0), 0), [customerOrders]);
  
  const lastOrderDate = useMemo(() => {
    if (customerOrders.length === 0) return "No orders yet";
    const lastOrder = customerOrders[0];
    const dateDiff = (new Date() - new Date(lastOrder.createdAt)) / (1000 * 60 * 60 * 24);
    if (dateDiff < 30) return `${Math.floor(dateDiff)} months ago`;
    if (dateDiff < 365) return `${Math.floor(dateDiff / 30)} months ago`;
    return `${Math.floor(dateDiff / 365)} years ago`;
  }, [customerOrders]);

  const handleCall = () => phone && Linking.openURL(`tel:${phone}`);
  const handleWhatsApp = () => phone && Linking.openURL(`whatsapp://send?phone=${phone}`);
  const handleEdit = () => navigationService.navigate('AddCustomer', { customer });
  const handleDelete = () => Alert.alert("Delete Customer", `Are you sure you want to delete ${name}?`, [{ text: "Cancel"}, { text: "Delete", style: "destructive", onPress: async () => { navigation.goBack(); }}]);

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}> 
      <Surface style={[styles.appBar, { paddingTop: insets.top }]} elevation={4}>
        <View style={styles.appBarContent}>
          <View style={styles.appBarLeft}>
            <IconButton icon={() => <PhosphorIcon name="arrow-left" size={24} color={theme?.colors?.onSurface} />} onPress={() => navigation.goBack()} />
            <Text style={styles.headerTitle}>Customer profile</Text>
          </View>
          <View style={styles.appBarRight}>
            <IconButton icon={() => <PhosphorIcon name="trash" size={24} color={theme?.colors?.onSurface} />} onPress={handleDelete} />
            <IconButton icon={() => <PhosphorIcon name="pencil" size={24} color={theme?.colors?.onSurface} />} onPress={handleEdit} />
          </View>
        </View>
      </Surface>

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={handleRefresh} colors={[theme.colors.primary]} />}
        showsVerticalScrollIndicator={false}
      >
        {/* Profile Card */}
        <LinearGradient colors={["#26313a", "#1a232b"]} style={styles.profileCard}>
          <View style={styles.profileRow}>
            <View style={styles.profileMain}>
              <View style={styles.nameRow}>
                <Text style={styles.name}>{name}</Text>
                {isVIP && <View style={styles.vip}><Text style={styles.vipText}>VIP</Text></View>}
              </View>
              <Text style={styles.phone}>{phone}</Text>
              <View style={styles.metaInfoRow}>
                <Text style={styles.metaInfo}>Last order: {lastOrderDate}</Text>
                <Text style={styles.metaInfo}>Customer since: {new Date(createdAt).toLocaleDateString('en-GB', { day: 'numeric', month: 'short', year: 'numeric' })}</Text>
              </View>
            </View>
            <View style={styles.profileActions}>
              <Button
                icon="whatsapp"
                onPress={handleWhatsApp}
                variant="ghost"
                size="sm"
                fullWidth={false}
                textColor="#25D366"
                accessibilityLabel="WhatsApp"
              />
              <Button
                icon="phone"
                onPress={handleCall}
                variant="ghost"
                size="sm"
                fullWidth={false}
                textColor="#60a5fa"
                accessibilityLabel="Call"
              />
            </View>
          </View>
        </LinearGradient>

        {/* Stats Row */}
        <View style={styles.statsRow}>
          <View style={styles.statCard}>
            <Text style={styles.statValue}>{customerOrders.length}</Text>
            <Text style={styles.statLabel}>Total orders</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statValue}>{formatCurrency(totalAmount)}</Text>
            <Text style={styles.statLabel}>Total amount</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statValue}>{customerMeasurements.length}</Text>
            <Text style={styles.statLabel}>Measurements</Text>
          </View>
        </View>

        {/* Information Card */}
        <Surface style={styles.infoCard} elevation={2}>
          <Text style={styles.infoTitle}>Information</Text>
          <View style={styles.infoRow}>
            <PhosphorIcon name="envelope" size={20} color="#60a5fa" style={styles.infoIcon} />
            <Text style={styles.infoText}>{email}</Text>
          </View>
          <View style={styles.infoRow}>
            <PhosphorIcon name="map-pin" size={20} color="#60a5fa" style={styles.infoIcon} />
            <Text style={styles.infoText}>{address}</Text>
          </View>
        </Surface>

        {/* Recent Orders Section */}
        <Surface style={styles.sectionCard} elevation={2}>
          <View style={styles.ordersHeaderRow}>
            <Text style={styles.ordersSectionTitle}>Recent Orders</Text>
            <Button
              onPress={() => navigationService.navigate('CustomerOrders', { customerId: customer.id })}
              variant="ghost"
              size="sm"
              fullWidth={false}
              rounded={false}
            >
              See all
            </Button>
          </View>
          {customerOrders.length === 0 ? (
            <Text style={styles.emptyOrdersText}>No recent orders found.</Text>
          ) : (
            customerOrders.slice(0, 2).map(order => (
              <OrderCard key={order.id} order={order} onPress={() => navigationService.navigate('OrderDetails', { order })} />
            ))
          )}
          <Button
            icon="plus"
            onPress={() => navigationService.navigate('AddOrder', { customer })}
            variant="primary"
            size="lg"
            fullWidth={true}
          >
            New Order
          </Button>
        </Surface>

        {/* Recent Measurements Section */}
        <Surface style={styles.sectionCard} elevation={2}>
          <View style={styles.measurementsHeaderRow}>
            <Text style={styles.measurementsSectionTitle}>Recent Measurements</Text>
            <Button
              onPress={() => {}} // No action for now, as dropdown is not implemented
              variant="ghost"
              size="sm"
              fullWidth={false}
              rounded={false}
            >
              Shirt
            </Button>
          </View>
          {customerMeasurements.length === 0 ? (
            <Text style={styles.emptyMeasurementsText}>No measurements found.</Text>
          ) : (
            customerMeasurements.slice(0, 2).map(measurement => (
              <View key={measurement.id} style={styles.measurementCard}>
                <View style={styles.measurementCardHeader}>
                  <Text style={styles.measurementTitle}>{measurement.garmentType || 'Shirt'}</Text>
                  <View style={styles.measurementDateContainer}>
                    <Text style={styles.measurementDateLabel}>Measurement taken:</Text>
                    <Text style={styles.measurementDate}>{new Date(measurement.createdAt).toLocaleDateString('en-GB', {day:'numeric', month: 'short', year:'numeric'})}</Text>
                  </View>
                </View>
                <View style={styles.measurementGrid}>
                  {(() => {
                    let measurements = measurement.measurements;
                    if (typeof measurements === 'string') {
                      try { measurements = JSON.parse(measurements); } catch (e) { measurements = {}; }
                    }
                    if (measurements && typeof measurements === 'object' && !Array.isArray(measurements)) {
                      const entries = Object.entries(measurements).filter(([, value]) => value != null && value !== '');
                      return entries.map(([key, value]) => (
                        <View key={key} style={styles.measurementField}>
                          <View style={styles.measurementFieldInner}>
                            <Text style={styles.measurementLabel}>{key.charAt(0).toUpperCase() + key.slice(1)}</Text>
                            <Text style={styles.measurementValue}>{value}"</Text>
                          </View>
                        </View>
                      ));
                    }
                    return null;
                  })()}
                </View>
                {measurement.notes && (
                  <View style={styles.notesSection}>
                    <Text style={styles.notesTitle}>Note</Text>
                    <Text style={styles.notesText}>{measurement.notes}</Text>
                  </View>
                )}
              </View>
            ))
          )}
          <Button
            icon="plus"
            onPress={() => navigationService.navigate('AddMeasurement', { customer })}
            variant="primary"
            size="lg"
            fullWidth={true}
          >
            New Measurement
          </Button>
        </Surface>
      </ScrollView>
    </View>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: { flex: 1 },
  scrollContent: { paddingBottom: 32, paddingHorizontal: 12 },
  appBar: { backgroundColor: theme.colors.surface, shadowColor: '#000', shadowOpacity: 0.08, shadowRadius: 8, shadowOffset: { width: 0, height: 2 }, elevation: 4, zIndex: 10 },
  appBarContent: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: theme.spacing.medium, paddingBottom: theme.spacing.extraSmall, paddingTop: theme.spacing.extraSmall },
  appBarLeft: { flexDirection: 'row', alignItems: 'center' },
  appBarRight: { flexDirection: 'row' },
  headerTitle: { fontSize: 20, fontWeight: '700', marginLeft: 4 },
  profileCard: { borderRadius: 18, marginTop: 16, marginBottom: 18, padding: 18, flexDirection: 'column', elevation: 2 },
  profileRow: { flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'space-between' },
  profileMain: { flex: 1 },
  nameRow: { flexDirection: 'row', alignItems: 'center', marginBottom: 2 },
  name: { fontSize: 26, fontWeight: 'bold', color: 'white', marginRight: 8 },
  vip: { backgroundColor: '#ff9800', borderRadius: 6, paddingHorizontal: 8, paddingVertical: 2, marginLeft: 4 },
  vipText: { fontSize: 13, fontWeight: 'bold', color: 'black' },
  phone: { fontSize: 16, color: '#60a5fa', marginBottom: 6, marginTop: 2 },
  profileActions: { flexDirection: 'row', alignItems: 'center', marginLeft: 12 },
  actionButton: { marginLeft: 8 },
  circleButton: { width: 48, height: 48, borderRadius: 24, justifyContent: 'center', alignItems: 'center', elevation: 2 },
  metaInfoRow: { flexDirection: 'row', justifyContent: 'flex-start', marginTop: 8, gap: 18 },
  metaInfo: { fontSize: 13, color: 'white', opacity: 0.8 },
  statsRow: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 18, gap: 10 },
  statCard: { flex: 1, backgroundColor: '#23272e', borderRadius: 12, alignItems: 'center', justifyContent: 'center', paddingVertical: 18, marginHorizontal: 2 },
  statValue: { fontSize: 22, fontWeight: 'bold', color: 'white' },
  statLabel: { fontSize: 14, color: '#b0b3b8', marginTop: 2 },
  infoCard: { backgroundColor: '#181b20', borderRadius: 16, padding: 18, marginBottom: 18, marginTop: 2 },
  infoTitle: { fontSize: 18, fontWeight: 'bold', color: 'white', marginBottom: 12 },
  infoRow: { flexDirection: 'row', alignItems: 'center', marginBottom: 10 },
  infoIcon: { marginRight: 12 },
  infoText: { fontSize: 15, color: 'white' },
  ordersSection: { marginTop: 18, marginBottom: 18, paddingHorizontal: 12 },
  ordersHeaderRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 },
  ordersSectionTitle: { fontSize: 18, fontWeight: 'bold', color: 'white' },
  seeAllBtn: { fontSize: 14, color: '#60a5fa', textDecorationLine: 'underline' },
  emptyOrdersText: { fontSize: 15, color: 'white', textAlign: 'center', paddingVertical: 10 },
  newOrderBtn: { flexDirection: 'row', alignItems: 'center', justifyContent: 'center', backgroundColor: '#60a5fa', borderRadius: 25, paddingVertical: 12, marginTop: 10 },
  newOrderBtnText: { fontSize: 16, fontWeight: 'bold', color: 'white' },
  measurementsSection: { marginTop: 18, marginBottom: 18, paddingHorizontal: 12 },
  measurementsHeaderRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 },
  measurementsSectionTitle: { fontSize: 18, fontWeight: 'bold', color: 'white' },
  garmentTypeDropdown: { flexDirection: 'row', alignItems: 'center', backgroundColor: '#23272e', borderRadius: 12, paddingHorizontal: 12, paddingVertical: 8, marginLeft: 10 },
  dropdownText: { fontSize: 14, color: '#60a5fa', fontWeight: 'bold' },
  emptyMeasurementsText: { fontSize: 15, color: 'white', textAlign: 'center', paddingVertical: 10 },
  measurementCard: { backgroundColor: '#181b20', borderRadius: 16, padding: 18, marginBottom: 12, elevation: 2 },
  measurementCardHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 },
  measurementTitle: { fontSize: 18, fontWeight: 'bold', color: 'white' },
  measurementDateContainer: { flexDirection: 'row', alignItems: 'center', marginTop: 4 },
  measurementDateLabel: { fontSize: 13, color: '#b0b3b8', marginRight: 8 },
  measurementDate: { fontSize: 13, color: 'white' },
  measurementGrid: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between', marginTop: 8 },
  measurementField: { width: '48%', marginBottom: 12 },
  measurementFieldInner: { backgroundColor: '#23272e', borderRadius: 10, padding: 12, marginBottom: 8 },
  measurementLabel: { fontSize: 13, color: '#b0b3b8', marginBottom: 4 },
  measurementValue: { fontSize: 16, fontWeight: 'bold', color: 'white' },
  notesSection: { marginTop: 12, paddingTop: 12, borderTopWidth: 1, borderTopColor: '#333' },
  notesTitle: { fontSize: 15, fontWeight: 'bold', color: 'white', marginBottom: 8 },
  notesText: { fontSize: 14, color: 'white', lineHeight: 20 },
  newMeasurementBtn: { flexDirection: 'row', alignItems: 'center', justifyContent: 'center', backgroundColor: '#60a5fa', borderRadius: 25, paddingVertical: 12, marginTop: 10 },
  newMeasurementBtnText: { fontSize: 16, fontWeight: 'bold', color: 'white' },
  sectionCard: { backgroundColor: '#181b20', borderRadius: 16, padding: 18, marginBottom: 18, marginTop: 2 },
});

export default CustomerDetailsScreen; 