import { useNavigation, RouteProp } from '@react-navigation/native';
import React, { useState, useEffect } from 'react';
import { ScrollView, View, StyleSheet, Alert, FlatList, ListRenderItem } from 'react-native';
import {
  Text,
  Surface,
  Chip,
  IconButton,
  Divider,
} from 'react-native-paper';


import FormSection from '../../components/forms/FormSection';
import ImagePicker from '../../components/forms/ImagePicker';
import CommonHeader from '../../components/navigation/CommonHeader';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import TextInput from '../../components/ui/TextInput';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import navigationService from '../../services/NavigationService';
import { Order, OrderItem, OrderType, Product, AddOrderScreenProps, FormField } from '../../types';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

interface OrderData {
  customerName: string;
  customerPhone: string;
  customerEmail: string;
  orderType: OrderType;
  items: OrderItem[];
  notes: string;
  discount: number;
  tax: number;
  image: string | null;
}

const AddOrderScreen: React.FC<AddOrderScreenProps> = ({ route }) => {
  const theme = useTheme();
  const { state, actions } = useData();
  const navigation = useNavigation();

  // Get order from route params for editing
  const editingOrder: Order | undefined = route?.params?.orderId ? state.orders.find(o => o.id === route.params?.orderId) : undefined;
  const isEditing: boolean = !!editingOrder;

  // Order form state
  const [orderData, setOrderData] = useState<OrderData>({
    customerName: editingOrder?.customerName || editingOrder?.customer || '',
    customerPhone: editingOrder?.phone || '',
    customerEmail: editingOrder?.email || '',
    orderType: (editingOrder?.orderType as OrderType) || 'dine-in',
    items: editingOrder?.items || [],
    notes: editingOrder?.notes || '',
    discount: editingOrder?.discount || 0,
    tax: editingOrder?.tax || 0,
    image: editingOrder?.image || null,
  });

  const [selectedProducts, setSelectedProducts] = useState<Product[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Filter products based on search
  const filteredProducts = state.products.filter((product: Product) =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleInputChange = (field: keyof OrderData, value: any): void => {
    setOrderData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const addProductToOrder = (product: Product): void => {
    const existingItem = orderData.items.find(item => item.productId === product.id);

    if (existingItem) {
      // Increase quantity
      setOrderData(prev => ({
        ...prev,
        items: prev.items.map(item =>
          item.productId === product.id
            ? { ...item, quantity: item.quantity + 1, total: item.price * (item.quantity + 1) }
            : item
        )
      }));
    } else {
      // Add new item
      setOrderData(prev => ({
        ...prev,
        items: [...prev.items, {
          id: `temp-${Date.now()}-${Math.random()}`,
          orderId: editingOrder?.id || 'temp',
          productId: product.id,
          productName: product.name,
          price: product.price,
          quantity: 1,
          total: product.price * 1,
        }]
      }));
    }
  };

  const updateItemQuantity = (productId: string, quantity: number): void => {
    if (quantity <= 0) {
      removeItemFromOrder(productId);
      return;
    }

    setOrderData(prev => ({
      ...prev,
      items: prev.items.map(item =>
        item.productId === productId
          ? { ...item, quantity, total: item.price * quantity }
          : item
      )
    }));
  };

  const removeItemFromOrder = (productId: string): void => {
    setOrderData(prev => ({
      ...prev,
      items: prev.items.filter(item => item.productId !== productId)
    }));
  };

  const calculateSubtotal = (): number => {
    return orderData.items.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const calculateTotal = (): number => {
    const subtotal = calculateSubtotal();
    const discountAmount = (subtotal * orderData.discount) / 100;
    const taxAmount = ((subtotal - discountAmount) * orderData.tax) / 100;
    return subtotal - discountAmount + taxAmount;
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!orderData.customerName.trim()) {
      newErrors.customerName = 'Customer name is required';
    }

    if (orderData.items.length === 0) {
      newErrors.items = 'At least one item is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async (): Promise<void> => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please fix the errors and try again.');
      return;
    }

    try {
      // console.log('Saving order with data:', orderData);
      const orderToSave = {
        customer: orderData.customerName,
        customerName: orderData.customerName,
        phone: orderData.customerPhone,
        email: orderData.customerEmail,
        items: orderData.items,
        notes: orderData.notes,
        discount: orderData.discount,
        tax: orderData.tax,
        image: orderData.image || undefined,
        orderType: orderData.orderType,
        subtotal: calculateSubtotal(),
        total: calculateTotal(),
        updatedAt: new Date().toISOString(),
      };

      if (isEditing && editingOrder?.id) {
        // Update existing order
        const updatedOrder = {
          ...editingOrder,
          ...orderToSave,
          id: editingOrder.id,
        };
        await actions.updateOrder(updatedOrder);
        // console.log('Order updated successfully:', updatedOrder);

        Alert.alert(
          'Success',
          'Order updated successfully!',
          [
            {
              text: 'Done',
              onPress: () => navigation.goBack(),
              style: 'default'
            }
          ]
        );
      } else {
        // Add new order
        const newOrder = {
          id: Date.now().toString(),
          ...orderToSave,
          status: 'pending' as const,
          date: new Date().toISOString().split('T')[0],
          time: new Date().toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
          }),
          createdAt: new Date().toISOString(),
        };

        await actions.addOrder(newOrder);
        // console.log('Order created successfully:', newOrder);

        Alert.alert(
          'Success',
          'Order created successfully!',
          [
            {
              text: 'Create Another',
              onPress: () => {
                setOrderData({
                  customerName: '',
                  customerPhone: '',
                  customerEmail: '',
                  orderType: 'dine-in',
                  items: [],
                  notes: '',
                  discount: 0,
                  tax: 0,
                  image: null,
                });
              }
            },
            {
              text: 'Done',
              onPress: () => navigation.goBack(),
              style: 'default'
            }
          ]
        );
      }
    } catch (error) {
      // console.error('Error saving order:', error);
      const errorMessage = error instanceof Error ? error.message : 'Please try again.';
      Alert.alert('Error', `Failed to ${isEditing ? 'update' : 'create'} order: ${errorMessage}`);
    }
  };

  const renderProductItem = ({ item: product }: { item: Product }) => (
    <Card
      title={product.name}
      subtitle={product.category}
      description=""
      price={`৳${product.price.toFixed(2)}`}
      status=""
      image=""
      icon=""
      iconColor=""
      iconBackgroundColor=""
      statusColor=""
      statusBackgroundColor=""
      badge=""
      badgeColor=""
      onPress={() => addProductToOrder(product)}
      onLongPress={() => {}}
      actions={[]}
      primaryAction={{
        icon: "plus",
        onPress: () => addProductToOrder(product)
      }}
      secondaryAction={null}
      menuItems={[]}
      menuVisible={false}
      onMenuToggle={() => {}}
      onMenuDismiss={() => {}}
      style={styles.productCard}
      contentStyle={{}}
      layout="compact"
      showImage={false}
      showIcon={false}
    />
  );

  const renderOrderItem = (item: OrderItem) => (
    <View key={item.productId} style={styles.orderItem}>
      <View style={styles.orderItemInfo}>
        <Text variant="titleMedium" style={{ fontWeight: '600' }}>
          {item.productName}
        </Text>
        <Text variant="bodySmall" style={{ color: theme.colors.primary }}>
          ৳{item.price.toFixed(2)} each
        </Text>
      </View>

      <View style={styles.quantityControls}>
        <IconButton
          icon="minus"
          size={16}
          onPress={() => updateItemQuantity(item.productId, item.quantity - 1)}
        />
        <Text variant="titleMedium" style={styles.quantity}>
          {item.quantity}
        </Text>
        <IconButton
          icon="plus"
          size={16}
          onPress={() => updateItemQuantity(item.productId, item.quantity + 1)}
        />
      </View>

      <View style={styles.itemTotal}>
        <Text variant="titleMedium" style={{ fontWeight: '600' }}>
          ৳{(item.price * item.quantity).toFixed(2)}
        </Text>
        <IconButton
          icon="delete"
          size={16}
          iconColor={theme.colors.error}
          onPress={() => removeItemFromOrder(item.productId)}
        />
      </View>
    </View>
  );

  // Field config for FormSection (customer info)
  const customerFields: FormField[] = [
    {
      name: 'customerName',
      key: 'customerName',
      label: 'Customer Name',
      type: 'text',
      value: orderData.customerName,
      onChange: (val: string) => setOrderData(prev => ({ ...prev, customerName: val })),
      required: true,
      validation: (val: string) => val.trim() !== '' ? '' : 'Customer name is required',
    },
    {
      name: 'customerPhone',
      key: 'customerPhone',
      label: 'Phone',
      type: 'phone',
      value: orderData.customerPhone,
      onChange: (val: string) => setOrderData(prev => ({ ...prev, customerPhone: val })),
      required: false,
      validation: (val: string) => val === '' || (/^\d{8,15}$/.test(val)) ? '' : 'Invalid phone number',
    },
    {
      name: 'customerEmail',
      key: 'customerEmail',
      label: 'Email',
      type: 'email',
      value: orderData.customerEmail,
      onChange: (val: string) => setOrderData(prev => ({ ...prev, customerEmail: val })),
      required: false,
      validation: (val: string) => val === '' || (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val)) ? '' : 'Invalid email address',
    },
  ];

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title={isEditing ? "Edit Order" : "Create Order"}
        subtitle={isEditing ? "Update order details" : "New customer order"}
        showSearch={false}
        onNotificationPress={() => {
          try {
            navigationService.navigate('NotificationsScreen');
          } catch (error) {
            // console.error('Failed to navigate to NotificationsScreen:', error);
          }
        }}
        onProfilePress={() => {
          // console.log('Profile pressed - navigating to MyProfile');
          try {
            navigationService.navigate('MyProfile');
          } catch (error) {
            // console.error('Failed to navigate to MyProfile:', error);
          }
        }}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>

        {/* Customer Information */}
        <FormSection
          fields={customerFields}
          errors={errors}
          onFieldError={(key: string, error: string) => setErrors(prev => ({ ...prev, [key]: error }))}
          title="Customer Information"
        />

        {/* Order Type */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Order Type
          </Text>

          <View style={styles.chipContainer}>
            {[
              { id: 'dine-in', label: 'Dine In', icon: 'silverware-fork-knife' },
              { id: 'takeaway', label: 'Takeaway', icon: 'bag-personal' },
              { id: 'delivery', label: 'Delivery', icon: 'truck-delivery' }
            ].map((type) => (
              <Chip
                key={type.id}
                selected={orderData.orderType === type.id}
                onPress={() => handleInputChange('orderType', type.id)}
                icon={type.icon}
                style={[
                  styles.chip,
                  orderData.orderType === type.id && { backgroundColor: theme.colors.primaryContainer }
                ]}
                textStyle={{
                  color: orderData.orderType === type.id ? theme.colors.onPrimaryContainer : theme.colors.onSurface
                }}
              >
                {String(type.label ?? '')}
              </Chip>
            ))}
          </View>
        </Surface>

        {/* Add Products */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Add Products ({state.products.length} available)
          </Text>

          <TextInput
            label="Search products..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            style={styles.input}
            type="search"
            minLength={0}
            maxLength={100}
            validate={() => ''}
            error=""
            onBlur={() => {}}
            rightAffix=""
          />

          {filteredProducts.length > 0 ? (
            <FlatList
              data={filteredProducts}
              renderItem={renderProductItem}
              keyExtractor={(item) => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.productsList}
            />
          ) : (
            <View style={{ padding: 20, alignItems: 'center' }}>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                {state.products.length === 0
                  ? 'No products available. Please add products first.'
                  : 'No products match your search.'}
              </Text>
            </View>
          )}
        </Surface>

        {/* Order Items */}
        {orderData.items.length > 0 && (
          <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
            <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Order Items ({orderData.items.length})
            </Text>
            {errors.items && <Text style={[styles.errorText, { color: theme.colors.error }]}>{errors.items}</Text>}

            {orderData.items.map(renderOrderItem)}

            <Divider style={{ marginVertical: 16 }} />

            {/* Order Summary */}
            <View style={styles.summaryRow}>
              <Text variant="bodyLarge">Subtotal:</Text>
              <Text variant="bodyLarge">৳{calculateSubtotal().toFixed(2)}</Text>
            </View>

            <View style={styles.row}>
              <TextInput
                label="Discount %"
                value={orderData.discount.toString()}
                onChangeText={(value: string) => handleInputChange('discount', parseFloat(value) || 0)}
                keyboardType="decimal-pad"
                style={{
                  ...styles.input,
                  ...styles.halfWidth
                }}
                type="number"
                minLength={0}
                maxLength={5}
                validate={() => ''}
                error=""
                onBlur={() => {}}
                rightAffix="%"
              />

              <TextInput
                label="Tax %"
                value={orderData.tax.toString()}
                onChangeText={(value: string) => handleInputChange('tax', parseFloat(value) || 0)}
                keyboardType="decimal-pad"
                style={{
                  ...styles.input,
                  ...styles.halfWidth
                }}
                type="number"
                minLength={0}
                maxLength={5}
                validate={() => ''}
                error=""
                onBlur={() => {}}
                rightAffix="%"
              />
            </View>

            <View style={[styles.summaryRow, styles.totalRow]}>
              <Text variant="headlineSmall" style={{ fontWeight: '700' }}>Total:</Text>
              <Text variant="headlineSmall" style={{ fontWeight: '700', color: theme.colors.primary }}>
                ৳{calculateTotal().toFixed(2)}
              </Text>
            </View>
          </Surface>
        )}

        {/* Notes */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Order Notes
          </Text>

          <TextInput
            label="Special instructions..."
            value={orderData.notes}
            onChangeText={(value: string) => handleInputChange('notes', value)}
            multiline
            numberOfLines={3}
            style={styles.input}
            type="text"
            minLength={0}
            maxLength={500}
            validate={() => ''}
            error=""
            onBlur={() => {}}
            rightAffix=""
          />
        </Surface>

        {/* Compact Image Section */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Order Image
          </Text>
          <View style={styles.compactImageContainer}>
            <ImagePicker
              key={orderData.image || 'no-image'} // Force re-render when image changes
              onImageSelected={(imageUri: string) => handleInputChange('image', imageUri)}
              currentImage={orderData.image}
              placeholder="Add image (optional)"
            />
          </View>
        </Surface>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <Button
            variant="outline"
            onPress={() => navigation.goBack()}
            style={{...styles.button, ...styles.cancelButton}}
          >
            Cancel
          </Button>

          <Button
            variant="primary"
            onPress={handleSave}
            style={{...styles.button, ...styles.saveButton}}
          >
            {isEditing ? 'Update Order' : 'Create Order'}
          </Button>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: 16,
  },
  input: {
    marginBottom: 12,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },
  errorText: {
    fontSize: 12,
    marginTop: -8,
    marginBottom: 8,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  chip: {
    marginBottom: 8,
  },
  productsList: {
    maxHeight: 120,
  },
  productCard: {
    width: 200,
    marginRight: 12,
  },
  productContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  productInfo: {
    flex: 1,
  },
  orderItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  orderItemInfo: {
    flex: 1,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantity: {
    marginHorizontal: 8,
    minWidth: 30,
    textAlign: 'center',
  },
  itemTotal: {
    alignItems: 'flex-end',
    minWidth: 80,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  totalRow: {
    paddingTop: 12,
    borderTopWidth: 2,
    borderTopColor: '#E0E0E0',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 24,
    marginBottom: 32,
  },
  button: {
    flex: 1,
  },
  cancelButton: {
    borderColor: '#E0E0E0',
  },
  saveButton: {
    elevation: 2,
  },
  compactImageContainer: {
    alignItems: 'center',
  },
});

export default AddOrderScreen;
