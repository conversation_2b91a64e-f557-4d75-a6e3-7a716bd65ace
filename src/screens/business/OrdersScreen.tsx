import React, { useState, useCallback, useMemo } from 'react';
import { FlatList, View, StyleSheet, Alert, TouchableOpacity, Image, RefreshControl, ListRenderItem } from 'react-native';
import {
    Text,
    Button,
    Searchbar,
    Chip,
    Surface,
    IconButton,
} from 'react-native-paper';
// Removed unused Icon import - using react-native-paper IconButton and Button icons

import OrderDetailsBottomSheet from '../../components/bottomsheets/OrderDetailsBottomSheet';
import PDFInvoiceBottomSheet from '../../components/bottomsheets/PDFInvoiceBottomSheet';
import InfoCard from '../../components/cards/InfoCard';
import Search from '../../components/forms/Search';
import CommonHeader from '../../components/navigation/CommonHeader';
import Chips from '../../components/ui/Chips';
import EmptyState from '../../components/ui/EmptyState';
import StatCardGroup from '../../components/ui/StatCardGroup';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import navigationService from '../../services/NavigationService';
import { StatCard } from '../../types';
import { PDFInvoiceGenerator } from '../../utils/pdfInvoiceGenerator';

interface OrdersScreenProps {
    navigation: any;
}

interface Order {
    id: string | number;
    customerName?: string;
    customer?: string;
    customerPhone?: string;
    phone?: string;
    status: string;
    date: string;
    total: number;
    items: any[];
    image?: string;
    createdAt: string;
}

interface StatusColors {
    bg: string;
    text: string;
}

interface OrderStats {
    total: number;
    pending: number;
    inProgress: number;
    completed: number;
    revenue: number;
}

const OrdersScreen: React.FC<OrdersScreenProps> = ({ navigation }) => {
    const theme = useTheme();
    const { state, actions } = useData();
    const [searchQuery, setSearchQuery] = useState<string>('');
    const [selectedStatus, setSelectedStatus] = useState<string>('All');
    const orderDetailsBottomSheetRef = React.useRef<any>(null);
    const invoiceBottomSheetRef = React.useRef<any>(null);
    const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
    const [invoiceOrder, setInvoiceOrder] = useState<Order | null>(null);
    const [refreshing, setRefreshing] = useState<boolean>(false);

    const statuses = ['All', 'Pending', 'In Progress', 'Completed', 'Cancelled'];

    // Pull to refresh functionality
    const handleRefresh = useCallback(async (): Promise<void> => {
        setRefreshing(true);
        try {
            // Simulate data refresh - in real app, this would reload from API
            await new Promise(resolve => setTimeout(resolve, 1000));
            LoggingService.info('Orders data refreshed', 'SCREEN');
        } catch (error) {
            LoggingService.warn('Failed to refresh orders data', 'SCREEN', error as Error);
        } finally {
            setRefreshing(false);
        }
    }, []);

    const filteredOrders = useMemo(() => {
        return state.orders.filter((order: any) => {
            // Safely handle customer name with multiple fallbacks
            const customerName = order?.customerName || order?.customer || '';
            const orderId = order?.id ? order.id.toString() : '';
            const orderStatus = order?.status || 'pending';

            // Safely perform search matching
            const matchesSearch = (customerName && customerName.toLowerCase().includes(searchQuery.toLowerCase())) ||
                (orderId && orderId.includes(searchQuery));
            const matchesStatus = selectedStatus === 'All' || orderStatus === selectedStatus;
            return matchesSearch && matchesStatus;
        });
    }, [state.orders, searchQuery, selectedStatus]);

    const handleAddOrder = (): void => {
        LoggingService.info('Navigating to Add Order page', 'NAVIGATION');
        try {
            navigationService.navigate('AddOrder');
        } catch (error) {
            LoggingService.error('Failed to navigate to AddOrder', 'NAVIGATION', error as Error);
        }
    };

    const handleOrderPress = useCallback((order: Order): void => {
        LoggingService.info('Order card pressed', 'NAVIGATION', { orderId: order.id });
        setSelectedOrder(order);
        // Add a small delay to ensure state is set before opening bottom sheet
        setTimeout(() => {
            orderDetailsBottomSheetRef.current?.expand();
        }, 50);
    }, []);

    const handleEditOrder = useCallback((order: Order): void => {
        LoggingService.info('Navigating to Edit Order page', 'NAVIGATION', { orderId: order.id });
        try {
            // Navigate to AddOrder page with order data for editing
            navigationService.navigate('AddOrder', { order });
        } catch (error) {
            LoggingService.error('Failed to navigate to edit order', 'NAVIGATION', error as Error);
        }
    }, []);

    const handleDeleteOrder = useCallback((order: any): void => {
        Alert.alert(
            'Delete Order',
            `Are you sure you want to delete order #${order.id}?`,
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Delete',
                    style: 'destructive',
                    onPress: () => actions.deleteOrder(order.id.toString()),
                },
            ]
        );
    }, [actions]);

    const handleUpdateOrderStatus = useCallback((orderId: string | number, newStatus: string): void => {
        actions.updateOrderStatus(orderId.toString(), newStatus);
    }, [actions]);

    const handleViewInvoice = useCallback((order: Order): void => {
        LoggingService.info('Opening invoice for order', 'SCREEN', { orderId: order.id });
        setInvoiceOrder(order);
        invoiceBottomSheetRef.current?.expand();
    }, []);

    const handleGeneratePDF = async (order: Order): Promise<void> => {
        try {
            const pdf = await PDFInvoiceGenerator.generatePDF(order);
            Alert.alert(
                'PDF Generated',
                `Invoice PDF saved successfully!\nLocation: ${pdf.filePath}`,
                [
                    { text: 'OK' },
                    {
                        text: 'Share',
                        onPress: async () => {
                            try {
                                await PDFInvoiceGenerator.generateAndSharePDF(order);
                            } catch (error) {
                                Alert.alert('Share Error', 'Failed to share PDF invoice.');
                            }
                        },
                    },
                ]
            );
        } catch (error) {
            LoggingService.error('PDF generation error', 'SCREEN', error as Error);
            Alert.alert('PDF Error', 'Failed to generate PDF invoice. Please try again.');
        }
    };

    const getStatusColor = useCallback((status: string): StatusColors => {
        switch (status) {
            case 'Completed':
                return {
                    bg: theme.colors.tertiary, // Green - Success
                    text: theme.colors.onTertiary,
                };
            case 'In Progress':
                return {
                    bg: theme.colors.secondary, // Orange - In Progress
                    text: theme.colors.onSecondary,
                };
            case 'Pending':
                return {
                    bg: theme.colors.primary, // Blue - Waiting
                    text: theme.colors.onPrimary,
                };
            case 'Cancelled':
                return {
                    bg: theme.colors.error, // Red - Error/Cancelled
                    text: theme.colors.onError,
                };
            default:
                return {
                    bg: theme.colors.surfaceVariant,
                    text: theme.colors.onVariant,
                };
        }
    }, [theme]);

    const renderOrderCard: ListRenderItem<any> = useCallback(({ item: order }) => {
        const statusColors = getStatusColor(order.status);

        return (
            <TouchableOpacity
                onPress={() => handleOrderPress(order)}
                activeOpacity={0.7}
                delayPressIn={0}
                delayPressOut={100}
            >
                <Surface style={[styles.orderCard, { backgroundColor: theme.colors.surface }]} elevation={0}>
                    {/* Status Bar */}
                    <View style={[styles.statusBar, { backgroundColor: statusColors.bg }]}>
                        <Text variant="bodySmall" style={{ color: statusColors.text, fontWeight: '600' }}>
                            {order.status}
                        </Text>
                    </View>

                    <View style={styles.orderContent}>
                        {/* Header Row */}
                        <View style={styles.headerRow}>
                            <View style={styles.orderIdSection}>
                                <Text variant="titleLarge" style={{ fontWeight: '700', color: theme.colors.primary }}>
                                    #{order.id}
                                </Text>
                                <Text variant="bodySmall" style={{ color: theme.colors.onVariant }}>
                                    {order.date}
                                </Text>
                            </View>
                            <Text variant="headlineSmall" style={{ color: theme.colors.on, fontWeight: '700' }}>
                                ৳{order.total.toFixed(2)}
                            </Text>
                        </View>

                        {/* Content Row */}
                        <View style={styles.contentRow}>
                            {order.image && (
                                <Image source={{ uri: order.image }} style={styles.orderImage} />
                            )}
                            <View style={styles.orderDetails}>
                                <Text variant="titleMedium" style={{ fontWeight: '600', marginBottom: 2 }}>
                                    {order.customerName || order.customer || 'Unknown Customer'}
                                </Text>
                                <Text variant="bodySmall" style={{ color: theme.colors.onVariant, marginBottom: 8 }}>
                                    {order.items.length} item{order.items.length !== 1 ? 's' : ''} • {order.customerPhone || order.phone || 'No phone'}
                                </Text>
                            </View>
                        </View>

                        {/* Action Row */}
                        <View style={styles.actionRow}>
                            <View style={styles.actionGroup}>
                                <IconButton
                                    icon="pencil-outline"
                                    size={20}
                                    iconColor={theme.colors.onVariant}
                                    onPress={() => handleEditOrder(order)}
                                    style={styles.actionIcon}
                                />
                                {order.status === 'Pending' && (
                                    <>
                                        <Button
                                            mode="outlined"
                                            onPress={() => handleUpdateOrderStatus(order.id, 'Cancelled')}
                                            compact
                                            style={styles.actionButton}
                                            icon="close-outline"
                                        >
                                            Cancel
                                        </Button>
                                        <Button
                                            mode="contained"
                                            onPress={() => handleUpdateOrderStatus(order.id, 'In Progress')}
                                            compact
                                            style={styles.primaryButton}
                                            icon="play-outline"
                                        >
                                            Start
                                        </Button>
                                    </>
                                )}
                                {order.status === 'In Progress' && (
                                    <Button
                                        mode="contained"
                                        onPress={() => handleUpdateOrderStatus(order.id, 'Completed')}
                                        compact
                                        style={styles.primaryButton}
                                        icon="check-outline"
                                    >
                                        Complete
                                    </Button>
                                )}
                                {order.status === 'Completed' && (
                                    <Button
                                        mode="contained"
                                        onPress={() => handleViewInvoice(order)}
                                        compact
                                        style={styles.primaryButton}
                                        icon="receipt"
                                        buttonColor={theme.colors.tertiary}
                                        textColor={theme.colors.onTertiary}
                                    >
                                        Invoice
                                    </Button>
                                )}
                                {order.status === 'Cancelled' && (
                                    <Button
                                        mode="outlined"
                                        onPress={() => handleUpdateOrderStatus(order.id, 'Pending')}
                                        compact
                                        style={styles.actionButton}
                                        icon="refresh-outline"
                                    >
                                        Reactivate
                                    </Button>
                                )}
                            </View>
                            <IconButton
                                icon="delete-outline"
                                size={20}
                                iconColor={theme.colors.error}
                                onPress={() => handleDeleteOrder(order)}
                                style={styles.actionIcon}
                            />
                        </View>
                    </View>
                </Surface>
            </TouchableOpacity>
        );
    }, [theme, handleOrderPress, handleEditOrder, handleDeleteOrder, handleViewInvoice, handleUpdateOrderStatus, getStatusColor]);

    const orderStats = useMemo((): OrderStats => ({
        total: filteredOrders.length,
        pending: filteredOrders.filter((o: any) => o.status === 'Pending').length,
        inProgress: filteredOrders.filter((o: any) => o.status === 'In Progress').length,
        completed: filteredOrders.filter((o: any) => o.status === 'Completed').length,
        revenue: filteredOrders.reduce((sum: number, order: any) => sum + order.total, 0),
    }), [filteredOrders]);

    return (
        <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
            <CommonHeader
                title="Orders"
                subtitle="Track and manage orders"
                searchPlaceholder="Search orders..."
                searchType="orders"
                searchData={state.orders}
                searchFields={["customerName", "customer", "id", "status"]}
                onSearchChange={setSearchQuery}
                onSearchResult={(order: Order) => {
                    LoggingService.info('Order selected from search', 'SEARCH', { orderId: order.id });
                    setSelectedOrder(order);
                    orderDetailsBottomSheetRef.current?.expand();
                }}
                onNotificationPress={() => {
                    try {
                        navigationService.navigate('NotificationsScreen');
                    } catch (error) {
                        LoggingService.error('Failed to navigate to NotificationsScreen', 'NAVIGATION', error as Error);
                    }
                }}
                onProfilePress={() => {
                    LoggingService.info('Profile pressed - navigating to MyProfile', 'NAVIGATION');
                    try {
                        navigationService.navigate('MyProfile');
                    } catch (error) {
                        LoggingService.error('Failed to navigate to MyProfile', 'NAVIGATION', error as Error);
                    }
                }}
            />

            <FlatList
                data={filteredOrders}
                renderItem={renderOrderCard}
                keyExtractor={(item) => item.id.toString()}
                style={styles.content}
                showsVerticalScrollIndicator={false}
                removeClippedSubviews={true}
                maxToRenderPerBatch={10}
                updateCellsBatchingPeriod={50}
                initialNumToRender={8}
                windowSize={10}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={handleRefresh}
                        colors={[theme.colors.primary]}
                        tintColor={theme.colors.primary}
                    />
                }
                ListHeaderComponent={() => (
                    <View>
                        {/* Status Filter */}
                        <Chips
                            filters={statuses as any}
                            selectedFilter={selectedStatus}
                            onFilterChange={(filter) => setSelectedStatus(String(filter))}
                            showCounts={true}
                            data={state.orders as any}
                            countField="status"
                            style={{ marginBottom: 16 }}
                            chipStyle={{}}
                        />

                        {/* Stats Row */}
                        <StatCardGroup
                            title="Order Stats"
                            cards={[
                                {
                                    key: 'total',
                                    title: 'Total Orders',
                                    value: orderStats.total.toString(),
                                    icon: 'clipboard-text',
                                    iconColor: theme.colors.primary,
                                    elevation: 1,
                                },
                                {
                                    key: 'revenue',
                                    title: 'Revenue',
                                    value: `৳${orderStats.revenue.toFixed(0)}`,
                                    icon: 'currency-usd',
                                    iconColor: theme.colors.secondary,
                                    elevation: 1,
                                },
                            ] as StatCard[]}
                            columns={2}
                            showTitle={false}
                            containerStyle={{ marginBottom: 16 }}
                        />
                    </View>
                )}
                ListEmptyComponent={() => (
                    <EmptyState
                        type="orders"
                        searchQuery={searchQuery}
                        onActionPress={handleAddOrder}
                        description="No orders found"
                        actionLabel="Add Order"
                        style={{}}
                        iconColor={theme.colors.primary}
                    />
                )}
            />

            <OrderDetailsBottomSheet
                bottomSheetRef={orderDetailsBottomSheetRef}
                order={selectedOrder}
                onUpdateStatus={handleUpdateOrderStatus}
                onEdit={handleEditOrder}
                onDelete={handleDeleteOrder}
            />

            {React.createElement(PDFInvoiceBottomSheet as any, {
                ref: invoiceBottomSheetRef,
                order: invoiceOrder,
                onClose: () => setInvoiceOrder(null)
            })}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    searchbar: {
        flex: 1,
        borderRadius: 25,
        height: 48,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    statusContainer: {
        marginBottom: 8,
    },
    statusChip: {
        marginRight: 6,
    },
    content: {
        flex: 1,
        paddingHorizontal: 16,
    },
    statsRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 16,
        gap: 12,
    },
    statCardWrapper: {
        flex: 1,
    },
    orderCard: {
        marginBottom: 12,
        borderRadius: 16,
        borderWidth: 1,
        borderColor: 'rgba(0,0,0,0.05)',
        overflow: 'hidden',
    },
    statusBar: {
        paddingHorizontal: 16,
        paddingVertical: 6,
        alignItems: 'center',
    },
    orderContent: {
        padding: 16,
    },
    headerRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    orderIdSection: {
        flex: 1,
    },
    contentRow: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        marginBottom: 16,
    },
    orderImage: {
        width: 56,
        height: 56,
        borderRadius: 12,
        resizeMode: 'cover',
        marginRight: 12,
    },
    orderDetails: {
        flex: 1,
    },
    actionRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    actionGroup: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
    },
    actionIcon: {
        margin: 0,
    },
    actionButton: {
        minWidth: 70,
        height: 36,
    },
    primaryButton: {
        minWidth: 80,
        height: 36,
    },
});

export default OrdersScreen;