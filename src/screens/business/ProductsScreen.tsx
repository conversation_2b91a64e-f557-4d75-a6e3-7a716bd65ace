import React, { useState, useC<PERSON>back, useMemo } from 'react';
import { FlatList, View, StyleSheet, RefreshControl, Alert, ListRenderItem } from 'react-native';
import {
  Text,
  Button,
  Surface,
  Menu,
  TextInput,
  FAB,
  IconButton,
} from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';


import ProductBottomSheet from '../../components/bottomsheets/ProductBottomSheet';
import ProductCard from '../../components/cards/ProductCard';
import FilterSortBottomSheet from '../../components/filters/FilterSortBottomSheet';
import EmptyState from '../../components/ui/EmptyState';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import navigationService from '../../services/NavigationService';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

interface ProductsScreenProps {
  navigation: any;
  route?: {
    params?: {
      selectMode?: boolean;
      onProductSelect?: (product: any) => void;
      returnTo?: string;
    };
  };
}

interface SortOption {
  key: string;
  label: string;
}

interface SelectedSort {
  key: string;
  direction: 'asc' | 'desc';
}

interface EnhancedProduct {
  id: string | number;
  name: string;
  price: number;
  stock: number;
  category: string;
  image?: string;
  [key: string]: any;
}

interface ItemRowProps {
  active: boolean;
  onPress: () => void;
  children: React.ReactNode;
  theme: any;
}

const ProductsScreen: React.FC<ProductsScreenProps> = ({ navigation, route }) => {
  const theme = useTheme();
  const { state, actions } = useData();
  const { products } = state;
  const insets = useSafeAreaInsets();

  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedFilter, setSelectedFilter] = useState<string>('All');
  const [menuVisible, setMenuVisible] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [selectedSort, setSelectedSort] = useState<SelectedSort>({ key: 'name', direction: 'asc' });
  const [tabIndex, setTabIndex] = useState<number>(0);
  const [routes] = useState([
    { key: 'filters', title: 'Filters' },
    { key: 'sorting', title: 'Sorting' },
  ]);
  
  // Selection state
  const [selectedProducts, setSelectedProducts] = useState<Set<string>>(new Set());
  const [isSelectionMode, setIsSelectionMode] = useState<boolean>(false);
  
  const optionsSheetRef = React.useRef<any>(null);
  const addProductSheetRef = React.useRef<any>(null);
  const filterSortSheetRef = React.useRef<any>(null);

  const filters = ['All', 'In Stock', 'Low Stock', 'Out of Stock', 'On Sale'];
  const selectMode = route?.params?.selectMode || false;
  const onProductSelect = route?.params?.onProductSelect;
  const returnTo = route?.params?.returnTo;

  const getFilterCount = useCallback((filter: string): number => {
    if (!products) return 0;
    return products.length;
  }, [products]);

  const sortOptions: SortOption[] = [
    { key: 'name', label: 'Name' },
    { key: 'price', label: 'Price' },
    { key: 'stock', label: 'Stock' },
    { key: 'category', label: 'Category' },
    { key: 'createdAt', label: 'Date Added' },
  ];

  const enhancedProducts = useMemo((): EnhancedProduct[] => {
    if (!products) return [];
    return products.map((product: any) => ({
      ...product,
      price: parseFloat(product.price) || 0,
      stock: parseInt(product.stock) || 0,
    }));
  }, [products]);

  const filteredProducts = useMemo((): EnhancedProduct[] => {
    let filtered = [...enhancedProducts];

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter((product: EnhancedProduct) =>
        product.name?.toLowerCase().includes(query) ||
        product.category?.toLowerCase().includes(query)
      );
    }

    // Apply category filters
    if (selectedFilter !== 'All') {
      switch (selectedFilter) {
        case 'In Stock':
          filtered = filtered.filter(product => product.stock > 0);
          break;
        case 'Low Stock':
          filtered = filtered.filter(product => product.stock > 0 && product.stock <= 10);
          break;
        case 'Out of Stock':
          filtered = filtered.filter(product => product.stock === 0);
          break;
        case 'On Sale':
          // You can add sale logic here
          break;
      }
    }

    filtered.sort((a: EnhancedProduct, b: EnhancedProduct) => {
        const key = selectedSort.key;
        let valA = (a as any)[key];
        let valB = (b as any)[key];

        if (typeof valA === 'string') valA = valA.toLowerCase();
        if (typeof valB === 'string') valB = valB.toLowerCase();
        
        if (valA < valB) return selectedSort.direction === 'asc' ? -1 : 1;
        if (valA > valB) return selectedSort.direction === 'asc' ? 1 : -1;
        return 0;
    });

    return filtered;
  }, [enhancedProducts, searchQuery, selectedFilter, selectedSort]);

  const handleRefresh = useCallback(async (): Promise<void> => {
    setRefreshing(true);
    try {
      // Simulate refresh - in real app this would reload data
      await new Promise(resolve => setTimeout(resolve, 1000));
      LoggingService.info('Products data refreshed', 'SCREEN');
    } catch (error) {
      LoggingService.error('Failed to refresh products data', 'SCREEN', error as Error);
    } finally {
      setRefreshing(false);
    }
  }, []);

  const handleProductSelect = (product: EnhancedProduct): void => {
    if (selectMode && onProductSelect) {
      onProductSelect(product);
      navigation.goBack();
      return;
    }
    // Navigate to product details or edit
    navigationService.navigate('AddProduct', { product });
  };

  // Selection handlers
  const handleProductLongPress = useCallback((product: EnhancedProduct) => {
    if (selectMode) return;
    
    setIsSelectionMode(true);
    setSelectedProducts(new Set([product.id.toString()]));
    
    LoggingService.info('Entered product selection mode', 'PRODUCTS', {
      productId: product.id,
      productName: product.name
    });
  }, [selectMode]);

  const handleProductPress = useCallback((product: EnhancedProduct) => {
    if (isSelectionMode) {
      const productId = product.id.toString();
      setSelectedProducts(prev => {
        const newSelected = new Set(prev);
        if (newSelected.has(productId)) {
          newSelected.delete(productId);
          if (newSelected.size === 0) {
            setIsSelectionMode(false);
          }
        } else {
          newSelected.add(productId);
        }
        return newSelected;
      });
    } else {
      handleProductSelect(product);
    }
  }, [isSelectionMode, handleProductSelect]);

  const handleProductCheckboxPress = useCallback((product: EnhancedProduct) => {
    if (isSelectionMode) {
      const productId = product.id.toString();
      setSelectedProducts(prev => {
        const newSelected = new Set(prev);
        if (newSelected.has(productId)) {
          newSelected.delete(productId);
          if (newSelected.size === 0) {
            setIsSelectionMode(false);
          }
        } else {
          newSelected.add(productId);
        }
        return newSelected;
      });
    }
  }, [isSelectionMode]);

  const handleSelectAll = useCallback(() => {
    setSelectedProducts(prev => {
      if (prev.size === filteredProducts.length) {
        setIsSelectionMode(false);
        return new Set();
      } else {
        return new Set(filteredProducts.map(product => product.id.toString()));
      }
    });
  }, [filteredProducts]);

  const handleBulkDelete = useCallback(() => {
    if (selectedProducts.size === 0) return;

    Alert.alert(
      'Delete Products',
      `Are you sure you want to delete ${selectedProducts.size} product${selectedProducts.size > 1 ? 's' : ''}? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const selectedIds = Array.from(selectedProducts);
              const deletedCount = selectedProducts.size;
              
              for (const id of selectedIds) {
                await actions.deleteProduct(id);
              }
              
              setSelectedProducts(new Set());
              setIsSelectionMode(false);
              
              await actions.reloadData();
              
              Alert.alert('Success', `Successfully deleted ${deletedCount} product${deletedCount > 1 ? 's' : ''}.`);
              
              LoggingService.info('Bulk product deletion completed', 'PRODUCTS', {
                deletedCount,
                productIds: selectedIds
              });
            } catch (error) {
              LoggingService.error('Bulk product deletion failed', 'PRODUCTS', error as Error);
              Alert.alert('Error', 'Failed to delete some products. Please try again.');
            }
          },
        },
      ]
    );
  }, [selectedProducts, actions]);

  const handleBulkExport = useCallback(() => {
    if (selectedProducts.size === 0) return;

    const selectedProductData = filteredProducts.filter(product => 
      selectedProducts.has(product.id.toString())
    );

    // Create CSV content
    const headers = ['Name', 'Price', 'Stock', 'Category'];
    const csvContent = [
      headers.join(','),
      ...selectedProductData.map(product =>
        [
          `"${product.name}"`,
          product.price,
          product.stock,
          `"${product.category || ''}"`
        ].join(',')
      )
    ].join('\n');

    Alert.alert('Success', `Successfully exported ${selectedProducts.size} product${selectedProducts.size > 1 ? 's' : ''}.`);
    
    LoggingService.info('Bulk product export completed', 'PRODUCTS', {
      exportedCount: selectedProducts.size
    });
  }, [selectedProducts, filteredProducts]);

  const handleCancelSelection = useCallback(() => {
    setSelectedProducts(new Set());
    setIsSelectionMode(false);
  }, []);

  const isProductSelected = useCallback((productId: string | number) => {
    return selectedProducts.has(productId.toString());
  }, [selectedProducts]);

  const renderProductCard: ListRenderItem<EnhancedProduct> = useCallback(({ item: product }) => {
    const selected = isSelectionMode && isProductSelected(product.id);
    
    return (
      <View style={styles.productCardContainer}>
        <ProductCard
          product={product}
          onPress={() => handleProductPress(product)}
          onLongPress={() => handleProductLongPress(product)}
          onCheckboxPress={() => handleProductCheckboxPress(product)}
          selected={selected}
          showSelectionIndicator={isSelectionMode}
        />
      </View>
    );
  }, [handleProductPress, handleProductLongPress, handleProductCheckboxPress, isSelectionMode, isProductSelected]);

  const keyExtractor = useCallback((item: EnhancedProduct) => item.id?.toString() || '', []);

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
    },
    headerContainer: {
      paddingHorizontal: 16,
      paddingBottom: 12,
      elevation: 2,
    },
    searchBox: {
      backgroundColor: 'transparent',
      marginBottom: 12,
    },
    searchOutline: {
      borderRadius: 8,
    },
    filtersContentContainer: {
      paddingLeft: 0,
    },
    filter: {
      marginRight: 8,
      borderWidth: 1,
    },
    filterText: {
      fontSize: 12,
    },
    listHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
      paddingHorizontal: 16,
    },
    title: {
      fontWeight: 'bold',
    },
    headerActions: {
      flexDirection: 'row',
      gap: 8,
    },
    headerButton: {
      borderRadius: 8,
    },
    headerButtonLabel: {
      fontSize: 12,
    },
    sortButton: {
      borderRadius: 8,
    },
    sortButtonLabel: {
      fontSize: 12,
    },
    listContainer: {
      padding: 16,
      paddingBottom: 100,
    },
    productCardContainer: {
      marginBottom: 12,
    },
    headerRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      minHeight: 36,
    },
    headerLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    selectAllButton: {
      marginRight: 8,
      margin: 0,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: '400',
      textAlign: 'left',
      flex: 1,
    },
    headerActionsRow: {
      flexDirection: 'row',
      gap: 8,
    },
    filterButton: {
      marginLeft: 1,
      borderRadius: 8,
      paddingHorizontal: 0,
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 32,
    },
    emptyTitle: {
      marginTop: 16,
      fontWeight: 'bold',
    },
    emptySubtitle: {
      marginTop: 8,
      color: theme.colors.secondary,
    },
    fab: {
      position: 'absolute',
      margin: 16,
      right: 0,
      bottom: 0,
    },
  }), [theme]);

  // Memoized header title and actions for selection mode
  const headerTitle = useMemo(() => {
    if (isSelectionMode) {
      return `Selected (${selectedProducts.size})`;
    }
    return `Products (${filteredProducts.length})`;
  }, [isSelectionMode, selectedProducts.size, filteredProducts.length]);

  return (
    <View style={[styles.container, { backgroundColor: theme?.colors?.background }]}>

      {/* Main Header */}
      <Surface style={[styles.headerContainer, { paddingTop: insets.top + 8, backgroundColor: theme.colors.surface }]}> 
        <TextInput
          mode="outlined"
          placeholder="Search products..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          style={[
            styles.searchBox,
            { height: 48, backgroundColor: theme.colors.background }
          ]}
          outlineStyle={styles.searchOutline}
          left={<TextInput.Icon icon="magnify" />}
          right={searchQuery ? <TextInput.Icon icon="close" onPress={() => setSearchQuery('')} /> : null}
        />
        <View style={styles.headerRow}>
          <View style={styles.headerLeft}>
            {isSelectionMode && (
              <IconButton
                icon={selectedProducts.size === filteredProducts.length ? "checkbox-marked" : "checkbox-blank-outline"}
                size={24}
                onPress={handleSelectAll}
                iconColor={theme.colors.onSurface}
                style={styles.selectAllButton}
              />
            )}
            <Text
              style={[
                styles.headerTitle,
                styles.title,
                { color: theme.colors.onSurface }
              ]}>
              {headerTitle}
            </Text>
          </View>
          {isSelectionMode ? (
            <View style={styles.headerActionsRow}>
              <IconButton
                icon="delete"
                size={20}
                onPress={handleBulkDelete}
                iconColor={theme.colors.onError}
                style={[
                  styles.headerButton,
                  { 
                    backgroundColor: theme.colors.error,
                    borderRadius: 8,
                    margin: 0
                  }
                ]}
              />
              <IconButton
                icon="close"
                size={20}
                onPress={handleCancelSelection}
                iconColor={theme.colors.onSurfaceVariant}
                style={[
                  styles.headerButton,
                  { 
                    backgroundColor: theme.colors.surfaceVariant,
                    borderRadius: 8,
                    margin: 0
                  }
                ]}
              />
            </View>
          ) : (
            <Button
              mode="text"
              icon="filter"
              onPress={() => filterSortSheetRef.current?.open()}
              style={[
                styles.headerButton,
                styles.filterButton,
                { 
                  backgroundColor: (theme as any).dark ? theme.colors.surfaceVariant : theme.colors.primary, 
                }
              ]}
              labelStyle={[styles.headerButtonLabel, { fontSize: 14, color: theme.colors.onPrimary }]}
            >
              Filters
            </Button>
          )}
        </View>
      </Surface>
      <FlatList
        data={filteredProducts}
        renderItem={renderProductCard}
        keyExtractor={keyExtractor}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme?.colors?.primary]}
            tintColor={theme?.colors?.primary}
          />
        }
        ListEmptyComponent={
          <EmptyState
            icon="package"
            title="No Products Found"
            description={searchQuery ? "Try adjusting your search terms" : "Start by adding your first product"}
            actionLabel="Add Product"
            onActionPress={() => addProductSheetRef.current?.expand?.()}
            type="products"
            style={{}}
            iconColor={theme.colors.primary}
            searchQuery={searchQuery}
          />
        }
      />
      {/* Hide FAB in selection mode */}
      {!isSelectionMode && (
        <FAB
          icon="plus"
          onPress={() => addProductSheetRef.current?.expand?.()}
          style={[styles.fab, { backgroundColor: theme.colors.primary }]}
          color={theme.colors.onPrimary}
        />
      )}
      {React.createElement(ProductBottomSheet as any, { ref: addProductSheetRef })}
      {React.createElement(FilterSortBottomSheet as any, {
        ref: filterSortSheetRef,
        filters,
        selectedFilter,
        onSelectFilter: setSelectedFilter,
        sortOptions,
        selectedSort,
        onSelectSort: setSelectedSort,
        onConfirm: () => filterSortSheetRef.current?.close(),
        title: "Filter Products"
      })}
    </View>
  );
};

export default ProductsScreen;