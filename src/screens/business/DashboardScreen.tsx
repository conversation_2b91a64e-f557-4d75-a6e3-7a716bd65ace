import React, { useState, useMemo, useCallback } from 'react';
import { ScrollView, View, StyleSheet, RefreshControl } from 'react-native';
import {
  Text,
  Button,
} from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import InfoCard from '../../components/cards/InfoCard';
import CommonHeader from '../../components/navigation/CommonHeader';
import StatCardGroup from '../../components/ui/StatCardGroup';
import { useData } from '../../context/DataContext';
import { useFinancial } from '../../context/FinancialContext';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import navigationService from '../../services/NavigationService';
import { SPACING, BORDER_RADIUS, COMPONENT_SIZES, TYPOGRAPHY, getBorderColor } from '../../theme/theme';
import { StatCard } from '../../types';


interface DashboardScreenProps {
  navigation: any;
  navigateToTab: (tabName: string) => void;
}

interface DashboardStats {
  todaysSales: number;
  totalOrders: number;
  totalProducts: number;
  totalCustomers: number;
}

interface DashboardCard {
  key: string;
  title: string;
  value: string;
  icon: string;
  iconColor: string;
  onPress: () => void;
}

const DashboardScreen: React.FC<DashboardScreenProps> = ({ navigation, navigateToTab }) => {
  const theme = useTheme();
  const { state } = useData();
  const { profitLossData, derivedData } = useFinancial();
  const insets = useSafeAreaInsets();

  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');

  // Memoize today's date to avoid recalculating
  const today = useMemo(() => new Date().toISOString().split('T')[0], []);

  // Simplified refresh functionality
  const handleRefresh = useCallback(async (): Promise<void> => {
    setRefreshing(true);
    try {
      // Simple refresh simulation
      await new Promise(resolve => setTimeout(resolve, 500));
      LoggingService.info('Dashboard refreshed', 'SCREEN');
    } catch (error) {
      LoggingService.warn('Failed to refresh dashboard', 'SCREEN', error as Error);
    } finally {
      setRefreshing(false);
    }
  }, []);

  // Enhanced navigation handlers with proper data views
  const handleStatCardPress = useCallback((type: string): void => {
    switch (type) {
      case 'sales':
        LoggingService.info('Fast navigation to Sales Reports', 'NAVIGATION');
        try {
          // Navigate to Reports screen
          navigationService.navigate('Reports');
        } catch (error) {
          LoggingService.error('Failed to navigate to Reports', 'NAVIGATION', error as Error);
        }
        break;
      case 'orders':
        LoggingService.info('Fast navigation to Orders', 'NAVIGATION');
        try {
          navigateToTab('Orders');
        } catch (error) {
          LoggingService.error('Failed to navigate to Orders', 'NAVIGATION', error as Error);
        }
        break;
      case 'products':
        LoggingService.info('Fast navigation to Products', 'NAVIGATION');
        try {
          // For products, use the dedicated Products screen
          navigationService.navigate('Products');
        } catch (error) {
          LoggingService.error('Failed to navigate to Products', 'NAVIGATION', error as Error);
        }
        break;
      case 'customers':
        LoggingService.info('Fast navigation to Customers', 'NAVIGATION');
        try {
          // Navigate to dedicated Customers screen
          navigationService.navigate('Customers');
        } catch (error) {
          LoggingService.error('Failed to navigate to Customers', 'NAVIGATION', error as Error);
        }
        break;
      case 'financial':
        LoggingService.info('Fast navigation to Financial', 'NAVIGATION');
        try {
          navigateToTab('Financial');
        } catch (error) {
          LoggingService.error('Failed to navigate to Financial', 'NAVIGATION', error as Error);
        }
        break;
      case 'reports':
        LoggingService.info('Navigating to Advanced Reports', 'NAVIGATION');
        try {
          navigationService.navigate('Reports');
        } catch (error) {
          LoggingService.error('Failed to navigate to Reports', 'NAVIGATION', error as Error);
        }
        break;
    }
  }, [navigateToTab]);

  // Simplified stats calculation
  const dashboardStats = useMemo((): DashboardStats => {
    const todaysSales = state.orders
      .filter((order: any) => order.date === today && order.status === 'Completed')
      .reduce((sum: number, order: any) => sum + order.total, 0);

    return {
      todaysSales,
      totalOrders: state.orders.length,
      totalProducts: state.products.length,
      totalCustomers: state.customers.length,
    };
  }, [state.orders, state.products.length, state.customers.length, today]);

  const dashboardCards = useMemo((): DashboardCard[] => [
    {
      key: 'sales',
      title: 'Today\'s Sales',
      value: `৳${dashboardStats.todaysSales.toFixed(0)}`,
      icon: 'money',
      iconColor: theme.colors.primary,
      onPress: () => handleStatCardPress('sales')
    },
    {
      key: 'orders',
      title: 'Total Orders',
      value: dashboardStats.totalOrders.toString(),
      icon: 'clipboard-text',
      iconColor: theme.colors.secondary,
      onPress: () => handleStatCardPress('orders')
    },
    {
      key: 'products',
      title: 'Total Products',
      value: dashboardStats.totalProducts.toString(),
      icon: 'package',
      iconColor: theme.colors.tertiary,
      onPress: () => handleStatCardPress('products')
    },
    {
      key: 'customers',
      title: 'Total Customers',
      value: dashboardStats.totalCustomers.toString(),
      icon: 'users',
      iconColor: theme.colors.primary,
      onPress: () => handleStatCardPress('customers')
    },
    {
      key: 'profit',
      title: 'Net Profit',
      value: profitLossData ? `৳${(profitLossData as any).profit?.net?.toFixed(0) || '0'}` : '৳0',
      icon: 'chart-line',
      iconColor: (profitLossData as any)?.profit?.net >= 0 ? "#10B981" : "#EF4444",
      onPress: () => handleStatCardPress('financial')
    },
    {
      key: 'expenses',
      title: 'Total Expenses',
      value: `৳${derivedData?.totalExpenses?.toFixed(0) || '0'}`,
      icon: 'receipt',
      iconColor: "#F59E0B",
      onPress: () => handleStatCardPress('financial')
    },
    {
      key: 'reports',
      title: 'View Reports',
      value: 'Advanced Analytics',
      icon: 'chart-bar',
      iconColor: "#8B5CF6",
      onPress: () => handleStatCardPress('reports')
    },
    // Quality Control card removed

  ], [dashboardStats, theme.colors, profitLossData, derivedData, handleStatCardPress]);



  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Dashboard"
        subtitle="Welcome back!"
        searchPlaceholder="Search orders, products, customers..."
        searchType="global"
        searchData={[...state.orders, ...state.products, ...state.customers]}
        searchFields={["name", "customerName", "customer", "title", "description"]}
        onSearchChange={setSearchQuery}
        onSearchResult={(item: any) => {
          LoggingService.debug('Search result selected', 'SEARCH', item);
          // Handle navigation based on item type
          if (item.customerName || item.customer) {
            // It's an order
            LoggingService.info('Navigate to order details', 'NAVIGATION');
          } else if (item.price) {
            // It's a product
            LoggingService.info('Navigate to product details', 'NAVIGATION');
          } else {
            // It's a customer
            LoggingService.info('Navigate to customer details', 'NAVIGATION');
          }
        }}
        onNotificationPress={() => {
          try {
            navigationService.navigate('NotificationsScreen');
          } catch (error) {
            LoggingService.error('Failed to navigate to NotificationsScreen', 'NAVIGATION', error as Error);
          }
        }}
        onProfilePress={() => {
          LoggingService.info('Profile pressed - navigating to MyProfile', 'NAVIGATION');
          try {
            navigationService.navigate('MyProfile');
          } catch (error) {
            LoggingService.error('Failed to navigate to MyProfile', 'NAVIGATION', error as Error);
          }
        }}
      />

      <ScrollView
        style={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      >
        <View style={styles.content}>
          <StatCardGroup
            title="Dashboard Stats"
            cards={dashboardCards as StatCard[]}
            columns={2}
            showTitle={false}
          />
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flex: 1,
  },
  content: {
    padding: SPACING.lg,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: SPACING.md,
    marginHorizontal: -SPACING.xs / 2,
  },
  statCardWrapper: {
    width: '50%',
    paddingHorizontal: SPACING.xs / 2,
    marginBottom: SPACING.xs,
  },
  sectionTitle: {
    fontWeight: '700',
    marginBottom: SPACING.sm,
  },
  financialSection: {
    marginBottom: SPACING.lg,
  },
  financialCardsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.md,
  },
  financialCardWrapper: {
    width: '48%',
  },
  financialIconContainer: {
    width: COMPONENT_SIZES.icon.xxl,
    height: COMPONENT_SIZES.icon.xxl,
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.sm + 2,
  },
  reportsCardWrapper: {
    width: '100%',
  },
  reportsCard: {
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING.lg,
    borderWidth: 1,
  },
  reportsCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  reportsCardText: {
    flex: 1,
    marginLeft: 4,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
});

export default DashboardScreen;