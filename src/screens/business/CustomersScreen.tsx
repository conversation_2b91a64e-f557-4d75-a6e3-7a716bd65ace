import React, { useState, useCallback, useMemo } from 'react';
import { FlatList, View, StyleSheet, RefreshControl, ScrollView, Alert, Pressable, TouchableOpacity, Platform, ListRenderItem } from 'react-native';
import {
  Text,
  Button,
  Surface,
  Menu,
  TextInput,
  FAB,
  IconButton,
} from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';


import UnifiedWrapper from '../../components/bottomsheets/UnifiedWrapper';
import CustomerCard from '../../components/cards/CustomerCard';
import FilterSortBottomSheet from '../../components/filters/FilterSortBottomSheet';
import EmptyState from '../../components/ui/EmptyState';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import navigationService from '../../services/NavigationService';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';


interface CustomersScreenProps {
  navigation: any;
  route?: {
    params?: {
      selectMode?: boolean;
      onCustomerSelect?: (customer: any) => void;
      returnTo?: string;
    };
  };
}

interface SortOption {
  key: string;
  label: string;
}

interface SelectedSort {
  key: string;
  direction: 'asc' | 'desc';
}

interface EnhancedCustomer {
  id: string | number;
  name: string;
  email?: string;
  phone?: string;
  totalSpent: number;
  totalOrders: number;
  lastOrderDate: Date | null;
  [key: string]: any;
}

interface ItemRowProps {
  active: boolean;
  onPress: () => void;
  children: React.ReactNode;
  theme: any;
}

const CustomersScreen: React.FC<CustomersScreenProps> = ({ navigation, route }) => {
  const theme = useTheme();
  const { state, actions } = useData();
  const { orders, customers } = state;
  const insets = useSafeAreaInsets();

  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedFilter, setSelectedFilter] = useState<string>('All');
  const [menuVisible, setMenuVisible] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [selectedSort, setSelectedSort] = useState<SelectedSort>({ key: 'name', direction: 'asc' });
  const [tabIndex, setTabIndex] = useState<number>(0);
  const [routes] = useState([
    { key: 'filters', title: 'Filters' },
    { key: 'sorting', title: 'Sorting' },
  ]);
  
  // Selection state
  const [selectedCustomers, setSelectedCustomers] = useState<Set<string>>(new Set());
  const [isSelectionMode, setIsSelectionMode] = useState<boolean>(false);
  
  const optionsSheetRef = React.useRef<any>(null);
  const addCustomerSheetRef = React.useRef<any>(null);
  const filterSortSheetRef = React.useRef<any>(null);

  const filters = ['All', 'Recent', 'Active', 'New', 'VIP'];
  const selectMode = route?.params?.selectMode || false;
  const onCustomerSelect = route?.params?.onCustomerSelect;
  const returnTo = route?.params?.returnTo;

  const getFilterCount = useCallback((filter: string): number => {
    if (!customers) return 0;
    return customers.length;
  }, [customers]);

  const sortOptions: SortOption[] = [
    { key: 'name', label: 'Name' },
    { key: 'createdAt', label: 'Date Added' },
    { key: 'lastOrderDate', label: 'Last Order' },
    { key: 'totalSpent', label: 'Total Spent' },
    { key: 'totalOrders', label: 'Total Orders' },
  ];

  const enhancedCustomers = useMemo((): EnhancedCustomer[] => {
    if (!customers) return [];
    return customers.map((customer: any) => {
        const customerOrders = (orders || []).filter((order: any) => String(order.customerId) === String(customer.id));
        const totalSpent = customerOrders.reduce((sum: number, order: any) => sum + (order.total || order.totalAmount || 0), 0);
        const lastOrderDate = customerOrders.length > 0
            ? new Date(Math.max(...customerOrders.map((o: any) => new Date(o.createdAt).getTime())))
            : null;

        return {
            ...customer,
            totalSpent,
            totalOrders: customerOrders.length,
            lastOrderDate,
        };
    });
  }, [customers, orders]);

  const filteredCustomers = useMemo((): EnhancedCustomer[] => {
    let filtered = [...enhancedCustomers];

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter((customer: EnhancedCustomer) =>
        customer.name?.toLowerCase().includes(query) ||
        customer.email?.toLowerCase().includes(query) ||
        customer.phone?.includes(query)
      );
    }
    // Add logic for other filters here if needed

    filtered.sort((a: EnhancedCustomer, b: EnhancedCustomer) => {
        const key = selectedSort.key;
        let valA = (a as any)[key];
        let valB = (b as any)[key];

        if (typeof valA === 'string') valA = valA.toLowerCase();
        if (typeof valB === 'string') valB = valB.toLowerCase();
        
        if (valA < valB) return selectedSort.direction === 'asc' ? -1 : 1;
        if (valA > valB) return selectedSort.direction === 'asc' ? 1 : -1;
        return 0;
    });

    return filtered;
  }, [enhancedCustomers, searchQuery, selectedFilter, selectedSort]);

  const handleRefresh = useCallback(async (): Promise<void> => {
    setRefreshing(true);
    try {
      // Simulate refresh - in real app this would reload data
      await new Promise(resolve => setTimeout(resolve, 1000));
      LoggingService.info('Customers data refreshed', 'SCREEN');
    } catch (error) {
      LoggingService.error('Failed to refresh customers data', 'SCREEN', error as Error);
    } finally {
      setRefreshing(false);
    }
  }, []);

  const handleCustomerSelect = (customer: EnhancedCustomer): void => {
    if (selectMode && onCustomerSelect) {
      onCustomerSelect(customer);
      navigation.goBack();
      return;
    }
    // Navigate to the new CustomerDetailsScreen
    navigationService.navigate('CustomerDetails', { customer });
  };

  // Selection handlers - Optimized with useCallback and memoization
  const handleCustomerLongPress = useCallback((customer: EnhancedCustomer) => {
    if (selectMode) return; // Don't enter selection mode if already in select mode
    
    setIsSelectionMode(true);
    setSelectedCustomers(new Set([customer.id.toString()]));
    
    LoggingService.info('Entered customer selection mode', 'CUSTOMERS', {
      customerId: customer.id,
      customerName: customer.name
    });
  }, [selectMode]);

  const handleCustomerPress = useCallback((customer: EnhancedCustomer) => {
    if (isSelectionMode) {
      // Card press in selection mode - use same multi-select logic as checkbox
      const customerId = customer.id.toString();
      setSelectedCustomers(prev => {
        const newSelected = new Set(prev);
        if (newSelected.has(customerId)) {
          newSelected.delete(customerId);
          // Exit selection mode if no items selected
          if (newSelected.size === 0) {
            setIsSelectionMode(false);
          }
        } else {
          newSelected.add(customerId);
        }
        return newSelected;
      });
    } else {
      // Normal press behavior
      handleCustomerSelect(customer);
    }
  }, [isSelectionMode, handleCustomerSelect]);

  const handleCustomerCheckboxPress = useCallback((customer: EnhancedCustomer) => {
    if (isSelectionMode) {
      // Checkbox press - toggle individual customer selection
      const customerId = customer.id.toString();
      setSelectedCustomers(prev => {
        const newSelected = new Set(prev);
        if (newSelected.has(customerId)) {
          newSelected.delete(customerId);
          // Exit selection mode if no items selected
          if (newSelected.size === 0) {
            setIsSelectionMode(false);
          }
        } else {
          newSelected.add(customerId);
        }
        return newSelected;
      });
    }
  }, [isSelectionMode]);

  const handleSelectAll = useCallback(() => {
    setSelectedCustomers(prev => {
      if (prev.size === filteredCustomers.length) {
        // Deselect all
        setIsSelectionMode(false);
        return new Set();
      } else {
        // Select all
        return new Set(filteredCustomers.map(customer => customer.id.toString()));
      }
    });
  }, [filteredCustomers]);

  const handleBulkDelete = useCallback(() => {
    if (selectedCustomers.size === 0) return;

    Alert.alert(
      'Delete Customers',
      `Are you sure you want to delete ${selectedCustomers.size} customer${selectedCustomers.size > 1 ? 's' : ''}? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
                        try {
              const selectedIds = Array.from(selectedCustomers);
              const deletedCount = selectedCustomers.size;
              
              // console.log('Deleting customers:', selectedIds);
              
              for (const id of selectedIds) {
                // console.log('Deleting customer with ID:', id);
                await actions.deleteCustomer(id);
                // console.log('Successfully deleted customer with ID:', id);
              }
              
              // console.log('All customers deleted, updating UI state');
              setSelectedCustomers(new Set());
              setIsSelectionMode(false);
              
              // Reload data from database to ensure UI is in sync
              // console.log('Reloading data from database...');
              await actions.reloadData();
              // console.log('Data reloaded successfully');
              
              Alert.alert('Success', `Successfully deleted ${deletedCount} customer${deletedCount > 1 ? 's' : ''}.`);
              
              LoggingService.info('Bulk customer deletion completed', 'CUSTOMERS', {
                deletedCount,
                customerIds: selectedIds
              });
            } catch (error) {
              // console.error('Error deleting customers:', error);
              LoggingService.error('Bulk customer deletion failed', 'CUSTOMERS', error as Error);
              Alert.alert('Error', 'Failed to delete some customers. Please try again.');
            }
          },
        },
      ]
    );
  }, [selectedCustomers, actions]);

  const handleBulkExport = useCallback(() => {
    if (selectedCustomers.size === 0) return;

    const selectedCustomerData = filteredCustomers.filter(customer => 
      selectedCustomers.has(customer.id.toString())
    );

    // Create CSV content
    const headers = ['Name', 'Email', 'Phone', 'Total Spent', 'Total Orders', 'Last Order'];
    const csvContent = [
      headers.join(','),
      ...selectedCustomerData.map(customer =>
        [
          `"${customer.name}"`,
          `"${customer.email || ''}"`,
          `"${customer.phone || ''}"`,
          customer.totalSpent,
          customer.totalOrders,
          customer.lastOrderDate ? `"${customer.lastOrderDate.toLocaleDateString()}"` : ''
        ].join(',')
      )
    ].join('\n');

    // In a real app, you would use a file system library to save the CSV
    // console.log('Export data:', csvContent);
    
    Alert.alert('Success', `Successfully exported ${selectedCustomers.size} customer${selectedCustomers.size > 1 ? 's' : ''}.`);
    
    LoggingService.info('Bulk customer export completed', 'CUSTOMERS', {
      exportedCount: selectedCustomers.size
    });
  }, [selectedCustomers, filteredCustomers]);

  const handleCancelSelection = useCallback(() => {
    setSelectedCustomers(new Set());
    setIsSelectionMode(false);
  }, []);

  // Memoized selection check function for better performance
  const isCustomerSelected = useCallback((customerId: string | number) => {
    return selectedCustomers.has(customerId.toString());
  }, [selectedCustomers]);

  // Optimized renderCustomerCard with proper memoization
  const renderCustomerCard: ListRenderItem<EnhancedCustomer> = useCallback(({ item: customer }) => {
    const customerId = customer.id.toString();
    const selected = isSelectionMode && isCustomerSelected(customer.id);
    
    return (
      <View style={{ marginBottom: 12 }}>
        <CustomerCard
          customer={customer}
          onPress={() => handleCustomerPress(customer)}
          onLongPress={() => handleCustomerLongPress(customer)}
          onCheckboxPress={() => handleCustomerCheckboxPress(customer)}
          selected={selected}
          showSelectionIndicator={isSelectionMode}
        />
      </View>
    );
  }, [handleCustomerPress, handleCustomerLongPress, handleCustomerCheckboxPress, isSelectionMode, isCustomerSelected]);

  const keyExtractor = useCallback((item: EnhancedCustomer) => item.id?.toString() || '', []);

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
    },
    headerContainer: {
      paddingHorizontal: 16,
      paddingBottom: 12,
      elevation: 2,
    },
    searchBox: {
      backgroundColor: 'transparent',
      marginBottom: 12,
    },
    searchOutline: {
      borderRadius: 8,
    },
    filtersContentContainer: {
      paddingLeft: 0,
    },
    filter: {
      marginRight: 8,
      borderWidth: 1,
    },
    filterText: {
      fontSize: 12,
    },
    listHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
      paddingHorizontal: 16,
    },
    title: {
      fontWeight: 'bold',
    },
    headerActions: {
      flexDirection: 'row',
      gap: 8,
    },
    headerButton: {
      borderRadius: 8,
    },
    headerButtonLabel: {
      fontSize: 12,
    },
    sortButton: {
      borderRadius: 8,
    },
    sortButtonLabel: {
      fontSize: 12,
    },
    listContainer: {
      padding: 16,
      paddingBottom: 100,
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 32,
    },
    emptyTitle: {
      marginTop: 16,
      fontWeight: 'bold',
    },
    emptySubtitle: {
      marginTop: 8,
      color: theme.colors.secondary,
    },
    fab: {
      position: 'absolute',
      margin: 16,
      right: 0,
      bottom: 0,
    },

  }), [theme, insets]);

  // Helper for switch-like tab bar
  const renderSwitchTabBar = (props: any) => {
    const { navigationState, position, jumpTo } = props;
    const { theme } = props;
    return (
      <View style={{
        flexDirection: 'row',
        backgroundColor: theme.colors.surfaceVariant,
        borderRadius: 12,
        marginHorizontal: 16,
        marginTop: 12,
        marginBottom: 8,
        padding: 4,
        width: 'auto',
        alignSelf: 'stretch',
      }}>
        {navigationState.routes.map((route: any, i: number) => {
          const active = navigationState.index === i;
          return (
            <Button
              key={route.key}
              mode={active ? 'contained' : 'text'}
              onPress={() => jumpTo(route.key)}
              style={{
                flex: 1,
                borderRadius: 10,
                backgroundColor: active ? theme.colors.secondaryContainer : 'transparent',
                elevation: 0,
                marginHorizontal: 2,
                minHeight: 36,
              }}
              labelStyle={{
                color: active ? theme.colors.onSecondaryContainer : theme.colors.onSurfaceVariant,
                fontWeight: 'bold',
                fontSize: 14,
                textTransform: 'none',
              }}
              contentStyle={{ height: 36 }}
            >
              {route.title}
            </Button>
          );
        })}
      </View>
    );
  };

  // Item row with platform feedback
  const ItemRow = React.memo<ItemRowProps>(({ active, onPress, children, theme }) => (
    Platform.OS === 'android' ? (
      <Pressable
        onPress={onPress}
        android_ripple={{ color: `${theme.colors.primary  }22`, borderless: false }}
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          backgroundColor: active ? theme.colors.surface : 'transparent',
          borderRadius: 12,
          paddingVertical: 12,
          paddingHorizontal: 16,
          marginBottom: 4,
        }}
      >
        {children}
      </Pressable>
    ) : (
      <TouchableOpacity
        onPress={onPress}
        activeOpacity={0.6}
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          backgroundColor: active ? theme.colors.surface : 'transparent',
          borderRadius: 12,
          paddingVertical: 12,
          paddingHorizontal: 16,
          marginBottom: 4,
        }}
      >
        {children}
      </TouchableOpacity>
    )
  ));

  // Memoized tab content for filters
  const FilterTab = React.memo<{
    filters: string[];
    selectedFilter: string;
    setSelectedFilter: (filter: string) => void;
    theme: any;
  }>(({ filters, selectedFilter, setSelectedFilter, theme }) => {
    const handleSelect = React.useCallback((filter: string) => {
      if (selectedFilter !== filter) setSelectedFilter(filter);
    }, [selectedFilter, setSelectedFilter]);
    return (
      <View style={{ padding: 8 }}>
        {filters.map((filter) => {
          const active = selectedFilter === filter;
          return (
            <ItemRow key={filter} active={active} onPress={() => handleSelect(filter)} theme={theme}>
              <Text
                style={{
                  flex: 1,
                  textAlign: 'left',
                  color: active ? theme.colors.primary : theme.colors.onSurfaceVariant,
                  fontWeight: active ? 'bold' : 'normal',
                  fontSize: 14,
                }}
              >
                {filter}
              </Text>
              <PhosphorIcon
                name={active ? 'checkmark-circle' : 'x-circle'}
                size={24}
                color={active ? theme.colors.primary : theme.colors.onSurfaceVariant}
              />
            </ItemRow>
          );
        })}
      </View>
    );
  });

  // Memoized tab content for sorting
  const SortTab = React.memo<{
    sortOptions: SortOption[];
    selectedSort: SelectedSort;
    setSelectedSort: (sort: SelectedSort | ((prev: SelectedSort) => SelectedSort)) => void;
    theme: any;
  }>(({ sortOptions, selectedSort, setSelectedSort, theme }) => {
    const handleSelect = React.useCallback((option: SortOption) => {
      if (selectedSort.key !== option.key) {
        setSelectedSort({ key: option.key, direction: 'asc' });
      } else {
        setSelectedSort(prev => ({ key: option.key, direction: prev.direction === 'asc' ? 'desc' : 'asc' }));
      }
    }, [selectedSort, setSelectedSort]);
    return (
      <View style={{ padding: 8 }}>
        {sortOptions.map((option) => {
          const active = selectedSort.key === option.key;
          return (
            <ItemRow key={option.key} active={active} onPress={() => handleSelect(option)} theme={theme}>
              <Text
                style={{
                  flex: 1,
                  textAlign: 'left',
                  color: active ? theme.colors.primary : theme.colors.onSurfaceVariant,
                  fontWeight: active ? 'bold' : 'normal',
                  fontSize: 14,
                }}
              >
                {option.label} {active ? (selectedSort.direction === 'asc' ? '↑' : '↓') : ''}
              </Text>
              <PhosphorIcon
                name={active ? 'checkmark-circle' : 'x-circle'}
                size={24}
                color={active ? theme.colors.primary : theme.colors.onSurfaceVariant}
              />
            </ItemRow>
          );
        })}
      </View>
    );
  });

  // Memoized header title and actions for selection mode
  const headerTitle = useMemo(() => {
    if (isSelectionMode) {
      return `Selected (${selectedCustomers.size})`;
    }
    return `Customers (${filteredCustomers.length})`;
  }, [isSelectionMode, selectedCustomers.size, filteredCustomers.length]);



  return (
    <View style={[styles.container, { backgroundColor: theme?.colors?.background }]}>

      {/* Main Header */}
      <Surface style={[styles.headerContainer, { paddingTop: insets.top + 8, backgroundColor: theme.colors.surface }]}> 
        <TextInput
          mode="outlined"
          placeholder="Search customers..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          style={[
            styles.searchBox,
            { height: 48, backgroundColor: theme.colors.background }
          ]}
          outlineStyle={styles.searchOutline}
          left={<TextInput.Icon icon="magnify" />}
          right={searchQuery ? <TextInput.Icon icon="close" onPress={() => setSearchQuery('')} /> : null}
        />
        <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', minHeight: 36 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
            {isSelectionMode && (
              <IconButton
                icon={selectedCustomers.size === filteredCustomers.length ? "checkbox-marked" : "checkbox-blank-outline"}
                size={24}
                onPress={handleSelectAll}
                iconColor={theme.colors.onSurface}
                style={{ marginRight: 8, margin: 0 }}
              />
            )}
            <Text
              style={[
                { fontSize: 18, fontWeight: "400", textAlign: 'left', flex: 1 },
                styles.title,
                { color: theme.colors.onSurface }
              ]}>
              {headerTitle}
            </Text>
          </View>
          {isSelectionMode ? (
            <View style={{ flexDirection: 'row', gap: 8 }}>
              <IconButton
                icon="delete"
                size={20}
                onPress={handleBulkDelete}
                iconColor={theme.colors.onError}
                style={[
                  styles.headerButton,
                  { 
                    backgroundColor: theme.colors.error,
                    borderRadius: 8,
                    margin: 0
                  }
                ]}
              />
              <IconButton
                icon="close"
                size={20}
                onPress={handleCancelSelection}
                iconColor={theme.colors.onSurfaceVariant}
                style={[
                  styles.headerButton,
                  { 
                    backgroundColor: theme.colors.surfaceVariant,
                    borderRadius: 8,
                    margin: 0
                  }
                ]}
              />
            </View>
          ) : (
            <Button
              mode="text"
              icon={({size}) => (
                <PhosphorIcon name="filter" size={20} color={theme.colors.onPrimary} />
              )}
              onPress={() => filterSortSheetRef.current?.open()}
              style={[
                styles.headerButton,
                { 
                  marginLeft: 1, 
                  backgroundColor: (theme as any).dark ? theme.colors.surfaceVariant : theme.colors.primary, 
                  borderRadius: 8, 
                  paddingHorizontal: 0 
                }
              ]}
              labelStyle={[styles.headerButtonLabel, { fontSize: 14, color: theme.colors.onPrimary }]}
            >
              Filters
            </Button>
          )}
        </View>
      </Surface>
      <FlatList
        data={filteredCustomers}
        renderItem={renderCustomerCard}
        keyExtractor={keyExtractor}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme?.colors?.primary]}
            tintColor={theme?.colors?.primary}
          />
        }
        ListEmptyComponent={
          <EmptyState
            icon="account-group"
            title="No Customers Found"
            description={searchQuery ? "Try adjusting your search terms" : "Start by adding your first customer"}
            actionLabel="Add Customer"
            onActionPress={() => addCustomerSheetRef.current?.open?.()}
            type="customers"
            style={{}}
            iconColor={theme.colors.primary}
            searchQuery={searchQuery}
          />
        }
      />
      {/* Hide FAB in selection mode */}
      {!isSelectionMode && (
        <FAB
          icon="plus"
          onPress={() => addCustomerSheetRef.current?.open?.()}
          style={[styles.fab, { backgroundColor: theme.colors.primary }]}
          color={theme.colors.onPrimary}
        />
      )}
      <UnifiedWrapper
        ref={addCustomerSheetRef}
        type="add-customer"
        title="Add Customer"
        mode="add"
        onSave={(customerData) => {
          // Handle customer save logic here
          console.log('Customer saved:', customerData);
        }}
      />
      {React.createElement(FilterSortBottomSheet as any, {
        ref: filterSortSheetRef,
        filters,
        selectedFilter,
        onSelectFilter: setSelectedFilter,
        sortOptions,
        selectedSort,
        onSelectSort: setSelectedSort,
        onConfirm: () => filterSortSheetRef.current?.close(),
        title: "Filter Customers"
      })}
    </View>
  );
};

export default CustomersScreen;