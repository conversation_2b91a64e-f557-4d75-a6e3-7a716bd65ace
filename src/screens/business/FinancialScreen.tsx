// @ts-nocheck
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';

import CommonHeader from '../../components/navigation/CommonHeader';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import FinancialService from '../../services/financialService';

interface FinancialMetrics {
  totalRevenue: number;
  totalExpenses: number;
  totalProfit: number;
  profitMargin: number;
  outletCount: number;
  averageOrderValue: number;
}

interface OutletFinancialData {
  outletId: string;
  outletName: string;
  revenue: number;
  expenses: number;
  profit: number;
  profitMargin: number;
  orderCount: number;
}

const FinancialScreen: React.FC = () => {
  const theme = useTheme();
  const { state } = useData();
  const currentOutlet = state.activeOutlet;
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [metrics, setMetrics] = useState<FinancialMetrics>({
    totalRevenue: 0,
    totalExpenses: 0,
    totalProfit: 0,
    profitMargin: 0,
    outletCount: 0,
    averageOrderValue: 0,
  });
  const [outletData, setOutletData] = useState<OutletFinancialData[]>([]);
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [selectedOutlet, setSelectedOutlet] = useState<string | null>(null);
  const [showProfitLoss, setShowProfitLoss] = useState(false);
  const [showPaymentAnalytics, setShowPaymentAnalytics] = useState(false);
  const [showTaxSummary, setShowTaxSummary] = useState(false);
  const [showCashReconciliation, setShowCashReconciliation] = useState(false);
  const [showExpenseManager, setShowExpenseManager] = useState(false);

  const periods = [
    { id: 'week', label: 'This Week' },
    { id: 'month', label: 'This Month' },
    { id: 'quarter', label: 'This Quarter' },
    { id: 'year', label: 'This Year' },
  ];

  useEffect(() => {
    loadFinancialData();
  }, [selectedPeriod, selectedOutlet]);

  const loadFinancialData = async () => {
    try {
      setLoading(true);
      const endDate = new Date().toISOString();
      const startDate = getStartDate(selectedPeriod);

      // Load consolidated financial report
      const consolidatedReport = await FinancialService.generateConsolidatedFinancialReport(
        startDate,
        endDate
      );

      // Update metrics
      setMetrics({
        totalRevenue: consolidatedReport.totalRevenue,
        totalExpenses: consolidatedReport.totalExpenses,
        totalProfit: consolidatedReport.totalProfit,
        profitMargin: consolidatedReport.overallProfitMargin,
        outletCount: consolidatedReport.outletMetrics.length,
        averageOrderValue: consolidatedReport.outletMetrics.reduce(
          (sum, outlet) => sum + outlet.averageOrderValue,
          0
        ) / consolidatedReport.outletMetrics.length || 0,
      });

      // Update outlet data
      setOutletData(
        consolidatedReport.outletMetrics.map(outlet => ({
          outletId: outlet.outletId,
          outletName: outlet.outletName,
          revenue: outlet.revenue,
          expenses: outlet.expenses,
          profit: outlet.profit,
          profitMargin: outlet.profitMargin,
          orderCount: outlet.orderCount,
        }))
      );
    } catch (error) {
      // console.error('Failed to load financial data:', error);
      Alert.alert('Error', 'Failed to load financial data');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadFinancialData();
    setRefreshing(false);
  };

  const getStartDate = (period: string): string => {
    const now = new Date();
    switch (period) {
      case 'week':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
      case 'month':
        return new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
      case 'quarter':
        const quarter = Math.floor(now.getMonth() / 3);
        return new Date(now.getFullYear(), quarter * 3, 1).toISOString();
      case 'year':
        return new Date(now.getFullYear(), 0, 1).toISOString();
      default:
        return new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
    }
  };

  const formatCurrency = (amount: number): string => {
    return `$${amount.toFixed(2)}`;
  };

  const formatPercentage = (value: number): string => {
    return `${value.toFixed(1)}%`;
  };

  const getProfitColor = (profit: number): string => {
    return profit >= 0 ? '#4CAF50' : theme.colors.error;
  };

  const handleOutletSelect = (outletId: string | null) => {
    setSelectedOutlet(outletId);
  };

  const filteredOutletData = selectedOutlet
    ? outletData.filter(outlet => outlet.outletId === selectedOutlet)
    : outletData;

  const statCards = [
    {
      title: 'Total Revenue',
      value: formatCurrency(metrics.totalRevenue),
      subtitle: `${selectedPeriod} period`,
      icon: '💰',
      color: theme.colors.primary,
    },
    {
      title: 'Total Expenses',
      value: formatCurrency(metrics.totalExpenses),
      subtitle: `${selectedPeriod} period`,
      icon: '💸',
      color: '#FF9800',
    },
    {
      title: 'Net Profit',
      value: formatCurrency(metrics.totalProfit),
      subtitle: `${formatPercentage(metrics.profitMargin)} margin`,
      icon: '📈',
      color: getProfitColor(metrics.totalProfit),
    },
    {
      title: 'Avg Order Value',
      value: formatCurrency(metrics.averageOrderValue),
      subtitle: `${metrics.outletCount} outlets`,
      icon: '🛍️',
      color: theme.colors.primary,
    },
  ];

  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <CommonHeader
          title="Financial Dashboard"
          subtitle="Multi-outlet financial overview"
        />
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: theme.colors.on}]}>
            Loading financial data...
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Financial Dashboard"
        subtitle="Multi-outlet financial overview"
      />

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Period Selector */}
        <View style={styles.periodSelector}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {periods.map((period) => (
              <TouchableOpacity
                key={period.id}
                style={[
                  styles.period,
                  selectedPeriod === period.id && { backgroundColor: theme.colors.primary }
                ]}
                onPress={() => setSelectedPeriod(period.id)}
              >
                <Text style={[
                  styles.periodText,
                  { color: selectedPeriod === period.id ? '#FFFFFF' : theme.colors.on}
                ]}>
                  {period.label}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Stats Cards */}
        <View style={styles.statsContainer}>
          {statCards.map((card, index) => (
            <View key={index} style={[styles.statCard, { backgroundColor: theme.colors.surface }]}>
              <Text style={[styles.statValue, { color: theme.colors.on}]}>
                {card.value}
              </Text>
              <Text style={[styles.statTitle, { color: theme.colors.onVariant }]}>
                {card.title}
              </Text>
              <Text style={[styles.statSubtitle, { color: theme.colors.onVariant }]}>
                {card.subtitle}
              </Text>
            </View>
          ))}
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.on}]}>
            Quick Actions
          </Text>
          <View style={styles.actions}>
            <TouchableOpacity
              style={[styles.action, { backgroundColor: theme.colors.primary }]}
              onPress={() => setShowExpenseManager(true)}
            >
              <Text style={[styles.actionText, { color: '#FFFFFF' }]}>
                Add Expense
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.action, { backgroundColor: '#FF9800' }]}
              onPress={() => setShowCashReconciliation(true)}
            >
              <Text style={[styles.actionText, { color: '#FFFFFF' }]}>
                Cash Reconciliation
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.action, { backgroundColor: '#4CAF50' }]}
              onPress={() => setShowProfitLoss(true)}
            >
              <Text style={[styles.actionText, { color: '#FFFFFF' }]}>
                P&L Report
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.action, { backgroundColor: theme.colors.primary }]}
              onPress={() => setShowPaymentAnalytics(true)}
            >
              <Text style={[styles.actionText, { color: '#FFFFFF' }]}>
                Payment Analytics
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.action, { backgroundColor: theme.colors.secondary }]}
              onPress={() => setShowTaxSummary(true)}
            >
              <Text style={[styles.actionText, { color: '#FFFFFF' }]}>
                Tax Summary
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Outlet Performance */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: theme.colors.on}]}>
              Outlet Performance
            </Text>
            <TouchableOpacity
              onPress={() => handleOutletSelect(null)}
              style={[
                styles.filter,
                { backgroundColor: selectedOutlet ? theme.colors.surface : theme.colors.primary }
              ]}
            >
              <Text style={[
                styles.filterText,
                { color: selectedOutlet ? theme.colors.on: '#FFFFFF' }
              ]}>
                All Outlets
              </Text>
            </TouchableOpacity>
          </View>

          {filteredOutletData.map((outlet) => (
            <View key={outlet.outletId} style={[styles.outletCard, { backgroundColor: theme.colors.surface }]}>
              <View style={styles.outletHeader}>
                <Text style={[styles.outletName, { color: theme.colors.on}]}>
                  {outlet.outletName}
                </Text>
                <TouchableOpacity
                  onPress={() => handleOutletSelect(outlet.outletId)}
                  style={[
                    styles.outlet,
                    { backgroundColor: selectedOutlet === outlet.outletId ? theme.colors.primary : theme.colors.surfaceVariant }
                  ]}
                >
                  <Text style={[
                    styles.outletText,
                    { color: selectedOutlet === outlet.outletId ? '#FFFFFF' : theme.colors.on}
                  ]}>
                    View
                  </Text>
                </TouchableOpacity>
              </View>

              <View style={styles.outletMetrics}>
                <View style={styles.metric}>
                  <Text style={[styles.metricLabel, { color: theme.colors.onVariant }]}>
                    Revenue
                  </Text>
                  <Text style={[styles.metricValue, { color: theme.colors.on}]}>
                    {formatCurrency(outlet.revenue)}
                  </Text>
                </View>

                <View style={styles.metric}>
                  <Text style={[styles.metricLabel, { color: theme.colors.onVariant }]}>
                    Expenses
                  </Text>
                  <Text style={[styles.metricValue, { color: theme.colors.on}]}>
                    {formatCurrency(outlet.expenses)}
                  </Text>
                </View>

                <View style={styles.metric}>
                  <Text style={[styles.metricLabel, { color: theme.colors.onVariant }]}>
                    Profit
                  </Text>
                  <Text style={[styles.metricValue, { color: getProfitColor(outlet.profit) }]}>
                    {formatCurrency(outlet.profit)}
                  </Text>
                </View>

                <View style={styles.metric}>
                  <Text style={[styles.metricLabel, { color: theme.colors.onVariant }]}>
                    Margin
                  </Text>
                  <Text style={[styles.metricValue, { color: getProfitColor(outlet.profit) }]}>
                    {formatPercentage(outlet.profitMargin)}
                  </Text>
                </View>
              </View>
            </View>
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  periodSelector: {
    marginBottom: 16,
  },
  period: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: '#F0F0F0',
  },
  periodText: {
    fontSize: 14,
    fontWeight: '500',
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 24,
  },
  statCard: {
    flex: 1,
    minWidth: '45%',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  statSubtitle: {
    fontSize: 12,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  actions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  action: {
    flex: 1,
    minWidth: '45%',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  actionText: {
    fontSize: 14,
    fontWeight: '600',
  },
  filter: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  filterText: {
    fontSize: 12,
    fontWeight: '500',
  },
  outletCard: {
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
  },
  outletHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  outletName: {
    fontSize: 16,
    fontWeight: '600',
  },
  outlet: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  outletText: {
    fontSize: 12,
    fontWeight: '500',
  },
  outletMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  metric: {
    alignItems: 'center',
  },
  metricLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  metricValue: {
    fontSize: 14,
    fontWeight: '600',
  },
});

export default FinancialScreen;
