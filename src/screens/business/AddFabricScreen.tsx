import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Alert,
} from 'react-native';
import {
  Card,
  Title,
  HelperText,
  Chip,
  FAB,
  SegmentedButtons,
  TextInput,
} from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
// Removed unused MaterialCommunityIcons import

import CommonHeader from '../../components/navigation/CommonHeader';
import Button from '../../components/ui/Button';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { QualityGrade, InventoryItemType } from '../../types';
import { AddFabricScreenProps } from '../../types/navigation';



interface FabricFormData {
  name: string;
  description: string;
  sku: string;
  barcode: string;
  category: string;
  subcategory: string;
  composition: string;
  color: string;
  pattern: string;
  texture: string;
  weight: string;
  width: string;
  careInstructions: string;
  costPrice: string;
  sellingPrice: string;
  unit: string;
  minimumOrder: string;
  totalQuantity: string;
  reorderLevel: string;
  maxStockLevel: string;
  qualityGrade: QualityGrade;
  supplierName: string;
  supplierContact: string;
  supplierPhone: string;
  supplierEmail: string;
  supplierAddress: string;
  supplierPaymentTerms: string;
  supplierLeadTime: string;
  supplierMinimumOrder: string;
  supplierRating: string;
  tags: string[];
}

const AddFabricScreen: React.FC<AddFabricScreenProps> = ({ navigation }) => {
  const theme = useTheme();
  const { actions } = useData();
  const insets = useSafeAreaInsets();

  const [formData, setFormData] = useState<FabricFormData>({
    name: '',
    description: '',
    sku: '',
    barcode: '',
    category: '',
    subcategory: '',
    composition: '',
    color: '',
    pattern: '',
    texture: '',
    weight: '',
    width: '',
    careInstructions: '',
    costPrice: '',
    sellingPrice: '',
    unit: 'meter',
    minimumOrder: '',
    totalQuantity: '',
    reorderLevel: '',
    maxStockLevel: '',
    qualityGrade: 'B',
    supplierName: '',
    supplierContact: '',
    supplierPhone: '',
    supplierEmail: '',
    supplierAddress: '',
    supplierPaymentTerms: '',
    supplierLeadTime: '',
    supplierMinimumOrder: '',
    supplierRating: '3',
    tags: [],
  });

  const [errors, setErrors] = useState<Partial<FabricFormData>>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [newTag, setNewTag] = useState<string>('');

  const units = ['meter', 'yard', 'piece'];
  const qualityGrades: QualityGrade[] = ['A', 'B', 'C', 'D'];
  const commonPatterns = ['Solid', 'Striped', 'Floral', 'Geometric', 'Abstract', 'Paisley', 'Polka Dot'];
  const commonTextures = ['Smooth', 'Textured', 'Rough', 'Silky', 'Woolly', 'Crisp', 'Soft'];

  const validateForm = (): boolean => {
    const newErrors: Partial<FabricFormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Fabric name is required';
    }

    if (!formData.sku.trim()) {
      newErrors.sku = 'SKU is required';
    }

    if (!formData.category.trim()) {
      newErrors.category = 'Category is required';
    }

    if (!formData.color.trim()) {
      newErrors.color = 'Color is required';
    }

    if (!formData.costPrice.trim()) {
      newErrors.costPrice = 'Cost price is required';
    } else if (isNaN(Number(formData.costPrice)) || Number(formData.costPrice) <= 0) {
      newErrors.costPrice = 'Cost price must be a positive number';
    }

    if (!formData.sellingPrice.trim()) {
      newErrors.sellingPrice = 'Selling price is required';
    } else if (isNaN(Number(formData.sellingPrice)) || Number(formData.sellingPrice) <= 0) {
      newErrors.sellingPrice = 'Selling price must be a positive number';
    }

    if (!formData.totalQuantity.trim()) {
      newErrors.totalQuantity = 'Total quantity is required';
    } else if (isNaN(Number(formData.totalQuantity)) || Number(formData.totalQuantity) < 0) {
      newErrors.totalQuantity = 'Total quantity must be a non-negative number';
    }

    if (!formData.supplierName.trim()) {
      newErrors.supplierName = 'Supplier name is required';
    }

    if (!formData.supplierPhone.trim()) {
      newErrors.supplierPhone = 'Supplier phone is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async (): Promise<void> => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const fabricData = {
        type: 'fabric' as InventoryItemType,
        name: formData.name.trim(),
        description: formData.description.trim(),
        sku: formData.sku.trim(),
        barcode: formData.barcode.trim() || undefined,
        category: formData.category.trim(),
        subcategory: formData.subcategory.trim(),
        supplier: {
          id: `supplier_${Date.now()}`,
          name: formData.supplierName.trim(),
          contactPerson: formData.supplierContact.trim(),
          phone: formData.supplierPhone.trim(),
          email: formData.supplierEmail.trim(),
          address: formData.supplierAddress.trim(),
          paymentTerms: formData.supplierPaymentTerms.trim(),
          leadTime: Number(formData.supplierLeadTime) || 0,
          minimumOrder: Number(formData.supplierMinimumOrder) || 0,
          rating: Number(formData.supplierRating) || 3,
        },
        specifications: {
          composition: formData.composition.trim(),
          color: formData.color.trim(),
          pattern: formData.pattern.trim(),
          texture: formData.texture.trim(),
          weight: Number(formData.weight) || undefined,
          width: Number(formData.width) || undefined,
          careInstructions: formData.careInstructions.trim(),
          origin: '',
          certifications: [],
        },
        pricing: {
          costPrice: Number(formData.costPrice),
          sellingPrice: Number(formData.sellingPrice),
          unit: formData.unit as 'meter' | 'yard' | 'piece',
          minimumOrder: Number(formData.minimumOrder) || 0,
          bulkDiscounts: [],
          seasonalPricing: [],
        },
        inventory: {
          totalQuantity: Number(formData.totalQuantity),
          availableQuantity: Number(formData.totalQuantity),
          reservedQuantity: 0,
          reorderLevel: Number(formData.reorderLevel) || 0,
          maxStockLevel: Number(formData.maxStockLevel) || Number(formData.totalQuantity),
          lastRestockDate: new Date().toISOString(),
          expiryDate: '',
        },
        qualityGrade: formData.qualityGrade,
        images: [],
        tags: formData.tags,
      };

      // TODO: Implement add warehouse item when warehouse functionality is restored
      LoggingService.info('Add warehouse item functionality not implemented', 'ADD_FABRIC');
      LoggingService.info('Fabric added successfully', 'ADD_FABRIC');
      
      Alert.alert(
        'Success',
        'Fabric added successfully',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      LoggingService.error('Failed to add fabric', 'ADD_FABRIC', error as Error);
      Alert.alert('Error', 'Failed to add fabric. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleAddTag = (): void => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()],
      }));
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string): void => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove),
    }));
  };

  const updateFormData = (field: keyof FabricFormData, value: string | QualityGrade | string[]): void => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Add New Fabric"
        subtitle="Enter fabric details"
        showSearch={false}
        showNotifications={false}
        showProfile={false}
      />

      <ScrollView 
        style={styles.content}
        contentContainerStyle={{ paddingBottom: insets.bottom + 80 }}
        showsVerticalScrollIndicator={false}
      >
        {/* Basic Information */}
        <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <Title style={[styles.sectionTitle, { color: theme.colors.on}]}>
              Basic Information
            </Title>

            <TextInput
              label="Fabric Name *"
              value={formData.name}
              onChangeText={(text) => updateFormData('name', text)}
              error={!!errors.name}
              style={styles.input}
              mode="outlined"
            />
            {errors.name && <HelperText type="error">{errors.name}</HelperText>}

            <TextInput
              label="Description"
              value={formData.description}
              onChangeText={(text) => updateFormData('description', text)}
              multiline
              numberOfLines={3}
              style={styles.input}
              mode="outlined"
            />

            <TextInput
              label="SKU *"
              value={formData.sku}
              onChangeText={(text) => updateFormData('sku', text)}
              error={!!errors.sku}
              style={styles.input}
              mode="outlined"
            />
            {errors.sku && <HelperText type="error">{errors.sku}</HelperText>}

            <TextInput
              label="Barcode"
              value={formData.barcode}
              onChangeText={(text) => updateFormData('barcode', text)}
              style={styles.input}
              mode="outlined"
            />

            <TextInput
              label="Category *"
              value={formData.category}
              onChangeText={(text) => updateFormData('category', text)}
              error={!!errors.category}
              style={styles.input}
              mode="outlined"
            />
            {errors.category && <HelperText type="error">{errors.category}</HelperText>}

            <TextInput
              label="Subcategory"
              value={formData.subcategory}
              onChangeText={(text) => updateFormData('subcategory', text)}
              style={styles.input}
              mode="outlined"
            />
          </Card.Content>
        </Card>

        {/* Specifications */}
        <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <Title style={[styles.sectionTitle, { color: theme.colors.on}]}>
              Specifications
            </Title>

            <TextInput
              label="Composition"
              value={formData.composition}
              onChangeText={(text) => updateFormData('composition', text)}
              placeholder="e.g., 100% Cotton, 60% Cotton 40% Polyester"
              style={styles.input}
              mode="outlined"
            />

            <TextInput
              label="Color *"
              value={formData.color}
              onChangeText={(text) => updateFormData('color', text)}
              error={!!errors.color}
              style={styles.input}
              mode="outlined"
            />
            {errors.color && <HelperText type="error">{errors.color}</HelperText>}

            <Text style={[styles.label, { color: theme.colors.onVariant }]}>
              Pattern
            </Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.chipScroll}>
              {commonPatterns.map((pattern) => (
                <Chip
                  key={pattern}
                  selected={formData.pattern === pattern}
                  onPress={() => updateFormData('pattern', pattern)}
                  style={styles.chip}
                >
                  {pattern}
                </Chip>
              ))}
            </ScrollView>

            <Text style={[styles.label, { color: theme.colors.onVariant }]}>
              Texture
            </Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.chipScroll}>
              {commonTextures.map((texture) => (
                <Chip
                  key={texture}
                  selected={formData.texture === texture}
                  onPress={() => updateFormData('texture', texture)}
                  style={styles.chip}
                >
                  {texture}
                </Chip>
              ))}
            </ScrollView>

            <View style={styles.row}>
              <TextInput
                label="Weight (g/m²)"
                value={formData.weight}
                onChangeText={(text) => updateFormData('weight', text)}
                keyboardType="numeric"
                style={[styles.input, styles.halfInput]}
                mode="outlined"
              />

              <TextInput
                label="Width (cm)"
                value={formData.width}
                onChangeText={(text) => updateFormData('width', text)}
                keyboardType="numeric"
                style={[styles.input, styles.halfInput]}
                mode="outlined"
              />
            </View>

            <TextInput
              label="Care Instructions"
              value={formData.careInstructions}
              onChangeText={(text) => updateFormData('careInstructions', text)}
              placeholder="e.g., Machine wash cold, Tumble dry low"
              multiline
              numberOfLines={2}
              style={styles.input}
              mode="outlined"
            />
          </Card.Content>
        </Card>

        {/* Pricing */}
        <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <Title style={[styles.sectionTitle, { color: theme.colors.on}]}>
              Pricing
            </Title>

            <View style={styles.row}>
              <TextInput
                label="Cost Price *"
                value={formData.costPrice}
                onChangeText={(text) => updateFormData('costPrice', text)}
                error={!!errors.costPrice}
                keyboardType="numeric"
                style={[styles.input, styles.halfInput]}
                mode="outlined"
              />

              <TextInput
                label="Selling Price *"
                value={formData.sellingPrice}
                onChangeText={(text) => updateFormData('sellingPrice', text)}
                error={!!errors.sellingPrice}
                keyboardType="numeric"
                style={[styles.input, styles.halfInput]}
                mode="outlined"
              />
            </View>
            {(errors.costPrice || errors.sellingPrice) && (
              <HelperText type="error">{errors.costPrice || errors.sellingPrice}</HelperText>
            )}

            <Text style={[styles.label, { color: theme.colors.onVariant }]}>
              Unit
            </Text>
            <SegmentedButtons
              value={formData.unit}
              onValueChange={(value) => updateFormData('unit', value)}
              buttons={units.map(unit => ({
                value: unit,
                label: unit.charAt(0).toUpperCase() + unit.slice(1),
              }))}
              style={styles.segmentedButtons}
            />

            <TextInput
              label="Minimum Order"
              value={formData.minimumOrder}
              onChangeText={(text) => updateFormData('minimumOrder', text)}
              keyboardType="numeric"
              style={styles.input}
              mode="outlined"
            />
          </Card.Content>
        </Card>

        {/* Inventory */}
        <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <Title style={[styles.sectionTitle, { color: theme.colors.on}]}>
              Inventory
            </Title>

            <TextInput
              label="Total Quantity *"
              value={formData.totalQuantity}
              onChangeText={(text) => updateFormData('totalQuantity', text)}
              error={!!errors.totalQuantity}
              keyboardType="numeric"
              style={styles.input}
              mode="outlined"
            />
            {errors.totalQuantity && <HelperText type="error">{errors.totalQuantity}</HelperText>}

            <View style={styles.row}>
              <TextInput
                label="Reorder Level"
                value={formData.reorderLevel}
                onChangeText={(text) => updateFormData('reorderLevel', text)}
                keyboardType="numeric"
                style={[styles.input, styles.halfInput]}
                mode="outlined"
              />

              <TextInput
                label="Max Stock Level"
                value={formData.maxStockLevel}
                onChangeText={(text) => updateFormData('maxStockLevel', text)}
                keyboardType="numeric"
                style={[styles.input, styles.halfInput]}
                mode="outlined"
              />
            </View>

            <Text style={[styles.label, { color: theme.colors.onVariant }]}>
              Quality Grade
            </Text>
            <SegmentedButtons
              value={formData.qualityGrade}
              onValueChange={(value) => updateFormData('qualityGrade', value as QualityGrade)}
              buttons={qualityGrades.map(grade => ({
                value: grade,
                label: `Grade ${grade}`,
              }))}
              style={styles.segmentedButtons}
            />
          </Card.Content>
        </Card>

        {/* Supplier Information */}
        <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <Title style={[styles.sectionTitle, { color: theme.colors.on}]}>
              Supplier Information
            </Title>

            <TextInput
              label="Supplier Name *"
              value={formData.supplierName}
              onChangeText={(text) => updateFormData('supplierName', text)}
              error={!!errors.supplierName}
              style={styles.input}
              mode="outlined"
            />
            {errors.supplierName && <HelperText type="error">{errors.supplierName}</HelperText>}

            <TextInput
              label="Contact Person"
              value={formData.supplierContact}
              onChangeText={(text) => updateFormData('supplierContact', text)}
              style={styles.input}
              mode="outlined"
            />

            <TextInput
              label="Phone *"
              value={formData.supplierPhone}
              onChangeText={(text) => updateFormData('supplierPhone', text)}
              error={!!errors.supplierPhone}
              keyboardType="phone-pad"
              style={styles.input}
              mode="outlined"
            />
            {errors.supplierPhone && <HelperText type="error">{errors.supplierPhone}</HelperText>}

            <TextInput
              label="Email"
              value={formData.supplierEmail}
              onChangeText={(text) => updateFormData('supplierEmail', text)}
              keyboardType="email-address"
              style={styles.input}
              mode="outlined"
            />

            <TextInput
              label="Address"
              value={formData.supplierAddress}
              onChangeText={(text) => updateFormData('supplierAddress', text)}
              multiline
              numberOfLines={2}
              style={styles.input}
              mode="outlined"
            />

            <TextInput
              label="Payment Terms"
              value={formData.supplierPaymentTerms}
              onChangeText={(text) => updateFormData('supplierPaymentTerms', text)}
              placeholder="e.g., Net 30"
              style={styles.input}
              mode="outlined"
            />

            <View style={styles.row}>
              <TextInput
                label="Lead Time (days)"
                value={formData.supplierLeadTime}
                onChangeText={(text) => updateFormData('supplierLeadTime', text)}
                keyboardType="numeric"
                style={[styles.input, styles.halfInput]}
                mode="outlined"
              />

              <TextInput
                label="Min Order"
                value={formData.supplierMinimumOrder}
                onChangeText={(text) => updateFormData('supplierMinimumOrder', text)}
                keyboardType="numeric"
                style={[styles.input, styles.halfInput]}
                mode="outlined"
              />
            </View>

            <Text style={[styles.label, { color: theme.colors.onVariant }]}>
              Supplier Rating
            </Text>
            <SegmentedButtons
              value={formData.supplierRating}
              onValueChange={(value) => updateFormData('supplierRating', value)}
              buttons={['1', '2', '3', '4', '5'].map(rating => ({
                value: rating,
                label: rating,
              }))}
              style={styles.segmentedButtons}
            />
          </Card.Content>
        </Card>

        {/* Tags */}
        <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <Title style={[styles.sectionTitle, { color: theme.colors.on}]}>
              Tags
            </Title>

            <View style={styles.tagInput}>
              <TextInput
                label="Add Tag"
                value={newTag}
                onChangeText={setNewTag}
                style={[styles.input, { flex: 1 }]}
                mode="outlined"
                onSubmitEditing={handleAddTag}
              />
              <Button
                mode="contained"
                onPress={handleAddTag}
                disabled={!newTag.trim()}
                style={styles.addTagButton}
              >
                Add
              </Button>
            </View>

            {formData.tags.length > 0 && (
              <View style={styles.tagsContainer}>
                {formData.tags.map((tag, index) => (
                  <Chip
                    key={index}
                    onClose={() => handleRemoveTag(tag)}
                    style={styles.tagChip}
                  >
                    {tag}
                  </Chip>
                ))}
              </View>
            )}
          </Card.Content>
        </Card>
      </ScrollView>

      <FAB
        icon="content-save"
        label="Save Fabric"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={handleSave}
        loading={loading}
        disabled={loading}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  card: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  input: {
    marginBottom: 12,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  halfInput: {
    flex: 1,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  chipScroll: {
    marginBottom: 12,
  },
  chip: {
    marginRight: 8,
  },
  segmentedButtons: {
    marginBottom: 12,
  },
  tagInput: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  addTagButton: {
    marginTop: 8,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tagChip: {
    marginBottom: 4,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});

export default AddFabricScreen; 