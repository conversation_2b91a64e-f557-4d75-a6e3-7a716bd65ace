import { useNavigation } from '@react-navigation/native';
import React, { useState, useMemo, useCallback } from 'react';
import { View, StyleSheet, FlatList, RefreshControl, ListRenderItem, ActivityIndicator } from 'react-native';
import {
  Text,
  Card,
  Searchbar,
} from 'react-native-paper';


import InfoCard from '../../components/cards/InfoCard';
import CommonHeader from '../../components/navigation/CommonHeader';
import EmptyState from '../../components/ui/EmptyState';
// import Search from '../../components/forms/Search';
import StatCardGroup from '../../components/ui/StatCardGroup';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { StatCard } from '../../types';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';
// StatCard type is defined locally in this file

interface ActivityLogItem {
  id: string;
  type: 'product' | 'order' | 'customer' | 'inventory' | 'financial' | 'system' | 'staff';
  action: string;
  title: string;
  description: string;
  timestamp: string;
  icon: string;
  color: string;
  data: any;
}

const ActivityLogScreen: React.FC = () => {
  const theme = useTheme();
  const { state } = useData();
  const navigation = useNavigation();



  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedFilter, setSelectedFilter] = useState<string>('All');
  const [refreshing, setRefreshing] = useState<boolean>(false);

  // Pull to refresh functionality
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      // Simulate data refresh - in real app, this would reload from API
      await new Promise(resolve => setTimeout(resolve, 1000));
      LoggingService.info('Activity log data refreshed', 'SCREEN');
    } catch (error) {
      LoggingService.warn('Failed to refresh activity log data', 'SCREEN', error);
    } finally {
      setRefreshing(false);
    }
  }, []);

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'Completed': return theme.colors.tertiary;
      case 'In Progress': return theme.colors.secondary;
      case 'Pending': return theme.colors.primary;
      case 'Cancelled': return theme.colors.error;
      default: return theme.colors.onVariant;
    }
  };

  // Generate activity log from current data
  const activityLog = useMemo((): ActivityLogItem[] => {
    const activities: ActivityLogItem[] = [];

    // Add product activities
    state.products.forEach(product => {
      activities.push({
        id: `product-${product.id}`,
        type: 'product',
        action: 'created',
        title: 'Product Created',
        description: `${product.name} was added to inventory`,
        timestamp: product.createdAt || new Date().toISOString(),
        icon: 'plus-circle',
        color: theme.colors.primary,
        data: product,
      });

      if (product.stock <= 5) {
        activities.push({
          id: `stock-${product.id}`,
          type: 'inventory',
          action: 'low_stock',
          title: 'Low Stock Alert',
          description: `${product.name} is running low (${product.stock} remaining)`,
          timestamp: new Date().toISOString(),
          icon: 'alert-circle',
          color: theme.colors.error,
          data: product,
        });
      }
    });

    // Add order activities
    state.orders.forEach(order => {
      activities.push({
        id: `order-${order.id}`,
        type: 'order',
        action: 'created',
        title: 'Order Created',
        description: `Order #${order.id} for ${order.customerName || 'Customer'} - $${order.total?.toFixed(2) || '0.00'}`,
        timestamp: order.createdAt || order.date || new Date().toISOString(),
        icon: 'clipboard-plus',
        color: theme.colors.secondary,
        data: order,
      });

      if (order.status) {
        activities.push({
          id: `order-status-${order.id}`,
          type: 'order',
          action: 'status_changed',
          title: 'Order Status Updated',
          description: `Order #${order.id} status changed to ${order.status}`,
          timestamp: order.updatedAt || new Date().toISOString(),
          icon: 'update',
          color: getStatusColor(order.status),
          data: order,
        });
      }
    });

    // Add customer activities
    state.customers.forEach(customer => {
      activities.push({
        id: `customer-${customer.id}`,
        type: 'customer',
        action: 'created',
        title: 'Customer Added',
        description: `${customer.name} was added to customer database`,
        timestamp: customer.createdAt || new Date().toISOString(),
        icon: 'account-plus',
        color: theme.colors.tertiary,
        data: customer,
      });
    });

    // Add financial activities
    if (state.orders && state.orders.length > 0) {
      const totalRevenue = state.orders.reduce((sum, order) => sum + (order.total || 0), 0);
      activities.push({
        id: 'financial-summary',
        type: 'financial',
        action: 'revenue_calculated',
        title: 'Revenue Summary',
        description: `Total revenue: $${totalRevenue.toFixed(2)} from ${state.orders.length} orders`,
        timestamp: new Date().toISOString(),
        icon: 'money',
        color: theme.colors.primary,
        data: { totalRevenue, orderCount: state.orders.length },
      });
    }

    // Add system activities
    activities.push({
      id: 'system-login',
      type: 'system',
      action: 'user_login',
      title: 'User Login',
      description: 'User logged into the system',
      timestamp: new Date().toISOString(),
      icon: 'shield-check',
      color: theme.colors.primary,
      data: { type: 'login' },
    });

    // Add staff activities (if staff data exists)
    if (state.staff && state.staff.length > 0) {
      state.staff.forEach(staff => {
        activities.push({
          id: `staff-${staff.id}`,
          type: 'staff',
          action: 'assigned',
          title: 'Staff Assignment',
          description: `${staff.name} was assigned to work`,
          timestamp: staff.createdAt || new Date().toISOString(),
          icon: 'account-hard-hat',
          color: theme.colors.secondary,
          data: staff,
        });
      });
    }

    // Add inventory activities
    const lowStockProducts = state.products.filter(product => product.stock <= 5);
    if (lowStockProducts.length > 0) {
      activities.push({
        id: 'inventory-alert',
        type: 'inventory',
        action: 'low_stock_alert',
        title: 'Low Stock Alert',
        description: `${lowStockProducts.length} products are running low on stock`,
        timestamp: new Date().toISOString(),
        icon: 'alert-circle',
        color: theme.colors.error,
        data: { lowStockCount: lowStockProducts.length, products: lowStockProducts },
      });
    }

    // Sort by timestamp (newest first)
    return activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }, [state.products, state.orders, state.customers, state.staff, theme]);

  const filters = ['All', 'Products', 'Orders', 'Customers', 'Inventory', 'Financial', 'System', 'Staff'];

  const filteredActivities = useMemo(() => {
    return activityLog.filter(activity => {
      const matchesSearch = activity.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           activity.description.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesFilter = selectedFilter === 'All' ||
                           (selectedFilter === 'Products' && activity.type === 'product') ||
                           (selectedFilter === 'Orders' && activity.type === 'order') ||
                           (selectedFilter === 'Customers' && activity.type === 'customer') ||
                           (selectedFilter === 'Inventory' && activity.type === 'inventory') ||
                           (selectedFilter === 'Financial' && activity.type === 'financial') ||
                           (selectedFilter === 'System' && activity.type === 'system') ||
                           (selectedFilter === 'Staff' && activity.type === 'staff');

      return matchesSearch && matchesFilter;
    });
  }, [activityLog, searchQuery, selectedFilter]);

  const formatTimestamp = (timestamp: string): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };

  const renderActivityItem: ListRenderItem<ActivityLogItem> = ({ item }) => (
    <Card style={[styles.activityCard, { backgroundColor: theme.colors.surface }]}>
      <Card.Content style={styles.activityContent}>
        <View style={styles.activityHeader}>
          <View style={[styles.iconContainer, { backgroundColor: `${item.color  }15` }]}>
            <PhosphorIcon name={item.icon as any} size={20} color={item.color} />
          </View>
          <View style={styles.activityInfo}>
            <Text variant="titleSmall" style={{ color: theme.colors.on}}>
              {item.title}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onVariant, marginTop: 2 }}>
              {item.description}
            </Text>
          </View>
          <Text variant="bodySmall" style={{ color: theme.colors.onVariant }}>
            {formatTimestamp(item.timestamp)}
          </Text>
        </View>
      </Card.Content>
    </Card>
  );

  const getActivityStats = () => {
    const stats = {
      total: activityLog.length,
      products: activityLog.filter(a => a.type === 'product').length,
      orders: activityLog.filter(a => a.type === 'order').length,
      customers: activityLog.filter(a => a.type === 'customer').length,
      inventory: activityLog.filter(a => a.type === 'inventory').length,
    };
    return stats;
  };

  const stats = getActivityStats();

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Activity Log"
        subtitle="Track all business activities"
      />

      <View style={styles.content}>
        {/* Search - Temporarily disabled to fix theme issue */}
        {/* <Search
          type="activities"
          mode="bar"
          data={activityLog}
          searchFields={["title", "description", "type"]}
          placeholder="Search activities..."
          onSearch={setSearchQuery}
          onResultSelect={(activity) => {
            LoggingService.debug('Activity selected from search', 'SCREEN', activity);
            // Could show activity details or navigate to related item
          }}
          showSuggestions={true}
          showRecentSearches={true}
          showFilters={true}
          filters={filters}
          onFilterChange={setSelectedFilter}
          style={{
            ...styles.searchbar,
            backgroundColor: theme.colors.surface
          }}
        /> */}



        {/* Stats */}
        <StatCardGroup
          cards={[
            {
              key: 'total',
              title: 'Total Activities',
              value: stats.total.toString(),
              icon: 'clipboard-text-multiple',
              iconColor: theme.colors.primary,
              elevation: 1,
            },
            {
              key: 'orders',
              title: 'Orders',
              value: stats.orders.toString(),
              icon: 'clipboard-text',
              iconColor: theme.colors.secondary,
              elevation: 1,
            },
            {
              key: 'products',
              title: 'Products',
              value: stats.products.toString(),
              icon: 'food-variant',
              iconColor: theme.colors.tertiary,
              elevation: 1,
            },
            {
              key: 'customers',
              title: 'Customers',
              value: stats.customers.toString(),
              icon: 'account-group-outline',
              iconColor: theme.colors.primary,
              elevation: 1,
            },
          ] as StatCard[]}
          columns={2}
          showTitle={false}
        />

        {/* Activity List */}
        <FlatList
          data={filteredActivities}
          renderItem={renderActivityItem}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
          ListEmptyComponent={() => (
            <EmptyState
              type="activities"
              searchQuery={searchQuery}
              description="No activities found matching your search"
              actionLabel="Clear Search"
              onActionPress={() => setSearchQuery('')}
              style={{}}
              iconColor={theme.colors.onVariant}
            />
          )}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  searchbar: {
    marginBottom: 16,
    borderRadius: 12,
  },
  filtersContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    flexWrap: 'wrap',
  },
  filter: {
    marginRight: 8,
    marginBottom: 8,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
    marginHorizontal: -4,
  },
  statCardWrapper: {
    width: '50%',
    paddingHorizontal: 4,
    marginBottom: 8,
  },
  listContainer: {
    paddingBottom: 20,
  },
  activityCard: {
    marginBottom: 8,
    borderRadius: 12,
  },
  activityContent: {
    paddingVertical: 12,
  },
  activityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityInfo: {
    flex: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
});

export default ActivityLogScreen;
