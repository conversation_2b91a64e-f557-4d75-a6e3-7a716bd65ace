import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import React, { useState} from 'react';
import { ScrollView, View, StyleSheet, Alert, TouchableOpacity, ActivityIndicator } from 'react-native';
import {
  Card,
  Text,
  Surface,
  Avatar,
  List
} from 'react-native-paper';

// Removed unused Icon import - using Button icons and ProfileSectionList

import EditProfileBottomSheet from '../../components/bottomsheets/EditProfileBottomSheet';
import SecuritySettingsBottomSheet from '../../components/bottomsheets/SecuritySettingsBottomSheet';
import ImagePicker from '../../components/forms/ImagePicker';
import CommonHeader from '../../components/navigation/CommonHeader';
import SessionInfo from '../../components/session/SessionInfo';
import Button from '../../components/ui/Button';
import PaymentMethodsModal from '../../components/ui/PaymentMethodsModal';
import Switch from '../../components/ui/Switch';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import navigationService from '../../services/NavigationService';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY, getBorderColor } from '../../theme/theme';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

interface ProfileItem {
  icon: string;
  label: string;
  onPress: () => void;
  rightComponent?: React.ReactNode;
}

const MyProfileScreen: React.FC = () => {
  const theme = useTheme();
  const { isDarkMode, toggleTheme } = theme;
  const { state, actions } = useData();
  const navigation = useNavigation();



  // Bottom sheet refs
  const editProfileBottomSheetRef = React.useRef<any>(null);
  const securityBottomSheetRef = React.useRef<any>(null);

  // Modal states
  const [paymentMethodsModalVisible, setPaymentMethodsModalVisible] = useState<boolean>(false);

  // Profile-related settings state
  const [notifications, setNotifications] = useState<boolean>(state.settings.notifications || true);
  const [autoBackup, setAutoBackup] = useState<boolean>(state.settings.autoBackup || true);

  const handleProfileSave = (profileData: any): void => {
    actions.updateSettings(profileData);
    Alert.alert('Success', 'Profile updated successfully!');
  };

  const handlePaymentMethodsSave = (paymentMethods: any): void => {
    actions.updateSettings({ paymentMethods });
    Alert.alert('Success', 'Payment methods updated successfully!');
  };

  const handleSettingChange = (key: string, value: boolean): void => {
    const newSettings = { [key]: value };
    actions.updateSettings(newSettings);
    if (key === 'notifications') setNotifications(value);
    if (key === 'autoBackup') setAutoBackup(value);
  };

  // Enhanced logout handler with comprehensive session management
  const handleLogout = async (): Promise<void> => {
    try {
      Alert.alert(
        'Confirm Logout',
        'Are you sure you want to logout? This will end your current session and clear all authentication data.',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Logout',
            style: 'destructive',
            onPress: async () => {
              try {
                LoggingService.info('User logout initiated from ProfileScreen', 'AUTH');
                
                // Import AuthService dynamically to avoid circular dependencies
                const AuthService = (await import('../../services/AuthService')).default;
                
                // Perform comprehensive logout with session cleanup
                await AuthService.logout();
                
                LoggingService.info('User logout completed successfully', 'AUTH');
                
                // Show success message
                Alert.alert(
                  'Logged Out',
                  'You have been logged out successfully. Your session has been securely terminated.',
                  [
                    { 
                      text: 'OK',
                      onPress: () => {
                        // In a real app with authentication flow, navigate to login screen
                        // For now, we'll just show a confirmation
                        LoggingService.info('Logout confirmation acknowledged', 'AUTH');
                      }
                    }
                  ]
                );
                
              } catch (error) {
                LoggingService.error('Logout failed', 'AUTH', error as Error);
                Alert.alert(
                  'Logout Error',
                  'Failed to logout completely. Some session data may still be present. Please try again or restart the app.',
                  [
                    { text: 'Retry', onPress: () => handleLogout() },
                    { text: 'Cancel', style: 'cancel' }
                  ]
                );
              }
            },
          },
        ]
      );
    } catch (error) {
      LoggingService.error('Logout dialog error', 'AUTH', error as Error);
      Alert.alert(
        'Error', 
        'Unable to process logout request. Please try again or restart the app.',
        [{ text: 'OK' }]
      );
    }
  };

  const ProfileHeader: React.FC = () => (
    <Surface style={[styles.profileCard, { backgroundColor: theme.colors.surface }]} elevation={1}>
      <View style={styles.profileContent}>
        <View style={styles.profileHeader}>
          <View style={styles.profileMain}>
            <View style={styles.avatarContainer}>
              {(state.settings as any).profileImage ? (
                <Avatar.Image
                  size={56}
                  source={{ uri: (state.settings as any).profileImage }}
                />
              ) : (
                <Avatar.Text
                  size={56}
                  label={state.settings.storeName.split(' ').map(word => word[0]).join('').substring(0, 2)}
                />
              )}
            </View>
            <View style={styles.profileInfo}>
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                <Text style={{
                  fontWeight: '700',
                  color: theme.colors.on,
                  fontSize: TYPOGRAPHY.fontSize.lg,
                  lineHeight: TYPOGRAPHY.lineHeight.tight * TYPOGRAPHY.fontSize.lg
                }}>
                  {state.settings.storeName}
                </Text>
                <View style={{
                  backgroundColor: `${theme.colors.primary  }22`,
                  borderRadius: 8,
                  paddingHorizontal: 8,
                  paddingVertical: 2,
                  marginLeft: 4,
                }}>
                  <Text style={{ color: theme.colors.primary, fontWeight: '600', fontSize: 12 }}>
                    {((state.settings as any).role || 'Owner').toUpperCase()}
                  </Text>
                </View>
              </View>
              <Text style={{
                color: theme.colors.onVariant,
                marginTop: 2,
                fontSize: TYPOGRAPHY.fontSize.sm,
                fontWeight: TYPOGRAPHY.fontWeight.normal
              }}>
                {state.settings.phone}
              </Text>
            </View>
          </View>
          <Button
            variant="outline"
            style={{...styles.editButton, minWidth: 24, paddingHorizontal: 4, height: 32}}
            onPress={() => editProfileBottomSheetRef.current?.expand()}
            icon="pencil"
            size="sm"
          >
            Edit
          </Button>
        </View>
      </View>
    </Surface>
  );

  // Define all section items for ProfileSectionList
  const preferencesItems: ProfileItem[] = [
    {
      icon: 'bell',
      label: 'Notifications',
      onPress: () => handleSettingChange('notifications', !notifications),
      rightComponent: (
        <Switch
          value={notifications}
          onValueChange={(value: boolean) => handleSettingChange('notifications', value)}
          style={{}}
        />
      ),
    },
    {
      icon: 'cloud',
      label: 'Auto Backup',
      onPress: () => handleSettingChange('autoBackup', !autoBackup),
      rightComponent: (
        <Switch
          value={autoBackup}
          onValueChange={(value: boolean) => handleSettingChange('autoBackup', value)}
          style={{}}
        />
      ),
    },
    {
      icon: 'storefront',
      label: 'Outlet Management',
      onPress: () => {
        try {
          navigationService.navigate('OutletManagement');
        } catch (error) {
          LoggingService.error('Failed to navigate to OutletManagement', 'NAVIGATION', error as Error);
        }
      },
    },
    {
      icon: 'clock',
      label: 'Activity Log',
      onPress: () => {
        try {
          navigationService.navigate('ActivityLog');
        } catch (error) {
          LoggingService.error('Failed to navigate to ActivityLog', 'NAVIGATION', error as Error);
        }
      },
    },
  ];

  const appSettingsItems: ProfileItem[] = [
    {
      icon: 'moon',
      label: 'Dark Mode',
      rightComponent: (
        <Switch
          value={isDarkMode}
          onValueChange={toggleTheme}
          style={{}}
        />
      ),
      onPress: toggleTheme,
    },
  ];

  const supportItems: ProfileItem[] = [
    {
      icon: 'question',
      label: 'Help & FAQ',
      onPress: () => {
        try {
          navigationService.navigate('HelpFAQ');
        } catch (error) {
          LoggingService.error('Failed to navigate to HelpFAQ', 'NAVIGATION', error as Error);
        }
      },
    },
    {
      icon: 'headset',
      label: 'Contact Support',
      onPress: () => {
        try {
          navigationService.navigate('ContactSupport');
        } catch (error) {
          LoggingService.error('Failed to navigate to ContactSupport', 'NAVIGATION', error as Error);
        }
      },
    },
    {
      icon: 'info',
      label: 'About',
      onPress: () => {
        try {
          navigationService.navigate('About');
        } catch (error) {
          LoggingService.error('Failed to navigate to About', 'NAVIGATION', error as Error);
        }
      },
    },
  ];

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="My Profile"
        subtitle="Personal & business settings"
        showSearch={false}
        onNotificationPress={() => {
          try {
            navigationService.navigate('NotificationsScreen');
          } catch (error) {
            LoggingService.error('Failed to navigate to NotificationsScreen', 'NAVIGATION', error as Error);
          }
        }}
        onProfilePress={() => navigation.goBack()}
      />
      

      
      <ScrollView 
        style={{ flex: 1, backgroundColor: theme.colors.background }} 
        contentContainerStyle={{ padding: 0, margin: 0 }} 
        showsVerticalScrollIndicator={false}
      >
        <ProfileHeader />
        
        {/* Preferences Section */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface, shadowColor: '#000' }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onVariant }]}>Profile Preferences</Text>
          {preferencesItems.map((item, idx) => (
            <TouchableOpacity
              key={item.label}
              style={[
                styles.sectionItem,
                { borderBottomColor: idx === preferencesItems.length - 1 ? 'transparent' : `${theme.colors.outline}22` }
              ]}
              onPress={item.onPress}
              activeOpacity={0.7}
            >
              <PhosphorIcon name={item.icon as any} size={24} color={theme.colors.onVariant} weight="regular" style={styles.sectionIcon} />
              <Text style={[styles.sectionLabel, { color: theme.colors.on}]}>{item.label}</Text>
              {item.rightComponent ? item.rightComponent : (
                <PhosphorIcon name="chevron-right" size={24} color={theme.colors.onVariant} weight="regular" style={styles.sectionChevron} />
              )}
            </TouchableOpacity>
          ))}
        </View>
        
        {/* App Settings Section */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface, shadowColor: '#000' }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onVariant }]}>App Preferences</Text>
          {appSettingsItems.map((item, idx) => (
            <TouchableOpacity
              key={item.label}
              style={[
                styles.sectionItem,
                { borderBottomColor: idx === appSettingsItems.length - 1 ? 'transparent' : `${theme.colors.outline}22` }
              ]}
              onPress={item.onPress}
              activeOpacity={0.7}
            >
              <PhosphorIcon name={item.icon as any} size={24} color={theme.colors.onVariant} weight="regular" style={styles.sectionIcon} />
              <Text style={[styles.sectionLabel, { color: theme.colors.on}]}>{item.label}</Text>
              {item.rightComponent ? item.rightComponent : (
                <PhosphorIcon name="chevron-right" size={24} color={theme.colors.onVariant} weight="regular" style={styles.sectionChevron} />
              )}
            </TouchableOpacity>
          ))}
        </View>
        
        {/* Support Section */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface, shadowColor: '#000' }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.onVariant }]}>Support & Help</Text>
          {supportItems.map((item, idx) => (
            <TouchableOpacity
              key={item.label}
              style={[
                styles.sectionItem,
                { borderBottomColor: idx === supportItems.length - 1 ? 'transparent' : `${theme.colors.outline}22` }
              ]}
              onPress={item.onPress}
              activeOpacity={0.7}
            >
              <PhosphorIcon name={item.icon as any} size={24} color={theme.colors.onVariant} weight="regular" style={styles.sectionIcon} />
              <Text style={[styles.sectionLabel, { color: theme.colors.on}]}>{item.label}</Text>
              {item.rightComponent ? item.rightComponent : (
                <PhosphorIcon name="chevron-right" size={24} color={theme.colors.onVariant} weight="regular" style={styles.sectionChevron} />
              )}
            </TouchableOpacity>
          ))}
        </View>
        
        {/* Session Information */}
        <SessionInfo showDetails={true} />
        
        {/* Logout Section */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface, shadowColor: '#000' }]}>
          <View style={styles.logoutFooter}>
            <Button
              mode="contained"
              icon="logout"
              onPress={handleLogout}
              style={{ width: '90%', borderRadius: 8 }}
              contentStyle={{ paddingVertical: 6 }}
              buttonColor={theme.colors.error}
              textColor={theme.colors.onError}
            >
              Logout
            </Button>
          </View>
        </View>
      </ScrollView>
      
      {/* Bottom Sheets and Modals */}
      {React.createElement(EditProfileBottomSheet as any, {
        ref: editProfileBottomSheetRef,
        profile: state.settings,
        onSave: handleProfileSave,
        onClose: () => {}
      })}
      

      
      {React.createElement(PaymentMethodsModal as any, {
        visible: paymentMethodsModalVisible,
        onDismiss: () => setPaymentMethodsModalVisible(false),
        onSave: handlePaymentMethodsSave,
        paymentMethods: (state.settings as any).paymentMethods
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  profileCard: {
    marginBottom: SPACING.lg,
    borderRadius: BORDER_RADIUS.xl,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  profileContent: {
    padding: SPACING.lg,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: SPACING.md,
  },
  profileMain: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    padding: SPACING.xs,
    borderRadius: BORDER_RADIUS.lg,
    marginRight: SPACING.md,
    position: 'relative',
  },
  statusIndicator: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  profileInfo: {
    flex: 1,
  },
  contactInfo: {
    gap: SPACING.xs,
  },
  contactRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: SPACING.md,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  editButton: {
    minWidth: 80,
  },
  mainSectionTitle: {
    fontWeight: '700',
    marginBottom: SPACING.md,
  },
  sectionCard: {
    marginBottom: SPACING.lg,
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING.md,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  setting: {
    width: 32,
    height: 32,
    borderRadius: BORDER_RADIUS.md,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.sm,
  },
  listItem: {
    paddingVertical: SPACING.xs,
  },
  sectionContainer: {
    marginBottom: SPACING.lg,
  },
  settingItem: {
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.xs,
  },
  footer: {
    marginTop: SPACING.lg,
    paddingVertical: SPACING.md,
  },
  // Section styles (replacing ProfileSectionList)
  section: {
    borderRadius: 0,
    padding: 8,
    marginVertical: 4,
    marginHorizontal: 0,
    elevation: 1,
  },
  sectionTitle: {
    fontSize: 13,
    fontWeight: '500',
    marginBottom: 8,
    marginLeft: 16,
    opacity: 0.7,
  },
  sectionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
  },
  sectionIcon: {
    marginRight: 16,
  },
  sectionLabel: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
  },
  sectionChevron: {
    marginLeft: 8,
  },
  logoutFooter: {
    marginTop: 8,
    alignItems: 'center',
  },
});

export default MyProfileScreen;