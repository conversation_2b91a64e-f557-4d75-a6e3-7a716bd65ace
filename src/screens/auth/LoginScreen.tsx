import { useNavigation } from '@react-navigation/native';
import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Surface,
  Card,
  Switch,
  ActivityIndicator,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';


import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY } from '../../theme/theme';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

/**
 * LoginScreen - User authentication interface
 * 
 * Features:
 * - Secure login with validation
 * - Remember me functionality
 * - Error handling and feedback
 * - Loading states
 * - Responsive design
 * - Accessibility support
 * 
 * @screen LoginScreen
 * @version 1.0.0
 */

const LoginScreen: React.FC = () => {
  const theme = useTheme();
  const { state, login, clearError } = useAuth();
  const navigation = useNavigation();

  // Form state
  const [username, setUsername] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [rememberMe, setRememberMe] = useState<boolean>(false);
  const [showPassword, setShowPassword] = useState<boolean>(false);

  // Validation state
  const [errors, setErrors] = useState<{
    username?: string;
    password?: string;
  }>({});

  // Clear errors when auth error changes
  useEffect(() => {
    if (state.error) {
      const timer = setTimeout(() => {
        clearError();
      }, 5000); // Clear error after 5 seconds

      return () => clearTimeout(timer);
    }
  }, [state.error, clearError]);

  /**
   * Validate form inputs
   */
  const validateForm = (): boolean => {
    const newErrors: typeof errors = {};

    // Username validation
    if (!username.trim()) {
      newErrors.username = 'Username is required';
    } else if (username.length < 3) {
      newErrors.username = 'Username must be at least 3 characters';
    }

    // Password validation
    if (!password) {
      newErrors.password = 'Password is required';
    } else if (password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  /**
   * Handle login submission
   */
  const handleLogin = async (): Promise<void> => {
    try {
      // Clear previous errors
      setErrors({});
      clearError();

      // Validate form
      if (!validateForm()) {
        LoggingService.warn('Login form validation failed', 'AUTH');
        return;
      }

      LoggingService.info(`Login attempt for username: ${username}`, 'AUTH');

      // Attempt login
      await login({
        username: username.trim(),
        password,
        rememberMe,
      });

      // Login successful - navigation will be handled by auth state change
      LoggingService.info('Login successful, navigating to main app', 'AUTH');
      
    } catch (error) {
      LoggingService.error('Login failed', 'AUTH', error as Error);
      
      // Show user-friendly error message
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      Alert.alert(
        'Login Failed',
        errorMessage === 'Invalid credentials' 
          ? 'Invalid username or password. Please try again.'
          : 'Unable to login at this time. Please try again later.',
        [{ text: 'OK' }]
      );
    }
  };

  /**
   * Handle demo login (for testing purposes)
   */
  const handleDemoLogin = async (): Promise<void> => {
    setUsername('demo');
    setPassword('demo123');
    setRememberMe(false);
    
    // Small delay to show the form update
    setTimeout(() => {
      handleLogin();
    }, 500);
  };

  /**
   * Clear form
   */
  const clearForm = (): void => {
    setUsername('');
    setPassword('');
    setRememberMe(false);
    setErrors({});
    clearError();
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      flexGrow: 1,
      justifyContent: 'center',
      padding: SPACING.lg,
    },
    card: {
      padding: SPACING.xl,
      borderRadius: BORDER_RADIUS.lg,
      elevation: 4,
    },
    header: {
      alignItems: 'center',
      marginBottom: SPACING.xl,
    },
    logo: {
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: theme.colors.primary,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: SPACING.md,
    },
    title: {
      fontSize: TYPOGRAPHY.fontSize.xxl,
      fontWeight: TYPOGRAPHY.fontWeight.bold,
      color: theme.colors.onSurface,
      marginBottom: SPACING.xs,
    },
    subtitle: {
      fontSize: TYPOGRAPHY.fontSize.md,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
    },
    form: {
      gap: SPACING.md,
    },
    input: {
      backgroundColor: theme.colors.surface,
    },
    passwordContainer: {
      position: 'relative',
    },
    passwordToggle: {
      position: 'absolute',
      right: 12,
      top: 16,
      zIndex: 1,
    },
    rememberMeContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      marginVertical: SPACING.sm,
    },
    rememberMeText: {
      fontSize: TYPOGRAPHY.fontSize.sm,
      color: theme.colors.onSurface,
    },
    loginButton: {
      marginTop: SPACING.md,
      paddingVertical: SPACING.xs,
    },
    errorText: {
      color: theme.colors.error,
      fontSize: TYPOGRAPHY.fontSize.sm,
      marginTop: SPACING.xs,
    },
    authError: {
      backgroundColor: theme.colors.errorContainer,
      padding: SPACING.md,
      borderRadius: BORDER_RADIUS.md,
      marginBottom: SPACING.md,
    },
    authErrorText: {
      color: theme.colors.onErrorContainer,
      fontSize: TYPOGRAPHY.fontSize.sm,
      textAlign: 'center',
    },
    demoSection: {
      marginTop: SPACING.xl,
      padding: SPACING.md,
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: BORDER_RADIUS.md,
    },
    demoTitle: {
      fontSize: TYPOGRAPHY.fontSize.md,
      fontWeight: TYPOGRAPHY.fontWeight.medium,
      color: theme.colors.onSurfaceVariant,
      marginBottom: SPACING.sm,
      textAlign: 'center',
    },
    demoText: {
      fontSize: TYPOGRAPHY.fontSize.sm,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      marginBottom: SPACING.md,
    },
    loadingContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: SPACING.sm,
    },
    loadingText: {
      color: theme.colors.onPrimary,
      fontSize: TYPOGRAPHY.fontSize.md,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
        >
          <Card style={styles.card}>
            {/* Header */}
            <View style={styles.header}>
              <Surface style={styles.logo}>
                <PhosphorIcon
                  name="scissors"
                  size={40}
                  color={theme.colors.onPrimary}
                />
              </Surface>
              <Text style={styles.title}>TailorZap</Text>
              <Text style={styles.subtitle}>
                Bakery Management System
              </Text>
            </View>

            {/* Auth Error */}
            {state.error && (
              <View style={styles.authError}>
                <Text style={styles.authErrorText}>
                  {state.error}
                </Text>
              </View>
            )}

            {/* Login Form */}
            <View style={styles.form}>
              {/* Username Input */}
              <View>
                <TextInput
                  label="Username"
                  value={username}
                  onChangeText={setUsername}
                  mode="outlined"
                  style={styles.input}
                  left={<TextInput.Icon icon={() => <PhosphorIcon name="user" size={20} color={theme.colors.onSurfaceVariant} />} />}
                  autoCapitalize="none"
                  autoCorrect={false}
                  autoComplete="username"
                  textContentType="username"
                  error={!!errors.username}
                  disabled={state.isLoading}
                />
                {errors.username && (
                  <Text style={styles.errorText}>{errors.username}</Text>
                )}
              </View>

              {/* Password Input */}
              <View style={styles.passwordContainer}>
                <TextInput
                  label="Password"
                  value={password}
                  onChangeText={setPassword}
                  mode="outlined"
                  style={styles.input}
                  left={<TextInput.Icon icon={() => <PhosphorIcon name="lock" size={20} color={theme.colors.onSurfaceVariant} />} />}
                  right={
                    <TextInput.Icon
                                              icon={() => <PhosphorIcon name={showPassword ? "eye-slash" : "eye"} size={20} color={theme.colors.onSurfaceVariant} />}
                      onPress={() => setShowPassword(!showPassword)}
                    />
                  }
                  secureTextEntry={!showPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                  autoComplete="password"
                  textContentType="password"
                  error={!!errors.password}
                  disabled={state.isLoading}
                />
                {errors.password && (
                  <Text style={styles.errorText}>{errors.password}</Text>
                )}
              </View>

              {/* Remember Me */}
              <View style={styles.rememberMeContainer}>
                <Text style={styles.rememberMeText}>Remember me</Text>
                <Switch
                  value={rememberMe}
                  onValueChange={setRememberMe}
                  disabled={state.isLoading}
                />
              </View>

              {/* Login Button */}
              <Button
                mode="contained"
                onPress={handleLogin}
                style={styles.loginButton}
                disabled={state.isLoading}
                icon={() => <PhosphorIcon name="arrow-right" size={20} color={theme.colors.onPrimary} />}
              >
                {state.isLoading ? (
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator size="small" color={theme.colors.onPrimary} />
                    <Text style={styles.loadingText}>Signing in...</Text>
                  </View>
                ) : (
                  'Sign In'
                )}
              </Button>
            </View>

            {/* Demo Section */}
            <View style={styles.demoSection}>
              <Text style={styles.demoTitle}>Demo Access</Text>
              <Text style={styles.demoText}>
                For testing purposes, you can use the demo login below
              </Text>
              <Button
                mode="outlined"
                onPress={handleDemoLogin}
                disabled={state.isLoading}
                icon={() => <PhosphorIcon name="user-circle" size={20} color={theme.colors.primary} />}
              >
                Demo Login
              </Button>
            </View>
          </Card>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default LoginScreen;