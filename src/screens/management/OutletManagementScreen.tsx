import React, { useState, useCallback, useMemo, useRef } from 'react';
import { FlatList, View, StyleSheet, RefreshControl, Alert, ScrollView } from 'react-native';
import { Chip, FAB } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';


import UnifiedWrapper from '../../components/bottomsheets/UnifiedWrapper';
import CommonHeader from '../../components/navigation/CommonHeader';
import Card from '../../components/ui/Card';
import EmptyState from '../../components/ui/EmptyState';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import navigationService from '../../services/NavigationService';
import { Outlet } from '../../types';
import { GenericScreenProps } from '../../types/navigation';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

interface OutletManagementScreenProps extends GenericScreenProps {
  route?: {
    params?: {
      selectMode?: boolean;
      onOutletSelect?: (outlet: Outlet) => void;
      returnTo?: string;
    };
  };
}

interface SelectedSort {
  key: string;
  direction: 'asc' | 'desc';
}

const OutletManagementScreen: React.FC<OutletManagementScreenProps> = ({ route }) => {
  const theme = useTheme();
  const { state, actions } = useData();
  const { outlets, activeOutlet } = state;
  const insets = useSafeAreaInsets();
  const outletFormRef = useRef<any>(null);

  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedFilter, setSelectedFilter] = useState<string>('All');
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [selectedSort] = useState<SelectedSort>({ key: 'name', direction: 'asc' });
  
  // Selection state
  const [selectedOutlets] = useState<Set<string>>(new Set());

  const filters = ['All', 'Active', 'Inactive', 'Recent'];
  const selectMode = route?.params?.selectMode || false;
  const onOutletSelect = route?.params?.onOutletSelect;
  const returnTo = route?.params?.returnTo;

  const getFilterCount = useCallback((_filter: string): number => {
    if (!outlets) return 0;
    return outlets.length;
  }, [outlets]);



  const filteredOutlets = useMemo(() => {
    let filtered = outlets || [];

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(outlet =>
        outlet.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        outlet.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (outlet.managerName || '').toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply status filter
    switch (selectedFilter) {
      case 'Active':
        filtered = filtered.filter(outlet => outlet.isActive);
        break;
      case 'Inactive':
        filtered = filtered.filter(outlet => !outlet.isActive);
        break;
      case 'Recent':
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        filtered = filtered.filter(outlet => 
          new Date(outlet.createdAt) >= thirtyDaysAgo
        );
        break;
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any = a[selectedSort.key as keyof Outlet];
      let bValue: any = b[selectedSort.key as keyof Outlet];

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (selectedSort.direction === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [outlets, searchQuery, selectedFilter, selectedSort]);

  const handleOutletSelect = (outlet: Outlet): void => {
    if (selectMode && onOutletSelect) {
      onOutletSelect(outlet);
      if (returnTo) {
        navigationService.navigate(returnTo);
      }
      return;
    }

    // Navigate to outlet details or edit
    navigationService.navigate('OutletDetails', { outletId: outlet.id });
  };

  const handleOutletLongPress = (outlet: Outlet): void => {
    if (selectMode) return;
    
    Alert.alert(
      'Outlet Options',
      'What would you like to do?',
      [
        {
          text: 'Edit',
          onPress: () => navigationService.navigate('OutletForm', { outletId: outlet.id }),
        },
        {
          text: 'Toggle Status',
          onPress: () => handleToggleOutletStatus(outlet),
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => handleDeleteOutlet(outlet),
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  const handleToggleOutletStatus = async (outlet: Outlet): Promise<void> => {
    try {
      await actions.updateOutlet(outlet.id, {
        isActive: !outlet.isActive
      });
      LoggingService.info('Outlet status toggled', 'OUTLET_MANAGEMENT', { outletId: outlet.id, newStatus: !outlet.isActive });
    } catch (error) {
      LoggingService.error('Failed to toggle outlet status', 'OUTLET_MANAGEMENT', error as Error);
      Alert.alert('Error', 'Failed to update outlet status');
    }
  };

  const handleDeleteOutlet = (outlet: Outlet): void => {
    Alert.alert(
      'Delete Outlet',
      `Are you sure you want to delete "${outlet.name}"? This action cannot be undone.`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // Note: deleteOutlet method doesn't exist in DataContext, using deactivateOutlet instead
              await actions.deactivateOutlet(outlet.id);
              LoggingService.info('Outlet deactivated', 'OUTLET_MANAGEMENT', { outletId: outlet.id });
            } catch (error) {
              LoggingService.error('Failed to deactivate outlet', 'OUTLET_MANAGEMENT', error as Error);
              Alert.alert('Error', 'Failed to deactivate outlet');
            }
          },
        },
      ]
    );
  };

  const handleRefresh = useCallback(async (): Promise<void> => {
    setRefreshing(true);
    try {
      await actions.refreshOutlets();
    } catch (error) {
      LoggingService.error('Failed to refresh outlets', 'OUTLET_MANAGEMENT', error as Error);
    } finally {
      setRefreshing(false);
    }
  }, [actions]);

  const handleAddOutlet = (): void => {
    LoggingService.debug('handleAddOutlet called', 'OUTLET_MANAGEMENT');
    LoggingService.debug('outletFormRef.current:', 'OUTLET_MANAGEMENT', outletFormRef.current);
    if (outletFormRef.current) {
      outletFormRef.current.present({});
    } else {
      LoggingService.warn('outletFormRef.current is null', 'OUTLET_MANAGEMENT');
    }
  };

  const handleOutletSuccess = (outlet: Outlet): void => {
    LoggingService.info('Outlet created/updated successfully', 'OUTLET_MANAGEMENT', { outletId: outlet.id });
    outletFormRef.current?.dismiss();
  };

  const handleOutletCancel = (): void => {
    outletFormRef.current?.dismiss();
  };

  const renderOutletCard = ({ item }: { item: Outlet }): React.ReactElement => {
    const isSelected = selectedOutlets.has(item.id);
    const isActiveOutlet = activeOutlet?.id === item.id;

    return (
      <Card
        title={item.name}
        subtitle={item.address}
        description={`Manager: ${item.managerName}`}
        price={undefined}
        status={item.isActive ? 'Active' : 'Inactive'}
        image={undefined}
        icon="store"
        iconColor={theme.colors.primary}
        iconBackgroundColor={theme.colors.primaryContainer}
        statusColor={item.isActive ? '#10b981' : theme.colors.error}
        statusBackgroundColor={item.isActive ? '#d1fae5' : theme.colors.errorContainer}
        badge={isActiveOutlet ? 'Current' : undefined}
        badgeColor={theme.colors.primary}
        onPress={() => handleOutletSelect(item)}
        onLongPress={() => handleOutletLongPress(item)}
        primaryAction={undefined}
        secondaryAction={undefined}
        menuVisible={false}
        onMenuToggle={() => {}}
        onMenuDismiss={() => {}}
        contentStyle={{}}
        style={[
          styles.outletCard,
          isSelected && styles.selectedCard,
          isActiveOutlet && styles.activeOutletCard
        ]}
      />
    );
  };

  const renderEmptyState = (): React.ReactElement => (
    <EmptyState
      type="products"
      description={searchQuery ? "No outlets match your search criteria" : "Create your first outlet to get started"}
      actionLabel="Add Outlet"
      onActionPress={handleAddOutlet}
      style={{}}
      iconColor={theme.colors.primary}
      searchQuery={searchQuery}
    />
  );

  const renderHeader = (): React.ReactElement => (
    <CommonHeader
      title="Outlet Management"
      subtitle={`${filteredOutlets.length} outlet${filteredOutlets.length !== 1 ? 's' : ''}`}
      searchPlaceholder="Search outlets..."
      searchValue={searchQuery}
      onSearchChange={setSearchQuery}
      showSearch={true}
      showNotifications={false}
      showProfile={false}
    />
  );

  const renderFilterChips = (): React.ReactElement => (
    <View style={styles.filterContainer}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.filterScroll}>
        {filters.map((filter) => (
          <Chip
            key={filter}
            selected={selectedFilter === filter}
            onPress={() => setSelectedFilter(filter)}
            style={styles.filterChip}
          >
            {filter} ({getFilterCount(filter)})
          </Chip>
        ))}
      </ScrollView>
      <PhosphorIcon 
        name="filter"
        size={20}
        color={theme.colors.onVariant}
        style={styles.filter}
      />
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {renderHeader()}
      {renderFilterChips()}
      
      <FlatList
        data={filteredOutlets}
        renderItem={renderOutletCard}
        keyExtractor={(item) => item.id}
        contentContainerStyle={[
          styles.listContainer,
          { paddingBottom: insets.bottom + 80 }
        ]}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />

      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={handleAddOutlet}
      />

      <UnifiedWrapper
        ref={outletFormRef}
        type="outlet"
        title="Outlet Management"
        mode="add"
        onSave={handleOutletSuccess}
        onClose={handleOutletCancel}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContainer: {
    padding: 16,
  },
  outletCard: {
    marginBottom: 12,
  },
  selectedCard: {
    borderColor: '#3b82f6',
    borderWidth: 2,
  },
  activeOutletCard: {
    borderColor: '#10b981',
    borderWidth: 2,
  },
  filterContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: 'transparent',
  },
  filterScroll: {
    flex: 1,
    paddingRight: 8,
  },
  filterChip: {
    marginRight: 8,
  },
  filter: {
    margin: 0,
  },
  add: {
    marginTop: 16,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});

export default OutletManagementScreen; 