import React, { useState, useCallback, useMemo } from 'react';
import { FlatList, View, StyleSheet, RefreshControl, Alert, ScrollView, TouchableOpacity } from 'react-native';
import {
  Text,
  Chip,
  FAB,
  Card,
  Title,
  Avatar,
  ProgressBar,
} from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import CommonHeader from '../../components/navigation/CommonHeader';
import EmptyState from '../../components/ui/EmptyState';
import StatCardGroup from '../../components/ui/StatCardGroup';
import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import navigationService from '../../services/NavigationService';
import { Staff, StaffRole, SkillLevel, StatCard } from '../../types';
import { StaffManagementScreenProps } from '../../types/navigation';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

interface SortOption {
  key: string;
  label: string;
}

interface SelectedSort {
  key: string;
  direction: 'asc' | 'desc';
}

const StaffManagementScreen: React.FC<StaffManagementScreenProps> = ({ route }) => {
  const theme = useTheme();
  const { state, actions } = useData();
  const { staff, activeOutlet } = state;
  const insets = useSafeAreaInsets();

  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedFilter, setSelectedFilter] = useState<string>('All');
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [selectedSort, _setSelectedSort] = useState<SelectedSort>({ key: 'name', direction: 'asc' });
  
  const selectMode = route?.params?.selectMode || false;
  const onStaffSelect = route?.params?.onStaffSelect;
  const returnTo = route?.params?.returnTo;
  const requiredRole = route?.params?.requiredRole;
  const requiredSkill = route?.params?.requiredSkill;

  const filters = ['All', 'Tailor', 'Cutter', 'Finisher', 'Quality Checker', 'Manager', 'Assistant', 'Available', 'Overloaded'];

  const _getFilterCount = useCallback((_filter: string): number => {
    if (!staff) return 0;
    return staff.length;
  }, [staff]);

  const _sortOptions: SortOption[] = [
    { key: 'name', label: 'Name' },
    { key: 'role', label: 'Role' },
    { key: 'workload.efficiency', label: 'Efficiency' },
    { key: 'performance.qualityRating', label: 'Quality Rating' },
    { key: 'workload.scheduledHours', label: 'Workload' },
    { key: 'createdAt', label: 'Date Added' },
  ];

  const filteredStaff = useMemo(() => {
    let filtered = staff || [];

    // Filter by outlet if active outlet is set
    if (activeOutlet) {
      filtered = filtered.filter(staffMember => staffMember.outletId === activeOutlet.id);
    }

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(staffMember =>
        staffMember.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (staffMember.email || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
        (staffMember.phone || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
        staffMember.employeeId.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply role filter
    switch (selectedFilter) {
      case 'Tailor':
        filtered = filtered.filter(staffMember => staffMember.role === 'tailor');
        break;
      case 'Cutter':
        filtered = filtered.filter(staffMember => staffMember.role === 'cutter');
        break;
      case 'Finisher':
        filtered = filtered.filter(staffMember => staffMember.role === 'finisher');
        break;
      case 'Quality Checker':
        filtered = filtered.filter(staffMember => staffMember.role === 'quality_checker');
        break;
      case 'Manager':
        filtered = filtered.filter(staffMember => staffMember.role === 'manager');
        break;
      case 'Assistant':
        filtered = filtered.filter(staffMember => staffMember.role === 'assistant');
        break;
      case 'Available':
        filtered = filtered.filter(staffMember => 
          (staffMember.workload?.scheduledHours || 0) < (staffMember.workload?.availableHours || 0)
        );
        break;
      case 'Overloaded':
        filtered = filtered.filter(staffMember => 
          (staffMember.workload?.scheduledHours || 0) >= (staffMember.workload?.availableHours || 0)
        );
        break;
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any = a[selectedSort.key as keyof Staff];
      let bValue: any = b[selectedSort.key as keyof Staff];

      // Handle nested properties
      if (selectedSort.key === 'workload.efficiency') {
        aValue = a.workload?.efficiency || 0;
        bValue = b.workload?.efficiency || 0;
      } else if (selectedSort.key === 'performance.qualityRating') {
        aValue = a.performance?.qualityRating || 0;
        bValue = b.performance?.qualityRating || 0;
      } else if (selectedSort.key === 'workload.scheduledHours') {
        aValue = a.workload?.scheduledHours || 0;
        bValue = b.workload?.scheduledHours || 0;
      }

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (selectedSort.direction === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [staff, activeOutlet, searchQuery, selectedFilter, selectedSort]);

  const staffStats = useMemo(() => {
    const totalStaff = staff?.length || 0;
    const activeStaff = staff?.filter(s => s.isActive).length || 0;
    const availableStaff = staff?.filter(s => 
      s.isActive && (s.workload?.scheduledHours || 0) < (s.workload?.availableHours || 0)
    ).length || 0;
    const averageEfficiency = staff?.length ? 
      staff.reduce((sum, s) => sum + (s.workload?.efficiency || 0), 0) / staff.length : 0;

    return [
      {
        key: 'total',
        title: 'Total Staff',
        value: totalStaff.toString(),
        icon: 'account-group',
        iconColor: theme.colors.primary,
        iconBackground: theme.colors.primaryContainer,
      },
      {
        key: 'active',
        title: 'Active Staff',
        value: activeStaff.toString(),
        icon: 'account-check',
        iconColor: '#10b981',
        iconBackground: '#d1fae5',
      },
      {
        key: 'available',
        title: 'Available',
        value: availableStaff.toString(),
        icon: 'account-clock',
        iconColor: '#3b82f6',
        iconBackground: '#dbeafe',
      },
      {
        key: 'efficiency',
        title: 'Avg Efficiency',
        value: `${averageEfficiency.toFixed(1)}%`,
        icon: 'trending-up',
        iconColor: '#f59e0b',
        iconBackground: '#fef3c7',
      },
    ];
  }, [staff, theme.colors]);

  const handleStaffSelect = (staffMember: Staff): void => {
    if (selectMode && onStaffSelect) {
      // Check if staff meets requirements
      if (requiredRole && staffMember.role !== requiredRole) {
        Alert.alert(
          'Role Mismatch',
          `This staff member is a ${staffMember.role}, but ${requiredRole} is required.`,
          [
            { text: 'Cancel', style: 'cancel' },
            { 
              text: 'Select Anyway', 
              onPress: () => {
                onStaffSelect(staffMember);
                if (returnTo) {
                  navigationService.navigate(returnTo);
                }
              }
            },
          ]
        );
      } else if (requiredSkill && !staffMember.skills?.some(skill => 
        skill.name.toLowerCase().includes(requiredSkill.toLowerCase())
      )) {
        Alert.alert(
          'Skill Mismatch',
          `This staff member doesn't have the required skill: ${requiredSkill}`,
          [
            { text: 'Cancel', style: 'cancel' },
            { 
              text: 'Select Anyway', 
              onPress: () => {
                onStaffSelect(staffMember);
                if (returnTo) {
                  navigationService.navigate(returnTo);
                }
              }
            },
          ]
        );
      } else {
        onStaffSelect(staffMember);
        if (returnTo) {
          navigationService.navigate(returnTo);
        }
      }
    } else {
      // Navigate to staff details
      navigationService.navigate('StaffDetails', { staffId: staffMember.id });
    }
  };

  const handleStaffLongPress = (staffMember: Staff): void => {
    Alert.alert(
      'Staff Options',
      staffMember.name,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'View Details', onPress: () => navigationService.navigate('StaffDetails', { staffId: staffMember.id }) },
        { text: 'Edit', onPress: () => navigationService.navigate('EditStaff', { staffId: staffMember.id }) },
        { text: 'Performance Report', onPress: () => handleViewPerformance(staffMember) },
        { text: 'Workload Analysis', onPress: () => handleViewWorkload(staffMember) },
        { 
          text: 'Delete', 
          style: 'destructive',
          onPress: () => handleDeleteStaff(staffMember)
        },
      ]
    );
  };

  const handleDeleteStaff = (staffMember: Staff): void => {
    Alert.alert(
      'Delete Staff Member',
      `Are you sure you want to delete "${staffMember.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await actions.deleteStaff(staffMember.id);
              LoggingService.info(`Staff member deleted: ${staffMember.id}`, 'STAFF_MANAGEMENT');
            } catch (error) {
              LoggingService.error('Failed to delete staff member', 'STAFF_MANAGEMENT', error as Error);
              Alert.alert('Error', 'Failed to delete staff member');
            }
          },
        },
      ]
    );
  };

  const handleViewPerformance = async (staffMember: Staff): Promise<void> => {
    try {
      const report = await actions.generatePerformanceReport(staffMember.id);
      navigationService.navigate('StaffPerformanceReport', { 
        staffId: staffMember.id,
        report 
      });
    } catch (error) {
      LoggingService.error('Failed to generate performance report', 'STAFF_MANAGEMENT', error as Error);
      Alert.alert('Error', 'Failed to generate performance report');
    }
  };

  const handleViewWorkload = async (staffMember: Staff): Promise<void> => {
    try {
      const analysis = await actions.analyzeWorkload(staffMember.id);
      navigationService.navigate('StaffWorkloadAnalysis', { 
        staffId: staffMember.id,
        analysis 
      });
    } catch (error) {
      LoggingService.error('Failed to analyze workload', 'STAFF_MANAGEMENT', error as Error);
      Alert.alert('Error', 'Failed to analyze workload');
    }
  };

  const handleRefresh = useCallback(async (): Promise<void> => {
    setRefreshing(true);
    try {
      await actions.reloadData();
    } catch (error) {
      LoggingService.error('Failed to refresh staff data', 'STAFF_MANAGEMENT', error as Error);
    } finally {
      setRefreshing(false);
    }
  }, [actions]);

  const handleAddStaff = (): void => {
    navigationService.navigate('AddStaff');
  };

  const getRoleIcon = (role: StaffRole): string => {
    switch (role) {
      case 'tailor': return 'scissors-cutting';
      case 'cutter': return 'content-cut';
      case 'finisher': return 'needle';
      case 'quality_checker': return 'check-decagram';
      case 'manager': return 'account-tie';
      case 'assistant': return 'account-plus';
      case 'designer': return 'palette';
      default: return 'account';
    }
  };

  const getRoleColor = (role: StaffRole): string => {
    switch (role) {
      case 'tailor': return '#10b981';
      case 'cutter': return '#3b82f6';
      case 'finisher': return '#8b5cf6';
      case 'quality_checker': return '#f59e0b';
      case 'manager': return '#ef4444';
      case 'assistant': return '#6b7280';
      case 'designer': return '#ec4899';
      default: return '#6b7280';
    }
  };

  const getAvailabilityStatus = (staffMember: Staff): { status: string; color: string; backgroundColor: string } => {
    const utilization = (staffMember.workload?.scheduledHours || 0) / (staffMember.workload?.availableHours || 1);
    
    if (utilization >= 1) {
      return {
        status: 'Overloaded',
        color: theme.colors.error,
        backgroundColor: theme.colors.errorContainer,
      };
    } else if (utilization >= 0.8) {
      return {
        status: 'Busy',
        color: '#f59e0b',
        backgroundColor: '#fef3c7',
      };
    } else {
      return {
        status: 'Available',
        color: '#10b981',
        backgroundColor: '#d1fae5',
      };
    }
  };

  const getSkillLevelColor = (level: SkillLevel): string => {
    switch (level) {
      case 'beginner': return '#6b7280';
      case 'intermediate': return '#3b82f6';
      case 'advanced': return '#f59e0b';
      case 'expert': return '#10b981';
      default: return '#6b7280';
    }
  };

  const renderStaffCard = ({ item }: { item: Staff }): React.ReactElement => {
    const availabilityStatus = getAvailabilityStatus(item);
    const roleColor = getRoleColor(item.role);
    const utilization = (item.workload?.scheduledHours || 0) / (item.workload?.availableHours || 1);

    return (
      <Card
        style={[styles.staffCard, { backgroundColor: theme.colors.surface }]}
        onPress={() => handleStaffSelect(item)}
        onLongPress={() => handleStaffLongPress(item)}
      >
        <Card.Content>
          <View style={styles.cardHeader}>
            <View style={styles.staffInfo}>
              <Avatar.Text
                size={48}
                label={item.name.split(' ').map(n => n[0]).join('').substring(0, 2)}
                style={[styles.avatar, { backgroundColor: roleColor }]}
              />
              <View style={styles.staffDetails}>
                <Title style={[styles.staffName, { color: theme.colors.on}]}>
                  {item.name}
                </Title>
                <Text style={[styles.employeeId, { color: theme.colors.onVariant }]}>
                  {item.employeeId}
                </Text>
                <View style={styles.roleContainer}>
                  <PhosphorIcon name={getRoleIcon(item.role) as any} size={16} color={roleColor} />
                  <Text style={[styles.roleText, { color: roleColor }]}>
                    {item.role.replace('_', ' ').toUpperCase()}
                  </Text>
                </View>
              </View>
            </View>
            <Chip 
              style={[styles.availabilityChip, { backgroundColor: availabilityStatus.backgroundColor }]}
              textStyle={{ color: availabilityStatus.color }}
            >
              {availabilityStatus.status}
            </Chip>
          </View>

          <View style={styles.performanceSection}>
            <View style={styles.performanceRow}>
              <Text style={[styles.performanceLabel, { color: theme.colors.onVariant }]}>
                Quality Rating
              </Text>
              <View style={styles.ratingContainer}>
                <PhosphorIcon name="star" size={16} color="#f59e0b" />
                <Text style={[styles.ratingText, { color: theme.colors.on}]}>
                  {(item.performance?.qualityRating || 0).toFixed(1)}
                </Text>
              </View>
            </View>

            <View style={styles.performanceRow}>
              <Text style={[styles.performanceLabel, { color: theme.colors.onVariant }]}>
                Efficiency
              </Text>
              <Text style={[styles.performanceValue, { color: theme.colors.on}]}>
                {item.workload?.efficiency || 0}%
              </Text>
            </View>

            <View style={styles.performanceRow}>
              <Text style={[styles.performanceLabel, { color: theme.colors.onVariant }]}>
                Workload
              </Text>
              <View style={styles.workloadContainer}>
                <ProgressBar
                  progress={utilization}
                  color={utilization > 0.8 ? theme.colors.error : theme.colors.primary}
                  style={styles.progressBar}
                />
                <Text style={[styles.workloadText, { color: theme.colors.on}]}>
                  {item.workload?.scheduledHours || 0}/{item.workload?.availableHours || 0}h
                </Text>
              </View>
            </View>
          </View>

          <View style={styles.skillsSection}>
            <Text style={[styles.skillsLabel, { color: theme.colors.onVariant }]}>
              Skills
            </Text>
            <View style={styles.skillsContainer}>
              {item.skills?.slice(0, 3).map((skill, index) => (
                <Chip
                  key={index}
                  style={[styles.skillChip, { backgroundColor: `${getSkillLevelColor(skill.level)  }20` }]}
                  textStyle={{ color: getSkillLevelColor(skill.level) }}
                >
                  {skill.name}
                </Chip>
              ))}
              {item.skills && item.skills.length > 3 && (
                <Chip style={styles.moreSkillsChip}>
                  +{item.skills.length - 3} more
                </Chip>
              )}
            </View>
          </View>

          <View style={styles.statsRow}>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: theme.colors.on}]}>
                {item.performance?.ordersCompleted || 0}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.onVariant }]}>
                Orders
              </Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: theme.colors.on}]}>
                {item.workload?.currentOrders || 0}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.onVariant }]}>
                Active
              </Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: theme.colors.on}]}>
                {item.performance?.onTimeDeliveryRate || 0}%
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.onVariant }]}>
                On Time
              </Text>
            </View>
          </View>
        </Card.Content>
      </Card>
    );
  };

  const renderEmptyState = (): React.ReactElement => (
    <EmptyState
      type="customers"
      description={searchQuery ? "No staff members match your search criteria" : "Add your first staff member to get started"}
      actionLabel="Add Staff"
      onActionPress={handleAddStaff}
      style={{}}
      iconColor={theme.colors.primary}
      searchQuery={searchQuery}
    />
  );

  const renderHeader = (): React.ReactElement => (
    <CommonHeader
      title="Staff Management"
      subtitle={`${filteredStaff.length} staff member${filteredStaff.length !== 1 ? 's' : ''}`}
      searchPlaceholder="Search staff..."
      searchValue={searchQuery}
      onSearchChange={setSearchQuery}
      showSearch={true}
      showNotifications={false}
      showProfile={false}
    />
  );

  const renderFilterChips = (): React.ReactElement => (
    <View style={styles.filterContainer}>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.filterScrollContent}
      >
        {filters.map((filter) => (
          <Chip
            key={filter}
            selected={selectedFilter === filter}
            onPress={() => setSelectedFilter(filter)}
            style={[
              styles.filterChip,
              selectedFilter === filter && { backgroundColor: theme.colors.primary }
            ]}
            textStyle={{
              color: selectedFilter === filter ? theme.colors.onPrimary : theme.colors.on}}
          >
            {filter}
          </Chip>
        ))}
      </ScrollView>
      
      <TouchableOpacity onPress={() => {/* TODO: Open filter/sort sheet */}} style={styles.filter}>
        <PhosphorIcon name="filter" size={20} />
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {renderHeader()}
      
      <StatCardGroup
        title="Staff Overview"
        cards={staffStats as StatCard[]}
        columns={2}
        showTitle={true}
        containerStyle={styles.statsContainer}
      />
      
      {renderFilterChips()}
      
      <FlatList
        data={filteredStaff}
        renderItem={renderStaffCard}
        keyExtractor={(item) => item.id}
        contentContainerStyle={[
          styles.listContainer,
          { paddingBottom: insets.bottom + 80 }
        ]}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />

      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={handleAddStaff}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  statsContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  listContainer: {
    padding: 16,
  },
  staffCard: {
    marginBottom: 12,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  staffInfo: {
    flexDirection: 'row',
    flex: 1,
    marginRight: 8,
  },
  avatar: {
    marginRight: 12,
  },
  staffDetails: {
    flex: 1,
  },
  staffName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  employeeId: {
    fontSize: 12,
    marginBottom: 4,
  },
  roleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  roleText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  availabilityChip: {
    alignSelf: 'flex-start',
  },
  performanceSection: {
    marginBottom: 12,
  },
  performanceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 6,
  },
  performanceLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  performanceValue: {
    fontSize: 12,
    fontWeight: '600',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  workloadContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginLeft: 8,
  },
  progressBar: {
    flex: 1,
    height: 6,
    borderRadius: 3,
    marginRight: 8,
  },
  workloadText: {
    fontSize: 10,
    minWidth: 40,
  },
  skillsSection: {
    marginBottom: 12,
  },
  skillsLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 6,
  },
  skillsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
  },
  skillChip: {
    marginBottom: 4,
  },
  moreSkillsChip: {
    backgroundColor: '#f3f4f6',
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 1,
    borderTopColor: '#f3f4f6',
    paddingTop: 8,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  statLabel: {
    fontSize: 10,
    marginTop: 2,
  },
  filterContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: 'transparent',
  },
  filterScrollContent: {
    paddingRight: 8,
  },
  filterChip: {
    marginRight: 8,
  },
  filter: {
    marginLeft: 8,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});

export default StaffManagementScreen; 