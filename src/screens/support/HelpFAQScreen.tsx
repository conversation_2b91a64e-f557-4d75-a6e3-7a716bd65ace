// @ts-nocheck
import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Text, Surface, List, Divider } from 'react-native-paper';


import { useTheme } from '../../context/ThemeContext';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY } from '../../theme/theme';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
  icon: string;
}

interface FAQCategory {
  id: string;
  title: string;
  icon: string;
  color: string;
}

const HelpFAQScreen: React.FC = () => {
  const theme = useTheme();
  const [expandedId, setExpandedId] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const categories: FAQCategory[] = [
    { id: 'all', title: 'All Questions', icon: 'question', color: theme.colors.primary },
    { id: 'general', title: 'General', icon: 'info', color: '#4285F4' },
    { id: 'technical', title: 'Technical', icon: 'gear', color: '#34A853' },
    { id: 'billing', title: 'Billing', icon: 'credit-card', color: '#FBBC04' },
    { id: 'features', title: 'Features', icon: 'lightbulb', color: '#EA4335' },
  ];

  const faqData: FAQItem[] = [
    {
      id: '1',
      question: 'How do I add a new product?',
      answer: 'To add a new product, go to the Products screen and tap the "+" button. Fill in the product details including name, price, category, and stock quantity. You can also add a product image for better identification.',
      category: 'general',
      icon: 'package',
    },
    {
      id: '2',
      question: 'How do I create an order?',
      answer: 'Navigate to the Orders screen and tap the "+" button. Select the customer, add products from your inventory, set quantities, and specify delivery details. The system will automatically calculate the total.',
      category: 'general',
      icon: 'receipt',
    },
    {
      id: '3',
      question: 'Can I manage multiple outlets?',
      answer: 'Yes! You can manage multiple outlets from the Settings screen. Use the Outlet Management option to add, edit, or switch between different outlets. Each outlet maintains its own inventory and orders.',
      category: 'features',
      icon: 'storefront',
    },
    {
      id: '4',
      question: 'How do I track customer information?',
      answer: 'Go to the Customers screen to view all your customers. You can add new customers, view their order history, contact information, and preferences. This helps in providing personalized service.',
      category: 'general',
      icon: 'users',
    },
    {
      id: '5',
      question: 'How do I generate reports?',
      answer: 'Access the Reports screen from the main menu. You can generate various reports including sales reports, inventory reports, customer analytics, and financial summaries. Reports can be exported or shared.',
      category: 'features',
      icon: 'chart-bar',
    },
    {
      id: '6',
      question: 'What payment methods are supported?',
      answer: 'The app supports multiple payment methods including cash, credit cards, mobile payments, and bank transfers. You can configure payment methods in the Settings under Payment Methods.',
      category: 'billing',
      icon: 'credit-card',
    },
    {
      id: '7',
      question: 'How do I backup my data?',
      answer: 'Your data is automatically backed up to the cloud. You can also manually backup data from Settings > Auto Backup. This ensures your business data is safe and can be restored if needed.',
      category: 'technical',
      icon: 'cloud',
    },
    {
      id: '8',
      question: 'Can I customize the app theme?',
      answer: 'Yes! Go to Settings and toggle the Dark Mode option to switch between light and dark themes. The app will remember your preference and apply it across all screens.',
      category: 'features',
      icon: 'moon',
    },
    {
      id: '9',
      question: 'How do I manage staff accounts?',
      answer: 'Access Staff Management from Settings to add, edit, or remove staff accounts. You can assign different roles and permissions to control what each staff member can access.',
      category: 'technical',
      icon: 'user-circle',
    },
    {
      id: '10',
      question: 'What if I forget my password?',
      answer: 'If you forget your password, use the "Forgot Password" option on the login screen. You\'ll receive a reset link via email. For additional security, contact our support team.',
      category: 'technical',
      icon: 'lock',
    },
  ];

  const filteredFAQ = selectedCategory === 'all' 
    ? faqData 
    : faqData.filter(item => item.category === selectedCategory);

  const toggleExpanded = (id: string) => {
    setExpandedId(expandedId === id ? null : id);
  };

  return (
    <ScrollView 
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      showsVerticalScrollIndicator={false}
    >
      {/* Header */}
      <Surface style={[styles.header, { backgroundColor: theme.colors.surface }]} elevation={1}>
        <View style={styles.headerContent}>
          <PhosphorIcon 
            name="question" 
            size={48} 
            color={theme.colors.primary} 
          />
          <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
            Help & FAQ
          </Text>
          <Text variant="bodyMedium" style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
            Find answers to common questions about using TailorZa
          </Text>
        </View>
      </Surface>

      {/* Category Filter */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.categoryContainer}
        contentContainerStyle={styles.categoryContent}
      >
        {categories.map((category) => (
          <Surface 
            key={category.id}
            style={[
              styles.categoryCard,
              { 
                backgroundColor: selectedCategory === category.id 
                  ? theme.colors.primaryContainer 
                  : theme.colors.surface 
              }
            ]} 
            elevation={1}
          >
            <View 
              style={styles.categoryItem}
              onTouchEnd={() => setSelectedCategory(category.id)}
            >
              <PhosphorIcon 
                name={category.icon as any} 
                size={24} 
                color={selectedCategory === category.id 
                  ? theme.colors.onPrimaryContainer 
                  : category.color
                } 
              />
              <Text 
                variant="bodySmall" 
                style={[
                  styles.categoryText,
                  { 
                    color: selectedCategory === category.id 
                      ? theme.colors.onPrimaryContainer 
                      : theme.colors.onSurface 
                  }
                ]}
              >
                {category.title}
              </Text>
            </View>
          </Surface>
        ))}
      </ScrollView>

      {/* FAQ List */}
      <View style={styles.faqContainer}>
        {filteredFAQ.map((item, _index) => (
          <Surface 
            key={item.id}
            style={[styles.faqCard, { backgroundColor: theme.colors.surface }]} 
            elevation={1}
          >
            <List.Accordion
              title={item.question}
              description={`Tap to ${expandedId === item.id ? 'collapse' : 'expand'}`}
              left={() => (
                <View style={[styles.iconContainer, { backgroundColor: `${theme.colors.primary}15` }]}>
                  <PhosphorIcon name={item.icon as any} size={20} color={theme.colors.primary} />
                </View>
              )}
              expanded={expandedId === item.id}
              onPress={() => toggleExpanded(item.id)}
              style={styles.accordion}
              titleStyle={[styles.questionText, { color: theme.colors.onSurface }]}
              descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
            >
              <Divider style={{ marginHorizontal: SPACING.lg }} />
              <View style={styles.answerContainer}>
                <Text variant="bodyMedium" style={[styles.answerText, { color: theme.colors.onSurfaceVariant }]}>
                  {item.answer}
                </Text>
              </View>
            </List.Accordion>
          </Surface>
        ))}
      </View>

      {/* Contact Support */}
      <Surface style={[styles.contactCard, { backgroundColor: theme.colors.surface }]} elevation={1}>
        <View style={styles.contactContent}>
          <PhosphorIcon name="headset" size={32} color={theme.colors.primary} />
          <View style={styles.contactInfo}>
            <Text variant="titleMedium" style={[styles.contactTitle, { color: theme.colors.onSurface }]}>
              Still need help?
            </Text>
            <Text variant="bodyMedium" style={[styles.contactSubtitle, { color: theme.colors.onSurfaceVariant }]}>
              Can't find what you're looking for? Contact our support team for personalized assistance.
            </Text>
          </View>
        </View>
      </Surface>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    margin: SPACING.lg,
    borderRadius: BORDER_RADIUS.lg,
  },
  headerContent: {
    padding: SPACING.xl,
    alignItems: 'center',
  },
  title: {
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
    fontWeight: '700',
  },
  subtitle: {
    textAlign: 'center',
    lineHeight: 20,
  },
  categoryContainer: {
    marginBottom: SPACING.lg,
  },
  categoryContent: {
    paddingHorizontal: SPACING.lg,
  },
  categoryCard: {
    borderRadius: BORDER_RADIUS.lg,
    marginRight: SPACING.md,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
  },
  categoryText: {
    marginLeft: SPACING.sm,
    fontWeight: '500',
  },
  faqContainer: {
    paddingHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  faqCard: {
    borderRadius: BORDER_RADIUS.lg,
    marginBottom: SPACING.md,
    overflow: 'hidden',
  },
  accordion: {
    backgroundColor: 'transparent',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  questionText: {
    fontWeight: '600',
    fontSize: TYPOGRAPHY.fontSize.md,
  },
  answerContainer: {
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.lg,
  },
  answerText: {
    lineHeight: 22,
  },
  contactCard: {
    margin: SPACING.lg,
    borderRadius: BORDER_RADIUS.lg,
  },
  contactContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.lg,
  },
  contactInfo: {
    flex: 1,
    marginLeft: SPACING.md,
  },
  contactTitle: {
    fontWeight: '600',
    marginBottom: SPACING.xs,
  },
  contactSubtitle: {
    lineHeight: 20,
  },
});

export default HelpFAQScreen;
