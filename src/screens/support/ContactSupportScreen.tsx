import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { Text, TextInput, Button, Surface } from 'react-native-paper';


import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY } from '../../theme/theme';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

interface ContactSupportScreenProps {
  navigation: any;
}

const ContactSupportScreen: React.FC<ContactSupportScreenProps> = ({ navigation }) => {
  const theme = useTheme();
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (!subject.trim() || !message.trim()) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      LoggingService.info('Support message submitted', 'SCREEN', { subject, messageLength: message.length });
      
      Alert.alert(
        'Success',
        'Your message has been sent. We\'ll get back to you within 24 hours.',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );
    } catch (error) {
      LoggingService.error('Failed to submit support message', 'SCREEN', error as Error);
      Alert.alert('Error', 'Failed to send message. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ScrollView 
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      contentContainerStyle={styles.contentContainer}
      showsVerticalScrollIndicator={false}
    >
      {/* Header */}
      <Surface style={[styles.header, { backgroundColor: theme.colors.surface }]} elevation={1}>
        <View style={styles.headerContent}>
          <PhosphorIcon 
            name="headset" 
            size={48} 
            color={theme.colors.primary} 
          />
          <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
            Contact Support
          </Text>
          <Text variant="bodyMedium" style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
            We're here to help! Send us a message and we'll respond within 24 hours.
          </Text>
        </View>
      </Surface>

      {/* Contact Methods */}
      <View style={styles.contactMethods}>
        <Surface style={[styles.contactCard, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <View style={styles.contactItem}>
            <PhosphorIcon name="phone" size={24} color={theme.colors.primary} />
            <View style={styles.contactInfo}>
              <Text variant="titleSmall" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                Phone Support
              </Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                +****************
              </Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                Mon-Fri, 9AM-6PM EST
              </Text>
            </View>
          </View>
        </Surface>

        <Surface style={[styles.contactCard, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <View style={styles.contactItem}>
            <PhosphorIcon name="envelope" size={24} color={theme.colors.primary} />
            <View style={styles.contactInfo}>
              <Text variant="titleSmall" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                Email Support
              </Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                <EMAIL>
              </Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                Response within 24 hours
              </Text>
            </View>
          </View>
        </Surface>

        <Surface style={[styles.contactCard, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <View style={styles.contactItem}>
            <PhosphorIcon name="chat-circle" size={24} color={theme.colors.primary} />
            <View style={styles.contactInfo}>
              <Text variant="titleSmall" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                Live Chat
              </Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                Available 24/7
              </Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                Instant response
              </Text>
            </View>
          </View>
        </Surface>
      </View>

      {/* Contact Form */}
      <Surface style={[styles.formCard, { backgroundColor: theme.colors.surface }]} elevation={1}>
        <Text variant="titleMedium" style={[styles.formTitle, { color: theme.colors.onSurface }]}>
          Send us a Message
        </Text>
        
        <TextInput
          label="Subject"
          value={subject}
          onChangeText={setSubject}
          mode="outlined"
          style={styles.input}
          theme={{
            colors: {
              primary: theme.colors.primary,
              outline: theme.colors.outline,
              onSurface: theme.colors.onSurface,
              onSurfaceVariant: theme.colors.onSurfaceVariant,
            }
          }}
        />

        <TextInput
          label="Message"
          value={message}
          onChangeText={setMessage}
          mode="outlined"
          multiline
          numberOfLines={6}
          style={styles.textArea}
          theme={{
            colors: {
              primary: theme.colors.primary,
              outline: theme.colors.outline,
              onSurface: theme.colors.onSurface,
              onSurfaceVariant: theme.colors.onSurfaceVariant,
            }
          }}
        />

        <Button
          mode="contained"
          onPress={handleSubmit}
          loading={isSubmitting}
          disabled={isSubmitting || !subject.trim() || !message.trim()}
          style={styles.submitButton}
          buttonColor={theme.colors.primary}
          textColor={theme.colors.onPrimary}
        >
          Send Message
        </Button>
      </Surface>

      {/* FAQ Link */}
      <Surface style={[styles.faqCard, { backgroundColor: theme.colors.surface }]} elevation={1}>
        <View style={styles.faqContent}>
          <PhosphorIcon name="question" size={24} color={theme.colors.primary} />
          <View style={styles.faqInfo}>
            <Text variant="titleSmall" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
              Check our FAQ first
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              You might find the answer to your question in our frequently asked questions.
            </Text>
          </View>
          <Button
            mode="text"
            onPress={() => navigation.navigate('HelpFAQ')}
            textColor={theme.colors.primary}
          >
            View FAQ
          </Button>
        </View>
      </Surface>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: SPACING.lg,
  },
  header: {
    borderRadius: BORDER_RADIUS.lg,
    marginBottom: SPACING.lg,
  },
  headerContent: {
    padding: SPACING.xl,
    alignItems: 'center',
  },
  title: {
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
    fontWeight: '700',
  },
  subtitle: {
    textAlign: 'center',
    lineHeight: 20,
  },
  contactMethods: {
    marginBottom: SPACING.lg,
  },
  contactCard: {
    borderRadius: BORDER_RADIUS.lg,
    marginBottom: SPACING.md,
  },
  contactItem: {
    flexDirection: 'row',
    padding: SPACING.lg,
    alignItems: 'center',
  },
  contactInfo: {
    marginLeft: SPACING.md,
    flex: 1,
  },
  formCard: {
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  formTitle: {
    marginBottom: SPACING.lg,
    fontWeight: '600',
  },
  input: {
    marginBottom: SPACING.md,
  },
  textArea: {
    marginBottom: SPACING.lg,
  },
  submitButton: {
    borderRadius: BORDER_RADIUS.md,
  },
  faqCard: {
    borderRadius: BORDER_RADIUS.lg,
  },
  faqContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.lg,
  },
  faqInfo: {
    flex: 1,
    marginLeft: SPACING.md,
    marginRight: SPACING.md,
  },
});

export default ContactSupportScreen;