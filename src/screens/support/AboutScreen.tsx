import React from 'react';
import { View, StyleSheet, ScrollView, Linking, Alert } from 'react-native';
import { Text, Surface, List, Button } from 'react-native-paper';


import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY } from '../../theme/theme';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

const AboutScreen: React.FC = () => {
  const theme = useTheme();

  const appInfo = {
    name: 'TailorZa',
    version: '1.0.0',
    build: '2024.1.0',
    description: 'Complete tailoring business management solution',
    company: 'TailorZa Inc.',
    website: 'https://tailorza.com',
    email: '<EMAIL>',
    phone: '+****************',
  };

  const features = [
    { icon: 'storefront', title: 'Multi-Outlet Management', description: 'Manage multiple tailoring outlets from one app' },
    { icon: 'users', title: 'Customer Management', description: 'Track customer information, measurements, and preferences' },
    { icon: 'receipt', title: 'Order Management', description: 'Create, track, and manage orders efficiently' },
    { icon: 'package', title: 'Inventory Management', description: 'Track fabric, supplies, and finished products' },
    { icon: 'chart-bar', title: 'Business Analytics', description: 'Generate reports and insights for business growth' },
    { icon: 'credit-card', title: 'Payment Processing', description: 'Accept multiple payment methods securely' },
  ];

  const teamMembers = [
    { name: 'Ahmed Hassan', role: 'CEO & Founder', avatar: '👨‍💼' },
    { name: 'Fatima Ali', role: 'CTO', avatar: '👩‍💻' },
    { name: 'Mohammed Khan', role: 'Head of Design', avatar: '👨‍🎨' },
    { name: 'Aisha Rahman', role: 'Customer Success', avatar: '👩‍💼' },
  ];

  const handleContact = (type: 'email' | 'phone' | 'website') => {
    try {
      switch (type) {
        case 'email':
          Linking.openURL(`mailto:${appInfo.email}`);
          break;
        case 'phone':
          Linking.openURL(`tel:${appInfo.phone}`);
          break;
        case 'website':
          Linking.openURL(appInfo.website);
          break;
      }
      LoggingService.info(`Contact method used: ${type}`, 'ABOUT');
    } catch (error) {
      LoggingService.error(`Failed to open ${type}`, 'ABOUT', error as Error);
      Alert.alert('Error', `Unable to open ${type}. Please try again.`);
    }
  };

  return (
    <ScrollView 
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      showsVerticalScrollIndicator={false}
    >
      {/* Header */}
      <Surface style={[styles.header, { backgroundColor: theme.colors.surface }]} elevation={1}>
        <View style={styles.headerContent}>
          <View style={[styles.logoContainer, { backgroundColor: `${theme.colors.primary}15` }]}>
            <PhosphorIcon 
              name="scissors" 
              size={48} 
              color={theme.colors.primary} 
            />
          </View>
          <Text variant="headlineSmall" style={[styles.appName, { color: theme.colors.onSurface }]}>
            {appInfo.name}
          </Text>
          <Text variant="bodyMedium" style={[styles.version, { color: theme.colors.onSurfaceVariant }]}>
            Version {appInfo.version} (Build {appInfo.build})
          </Text>
          <Text variant="bodyMedium" style={[styles.description, { color: theme.colors.onSurfaceVariant }]}>
            {appInfo.description}
          </Text>
        </View>
      </Surface>

      {/* Features */}
      <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
        <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
          Key Features
        </Text>
        <View style={styles.featuresGrid}>
          {features.map((feature, index) => (
            <View key={index} style={styles.featureItem}>
              <View style={[styles.featureIcon, { backgroundColor: `${theme.colors.primary}15` }]}>
                <PhosphorIcon name={feature.icon as any} size={24} color={theme.colors.primary} />
              </View>
              <Text variant="titleSmall" style={[styles.featureTitle, { color: theme.colors.onSurface }]}>
                {feature.title}
              </Text>
              <Text variant="bodySmall" style={[styles.featureDescription, { color: theme.colors.onSurfaceVariant }]}>
                {feature.description}
              </Text>
            </View>
          ))}
        </View>
      </Surface>

      {/* Team */}
      <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
        <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
          Our Team
        </Text>
        <View style={styles.teamGrid}>
          {teamMembers.map((member, index) => (
            <View key={index} style={styles.teamMember}>
              <Text style={styles.avatar}>{member.avatar}</Text>
              <Text variant="titleSmall" style={[styles.memberName, { color: theme.colors.onSurface }]}>
                {member.name}
              </Text>
              <Text variant="bodySmall" style={[styles.memberRole, { color: theme.colors.onSurfaceVariant }]}>
                {member.role}
              </Text>
            </View>
          ))}
        </View>
      </Surface>

      {/* Contact Information */}
      <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
        <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
          Contact Us
        </Text>
        
        <List.Item
          title="Email Support"
          description={appInfo.email}
          left={() => (
            <View style={[styles.contactIcon, { backgroundColor: `${theme.colors.primary}15` }]}>
              <PhosphorIcon name="envelope" size={20} color={theme.colors.primary} />
            </View>
          )}
          onPress={() => handleContact('email')}
          style={styles.contactItem}
        />
        
        <List.Item
          title="Phone Support"
          description={appInfo.phone}
          left={() => (
            <View style={[styles.contactIcon, { backgroundColor: `${theme.colors.primary}15` }]}>
              <PhosphorIcon name="phone" size={20} color={theme.colors.primary} />
            </View>
          )}
          onPress={() => handleContact('phone')}
          style={styles.contactItem}
        />
        
        <List.Item
          title="Website"
          description={appInfo.website}
          left={() => (
            <View style={[styles.contactIcon, { backgroundColor: `${theme.colors.primary}15` }]}>
              <PhosphorIcon name="globe" size={20} color={theme.colors.primary} />
            </View>
          )}
          onPress={() => handleContact('website')}
          style={styles.contactItem}
        />
      </Surface>

      {/* Legal */}
      <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
        <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
          Legal
        </Text>
        
        <List.Item
          title="Privacy Policy"
          description="How we protect your data"
          left={() => (
            <View style={[styles.contactIcon, { backgroundColor: `${theme.colors.primary}15` }]}>
              <PhosphorIcon name="shield" size={20} color={theme.colors.primary} />
            </View>
          )}
          onPress={() => Alert.alert('Privacy Policy', 'Privacy policy details would be shown here.')}
          style={styles.contactItem}
        />
        
        <List.Item
          title="Terms of Service"
          description="Our terms and conditions"
          left={() => (
            <View style={[styles.contactIcon, { backgroundColor: `${theme.colors.primary}15` }]}>
              <PhosphorIcon name="file" size={20} color={theme.colors.primary} />
            </View>
          )}
          onPress={() => Alert.alert('Terms of Service', 'Terms of service details would be shown here.')}
          style={styles.contactItem}
        />
      </Surface>

      {/* Footer */}
      <View style={styles.footer}>
        <Text variant="bodySmall" style={[styles.footerText, { color: theme.colors.onSurfaceVariant }]}>
          © 2024 {appInfo.company}. All rights reserved.
        </Text>
        <Text variant="bodySmall" style={[styles.footerText, { color: theme.colors.onSurfaceVariant }]}>
          Made with ❤️ for tailors worldwide
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    margin: SPACING.lg,
    borderRadius: BORDER_RADIUS.lg,
  },
  headerContent: {
    padding: SPACING.xl,
    alignItems: 'center',
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: BORDER_RADIUS.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  appName: {
    fontWeight: '700',
    marginBottom: SPACING.xs,
  },
  version: {
    marginBottom: SPACING.sm,
  },
  description: {
    textAlign: 'center',
    lineHeight: 20,
  },
  section: {
    margin: SPACING.lg,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.lg,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: SPACING.lg,
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  featureItem: {
    width: '48%',
    marginBottom: SPACING.lg,
    alignItems: 'center',
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  featureTitle: {
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: SPACING.xs,
  },
  featureDescription: {
    textAlign: 'center',
    lineHeight: 16,
  },
  teamGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  teamMember: {
    width: '48%',
    marginBottom: SPACING.lg,
    alignItems: 'center',
  },
  avatar: {
    fontSize: 32,
    marginBottom: SPACING.sm,
  },
  memberName: {
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: SPACING.xs,
  },
  memberRole: {
    textAlign: 'center',
  },
  contactItem: {
    paddingVertical: SPACING.xs,
  },
  contactIcon: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  footer: {
    padding: SPACING.lg,
    alignItems: 'center',
  },
  footerText: {
    textAlign: 'center',
    marginBottom: SPACING.xs,
  },
});

export default AboutScreen;