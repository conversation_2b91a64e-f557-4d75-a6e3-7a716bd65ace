import { createStackNavigator } from '@react-navigation/stack';
import React from 'react';

// import Demo from '../components/Demo';
import { useTheme } from '../context/ThemeContext';

// Import navigators and screens


import {
  AddFabricScreen,
  AddOrderScreen,
  AddProductScreen,
  CustomerDetailsScreen,
  CustomersScreen,
  ProductsScreen,
  ReportsScreen
} from '../screens/business';
import {
  OutletManagementScreen,
  PaymentMethodsScreen,
  StaffManagementScreen
} from '../screens/management';
import {
  ActivityLogScreen,
  NotificationsScreen,
  ProfileScreen as MyProfileScreen
} from '../screens/settings';
import {
  AboutScreen,
  ContactSupportScreen,
  HelpFAQScreen,
  SearchScreen
} from '../screens/support';
import {
  ImportDataScreen
} from '../screens/utils';
import { RootStackParamList } from '../types/navigation';


import TabNavigator from './TabNavigator';

const Stack = createStackNavigator<RootStackParamList>();

const AppNavigator: React.FC = () => {
  const theme = useTheme();

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: theme.colors.background },
        // Unified transition animations
        cardStyleInterpolator: ({ current, layouts }) => {
          return {
            cardStyle: {
              transform: [
                {
                  translateX: current.progress.interpolate({
                    inputRange: [0, 1],
                    outputRange: [layouts.screen.width, 0],
                  }),
                },
              ],
            },
          };
        },
      }}
      initialRouteName="Main"
    >
      {/* Main Tab Navigator */}
      <Stack.Screen
        name="Main"
        component={TabNavigator}
        options={{
          headerShown: false,
        }}
      />



      {/* Customers Screen */}
      <Stack.Screen
        name="Customers"
        component={CustomersScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Products Screen */}
      <Stack.Screen
        name="Products"
        component={ProductsScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Customer Details Screen */}
      <Stack.Screen
        name="CustomerDetails"
        component={CustomerDetailsScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Search Screen */}
      <Stack.Screen
        name="Search"
        component={SearchScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* My Profile Screen */}
      <Stack.Screen
        name="MyProfile"
        component={MyProfileScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Add Product Screen */}
      <Stack.Screen
        name="AddProduct"
        component={AddProductScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Add Order Screen */}
      <Stack.Screen
        name="AddOrder"
        component={AddOrderScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Payment Methods Screen */}
      <Stack.Screen
        name="PaymentMethods"
        component={PaymentMethodsScreen}
        options={{
          headerShown: false,
        }}
      />



      {/* Import Data Screen */}
      <Stack.Screen
        name="ImportData"
        component={ImportDataScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Activity Log Screen */}
      <Stack.Screen
        name="ActivityLog"
        component={ActivityLogScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Reports Screen */}
      <Stack.Screen
        name="Reports"
        component={ReportsScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Help & FAQ Screen */}
      <Stack.Screen
        name="HelpFAQ"
        component={HelpFAQScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Contact Support Screen */}
      <Stack.Screen
        name="ContactSupport"
        component={ContactSupportScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* About Screen */}
      <Stack.Screen
        name="About"
        component={AboutScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Notifications Screen */}
      <Stack.Screen
        name="Notifications"
        component={NotificationsScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* NotificationsScreen Screen */}
      <Stack.Screen
        name="NotificationsScreen"
        component={NotificationsScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Tailor Shop Screens */}
      
      {/* Outlet Management Screen */}
      <Stack.Screen
        name="OutletManagement"
        component={OutletManagementScreen}
        options={{
          headerShown: false,
        }}
      />









      {/* Add Fabric Screen */}
      <Stack.Screen
        name="AddFabric"
        component={AddFabricScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Staff Management Screen */}
      <Stack.Screen
        name="StaffManagement"
        component={StaffManagementScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Quality Control Screen - Removed */}





              {/*Demo Screen - Disabled */}
        {/* <Stack.Screen
          name="Demo"
          component={Demo}
          options={{
            headerShown: false,
          }}
        /> */}



      {/* Form Screens */}


    </Stack.Navigator>
  );
};

export default AppNavigator;