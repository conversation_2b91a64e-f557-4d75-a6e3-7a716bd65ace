import { useNavigation } from '@react-navigation/native';
import React, { useState, useCallback, useEffect } from 'react';
import { View } from 'react-native';
import { Text } from 'react-native-paper';


// Removed AddCustomerBottomSheet - now using UnifiedWrapper
import UnifiedWrapper from '../components/bottomsheets/UnifiedWrapper';
import BottomNavBar from '../components/navigation/BottomNavBar';
import { useTheme } from '../context/ThemeContext';
import { useSafeInsets } from '../hooks/useSafeInsets';
import {
  CustomersScreen,
  DashboardScreen,
  FinancialScreen,
  OrdersScreen,
  ScanScreen
} from '../screens/business';
import {
  OutletManagementScreen,
  StaffManagementScreen
} from '../screens/management';
import {
  ProfileScreen
} from '../screens/settings';
import LoggingService from '../services/LoggingService';

// Import custom bottom nav bar and quick actions

// Import navigation service
import navigationService from '../services/NavigationService';

interface ScreenProps {
    navigation: any;
    navigateToTab: (tabName: string) => void;
}

const TabNavigator: React.FC = () => {
    const theme = useTheme();
    const insets = useSafeInsets();
    const navigation = useNavigation();
    const quickActionsRef = React.useRef<any>(null);
    const addCustomerSheetRef = React.useRef<any>(null);
    const [currentTab, setCurrentTab] = useState<string>('Dashboard');

    const handlePlusPress = (): void => {
        LoggingService.info('Plus button pressed - opening quick actions', 'NAVIGATION');
        quickActionsRef.current?.open();
    };

    // Fast tab navigation function
    const navigateToTab = useCallback((tabName: string): void => {
        LoggingService.info(`Fast navigation to ${tabName}`, 'NAVIGATION');
        setCurrentTab(tabName);
    }, []);

    // Register tab navigation handler with NavigationService
    useEffect(() => {
        navigationService.setTabNavigationHandler(navigateToTab);

        // Cleanup on unmount
        return () => {
            navigationService.setTabNavigationHandler(() => {});
        };
    }, [navigateToTab]);

    const handleQuickAction = (actionId: string): void => {
        LoggingService.info('Quick action selected', 'NAVIGATION', { actionId });

        // Close the quick actions sheet first
        quickActionsRef.current?.close();

        // Add a small delay to ensure the bottom sheet closes before navigation
        setTimeout(() => {
            // Handle different quick actions
            switch (actionId) {
                case 'add-product':
                    // Navigate to Add Product screen using NavigationService
                    LoggingService.info('Navigating to AddProduct', 'NAVIGATION');
                    try {
                        navigationService.navigate('AddProduct');
                        LoggingService.info('Navigation to AddProduct successful', 'NAVIGATION');
                    } catch (error) {
                        LoggingService.error('Navigation to AddProduct failed', 'NAVIGATION', error as Error);
                    }
                    break;
                case 'add-order':
                    // Navigate to Add Order screen using NavigationService
                    LoggingService.info('Navigating to AddOrder', 'NAVIGATION');
                    try {
                        navigationService.navigate('AddOrder');
                        LoggingService.info('Navigation to AddOrder successful', 'NAVIGATION');
                    } catch (error) {
                        LoggingService.error('Navigation to AddOrder failed', 'NAVIGATION', error as Error);
                    }
                    break;
                case 'scan':
                    // Navigate to Scan tab
                    LoggingService.info('Navigating to Scan tab', 'NAVIGATION');
                    navigateToTab('Scan');
                    break;
                case 'add-customer':
                    // Open AddCustomerBottomSheet globally
                    addCustomerSheetRef.current?.expand();
                    break;
                case 'financial-analytics':
                    // Navigate to Financial tab
                    LoggingService.info('Navigating to Financial Analytics', 'NAVIGATION');
                    navigateToTab('Financial');
                    break;
                default:
                    LoggingService.warn('Unknown action', 'NAVIGATION', { actionId });
            }
        }, 300); // 300ms delay
    };

    const renderCurrentScreen = (): React.ReactElement => {
        const screenProps: ScreenProps = {
            navigation,
            navigateToTab
        };

        switch (currentTab) {
            case 'Dashboard':
                return <DashboardScreen {...screenProps} />;
            case 'Scan':
                return <ScanScreen {...screenProps} />;
            case 'Orders':
                return <OrdersScreen {...screenProps} />;
            case 'Financial':
                return <FinancialScreen {...(screenProps as any)} />;
            case 'Settings':
            case 'Profile':
                return <ProfileScreen {...(screenProps as any)} />;
            case 'Customers':
                return <CustomersScreen {...(screenProps as any)} />;
            case 'OutletManagement':
                return <OutletManagementScreen {...screenProps} />;

            case 'StaffManagement':
                return <StaffManagementScreen {...screenProps} route={{ params: {} } as any} />;
                case 'QualityControl':
      return <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={{ color: 'gray' }}>Quality Control not implemented</Text>
      </View>;
            default:
                return <DashboardScreen {...screenProps} />;
        }
    };

    return (
        <View style={{ flex: 1, backgroundColor: theme.colors.background }}>
            {/* Current Screen */}
            {renderCurrentScreen()}

            {/* Custom Bottom Navigation */}
            <BottomNavBar
                navigation={navigation}
                currentRoute={currentTab}
                onTabPress={setCurrentTab}
                onPlusPress={handlePlusPress}
                style={{}}
                backgroundColor={theme.colors.surface}
            />

            {/* Global Quick Actions Bottom Sheet */}
            <UnifiedWrapper
                ref={quickActionsRef}
                type="quick-actions"
                title="Quick Actions"
                mode="view"
                onAction={handleQuickAction}
            />

            {/* Global Add Customer Bottom Sheet */}
            <UnifiedWrapper
                ref={addCustomerSheetRef}
                type="add-customer"
                title="Add Customer"
                mode="add"
                onSave={(customerData) => {
                    console.log('Customer saved from global sheet:', customerData);
                }}
            />
        </View>
    );
};

export default TabNavigator;