import React from 'react';

// Core Entity Types
export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  stock: number;
  sku: string;
  barcode?: string;
  image?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  totalOrders: number;
  totalSpent: number;
  lastOrderDate?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Order {
  id: string;
  customerName: string;
  customer: string;
  email: string;
  phone: string;
  date: string;
  time: string;
  status: OrderStatus;
  orderType: OrderType;
  subtotal: number;
  tax: number;
  discount: number;
  total: number;
  items?: OrderItem[];
  notes?: string;
  image?: string;
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  id: string;
  orderId: string;
  productId: string;
  productName: string;
  quantity: number;
  price: number;
  total: number;
}

// Enums and Types
export type OrderStatus = 'pending' | 'confirmed' | 'in-progress' | 'completed' | 'cancelled';
export type OrderType = 'dine-in' | 'takeaway' | 'delivery' | 'custom';

// Outlet Management
export interface Outlet {
  id: string;
  name: string;
  address: string;
  phone?: string;
  email?: string;
  managerId?: string;
  managerName?: string;
  isActive: boolean;
  operatingHours: OperatingHours;
  settings: OutletSettings;
  createdAt: string;
  updatedAt: string;
}

export interface OperatingHours {
  monday: string;
  tuesday: string;
  wednesday: string;
  thursday: string;
  friday: string;
  saturday: string;
  sunday: string;
}

export interface OutletSettings {
  taxRate: number;
  currency: string;
  timezone: string;
  defaultMeasurementUnit: string;
}

// Staff Management
export interface Staff {
  id: string;
  employeeId: string;
  name: string;
  email?: string;
  phone?: string;
  role: StaffRole;
  outletId: string;
  isActive: boolean;
  profileImage?: string;
  skills?: any[];
  experience?: any;
  performance?: StaffPerformance;
  workload?: StaffWorkload;
  paymentStructure?: any;
  availability?: any;
  emergencyContact?: any;
  documents?: any;
  createdAt: string;
  updatedAt: string;
}

export type StaffRole = 'manager' | 'tailor' | 'assistant' | 'customer_service' | 'cutter' | 'finisher' | 'quality_checker' | 'designer';

// Data Context State
export interface DataState {
  // Core entities
  products: Product[];
  customers: Customer[];
  orders: Order[];
  orderItems: OrderItem[];
  
  // Outlets and staff
  outlets: Outlet[];
  staff: Staff[];
  
  // App state
  loading: boolean;
  error: string | null;
  settings: AppSettings;
  
  // Current selections
  selectedOutlet: Outlet | null;
  activeOrderId: string | null;
}

export interface AppSettings {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  currency: string;
  taxRate: number;
  notifications: boolean;
  autoBackup: boolean;
  profileImage?: string;
}

// Data Context Actions
export interface DataActions {
  // Product actions
  addProduct: (product: CreateProductData) => Promise<Product>;
  updateProduct: (id: string, updates: UpdateProductData) => Promise<Product>;
  deleteProduct: (id: string) => Promise<void>;
  
  // Customer actions
  addCustomer: (customer: CreateCustomerData) => Promise<Customer>;
  updateCustomer: (id: string, updates: UpdateCustomerData) => Promise<Customer>;
  deleteCustomer: (id: string) => Promise<void>;
  
  // Order actions
  addOrder: (order: CreateOrderData) => Promise<Order>;
  updateOrder: (id: string, updates: UpdateOrderData) => Promise<Order>;
  deleteOrder: (id: string) => Promise<void>;
  updateOrderStatus: (orderId: string, status: OrderStatus) => Promise<void>;
  
  // Outlet actions
  addOutlet: (outlet: CreateOutletData) => Promise<Outlet>;
  updateOutlet: (id: string, updates: UpdateOutletData) => Promise<Outlet>;
  deleteOutlet: (id: string) => Promise<void>;
  setActiveOutlet: (outlet: Outlet | null) => void;
  
  // Staff actions
  addStaff: (staff: CreateStaffData) => Promise<Staff>;
  updateStaff: (id: string, updates: UpdateStaffData) => Promise<Staff>;
  deleteStaff: (id: string) => Promise<void>;
  
  // Settings actions
  updateSettings: (settings: Partial<AppSettings>) => void;
  
  // Data management
  loadData: () => Promise<void>;
  refreshData: () => Promise<void>;
  clearData: () => void;
}

// Create/Update Data Types
export interface CreateProductData {
  name: string;
  description: string;
  price: number;
  category: string;
  stock: number;
  sku: string;
  barcode?: string;
  image?: string;
}

export interface UpdateProductData extends Partial<CreateProductData> {}

export interface CreateCustomerData {
  name: string;
  email: string;
  phone: string;
  address: string;
}

export interface UpdateCustomerData extends Partial<CreateCustomerData> {}

export interface CreateOrderData {
  customerName: string;
  customer: string;
  email: string;
  phone: string;
  orderType: OrderType;
  items: CreateOrderItemData[];
  notes?: string;
}

export interface CreateOrderItemData {
  productId: string;
  productName: string;
  quantity: number;
  price: number;
}

export interface UpdateOrderData extends Partial<Omit<CreateOrderData, 'items'>> {
  status?: OrderStatus;
}

export interface CreateOutletData {
  name: string;
  address: string;
  phone?: string;
  email?: string;
  managerId?: string;
  managerName?: string;
  isActive?: boolean;
  operatingHours: OperatingHours;
  settings: OutletSettings;
}

export interface UpdateOutletData extends Partial<CreateOutletData> {
  isActive?: boolean;
}

export interface CreateStaffData {
  employeeId: string;
  name: string;
  email?: string;
  phone?: string;
  role: StaffRole;
  outletId: string;
  profileImage?: string;
}

export interface UpdateStaffData extends Partial<CreateStaffData> {}

// Filter Types
export interface ProductFilters {
  category?: string;
  isActive?: boolean;
  search?: string;
  minPrice?: number;
  maxPrice?: number;
}

export interface CustomerFilters {
  isActive?: boolean;
  search?: string;
  minOrders?: number;
  minSpent?: number;
}

export interface OrderFilters {
  status?: OrderStatus;
  orderType?: OrderType;
  customerId?: string;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
}

export interface OutletFilters {
  isActive?: boolean;
  managerId?: string;
  search?: string;
}

export interface StaffFilters {
  role?: StaffRole;
  outletId?: string;
  isActive?: boolean;
  search?: string;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface ProductResponse extends ApiResponse<Product> {}
export interface ProductsResponse extends ApiResponse<Product[]> {}
export interface CustomerResponse extends ApiResponse<Customer> {}
export interface CustomersResponse extends ApiResponse<Customer[]> {}
export interface OrderResponse extends ApiResponse<Order> {}
export interface OrdersResponse extends ApiResponse<Order[]> {}
export interface OutletResponse extends ApiResponse<Outlet> {}
export interface OutletsResponse extends ApiResponse<Outlet[]> {}
export interface StaffResponse extends ApiResponse<Staff> {}
export interface StaffListResponse extends ApiResponse<Staff[]> {}

// Form Data Types
export interface ProductFormData {
  name: string;
  description: string;
  price: string;
  category: string;
  stock: string;
  sku: string;
  barcode?: string;
  image?: string;
}

export interface CustomerFormData {
  name: string;
  email: string;
  phone: string;
  address: string;
}

export interface OrderFormData {
  customerName: string;
  customer: string;
  email: string;
  phone: string;
  orderType: OrderType;
  notes?: string;
}

export interface OutletFormData {
  name: string;
  address: string;
  phone?: string;
  email?: string;
  managerId?: string;
  managerName?: string;
  operatingHours: OperatingHours;
  settings: OutletSettings;
}

export interface StaffFormData {
  employeeId: string;
  name: string;
  email?: string;
  phone?: string;
  role: StaffRole;
  outletId: string;
  profileImage?: string;
}

// Garment Order Types
export interface GarmentOrder {
  id: string;
  orderNumber?: string;
  customerId: string;
  customerName: string;
  outletId?: string;
  garmentType: string;
  specifications?: any;
  measurements: {
    chest?: number;
    waist?: number;
    hips?: number;
    shoulder?: number;
    sleeveLength?: number;
    neck?: number;
    inseam?: number;
    height?: number;
    weight?: number;
  };
  pricing?: any;
  timeline?: any;
  workflow?: any;
  fittings?: any;
  qualityChecks?: any;
  images?: any;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  tags?: string[];
  fabricId?: string;
  fabricName?: string;
  fabricQuantity?: number;
  fabricCost?: number;
  laborCost?: number;
  totalAmount?: number;
  status: GarmentOrderStatus;
  assignedTo?: string[];
  assignedToName?: string;
  deliveryDate?: string;
  notes?: string[];
  createdAt: string;
  updatedAt: string;
}

export type GarmentOrderStatus = 
  | 'draft'
  | 'confirmed'
  | 'in_progress'
  | 'stitching'
  | 'quality_check'
  | 'completed'
  | 'delivered'
  | 'cancelled';

export interface CreateGarmentOrderData {
  customerId: string;
  customerName: string;
  garmentType: string;
  measurements?: {
    chest?: number;
    waist?: number;
    hips?: number;
    shoulder?: number;
    sleeveLength?: number;
    neck?: number;
    inseam?: number;
    height?: number;
    weight?: number;
  };
  fabricId?: string;
  fabricName?: string;
  fabricQuantity?: number;
  fabricCost?: number;
  laborCost?: number;
  deliveryDate?: string;
  notes?: string;
}

export interface UpdateGarmentOrderData extends Partial<CreateGarmentOrderData> {
  status?: GarmentOrderStatus;
  assignedTo?: string;
  assignedToName?: string;
}

export interface GarmentOrderFilters {
  status?: GarmentOrderStatus;
  customerId?: string;
  assignedTo?: string;
  garmentType?: string;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
}

// Missing types that were causing errors
export interface CacheEntry<T = any> {
  key: string;
  data: T;
  timestamp: number;
  ttl: number;
}

export interface CacheConfig {
  defaultTTL: number;
  maxSize: number;
  cleanupInterval: number;
}

export interface PerformanceMetrics {
  memoryUsage: number;
  cpuUsage: number;
  responseTime: number;
  errorRate: number;
  loadTime: number;
  startupTime: number;
  fps: number;
  cacheHitRate: number;
  renderTime: number;
  navigationTime: number;
}

export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: number;
}

// Garment-related types
export type GarmentType = 
  | 'shirt'
  | 'pants'
  | 'dress'
  | 'suit'
  | 'blouse'
  | 'skirt'
  | 'jacket'
  | 'coat'
  | 'vest'
  | 'custom';

export type WorkflowStageStatus = 
  | 'pending'
  | 'in_progress'
  | 'completed'
  | 'blocked'
  | 'cancelled';

export interface Fitting {
  id: string;
  type: FittingType;
  status: FittingStatus;
  date: string;
  notes?: string;
  measurements?: Record<string, number>;
}

export type FittingType = 'initial' | 'interim' | 'final';
export type FittingStatus = 'scheduled' | 'completed' | 'cancelled' | 'rescheduled';

// Customer Measurement types
export interface CustomerMeasurement {
  id: string;
  customerId: string;
  garmentType: GarmentType;
  measurements: Record<string, number>;
  unit: MeasurementUnit;
  takenBy?: string;
  createdAt: string;
  updatedAt: string;
}

export interface MeasurementTemplate {
  id: string;
  name: string;
  garmentType: GarmentType;
  measurements: string[];
  description?: string;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

export type MeasurementUnit = 'cm' | 'inch' | 'mm';

// Product Category type
export type ProductCategory = 
  | 'fabric'
  | 'accessories'
  | 'tools'
  | 'materials'
  | 'other';

// Staff-related types
export type SkillLevel = 'beginner' | 'intermediate' | 'advanced' | 'expert';

export type PaymentStructureType = 'hourly' | 'piece_rate' | 'commission' | 'salary';

export interface StaffWorkload {
  currentOrders: number;
  maxCapacity: number;
  efficiency: number;
  availableHours: number;
  scheduledHours: number;
  overtimeHours: number;
  capacity: number;
  lastUpdated: string;
}

export interface StaffPerformance {
  qualityRating: number;
  customerSatisfactionRating: number;
  averageCompletionTime: number;
  totalOrdersCompleted: number;
  ordersCompleted: number;
  onTimeDeliveryRate: number;
  reworkRate: number;
  goals: any[];
  lastUpdated: string;
}

// Outlet Inventory types
export interface OutletInventory {
  id: string;
  outletId: string;
  productId: string;
  itemId?: string;
  quantity: number;
  minStock: number;
  maxStock: number;
  lastUpdated: string;
}

export interface InventoryTransaction {
  id: string;
  outletId: string;
  productId: string;
  itemId?: string;
  type: 'in' | 'out' | 'adjustment';
  quantity: number;
  reason: string;
  fromLocation?: string;
  toLocation?: string;
  orderId?: string;
  staffId?: string;
  notes?: string;
  performedBy?: string;
  cost?: number;
  batchNumber?: string;
  timestamp: string;
}

// Missing type definitions
export interface StatCard {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: string;
  color?: string;
  key?: string;
  onPress?: () => void;
  iconColor?: string;
  elevation?: number;
  props?: Record<string, unknown>;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

export interface StatCardGroupProps {
  title?: string;
  cards: StatCard[];
  columns?: number;
  showTitle?: boolean;
  titleStyle?: React.CSSProperties;
  containerStyle?: any;
  style?: React.CSSProperties;
  onCardPress?: (card: StatCard) => void;
}

export interface UnifiedFormSectionProps {
  title?: string;
  sectionTitle?: string;
  fields: FormField[];
  onFieldChange?: (fieldName: string, value: unknown) => void;
  onFieldError?: (fieldName: string, error: string) => void;
  errors?: Record<string, string>;
  values?: Record<string, unknown>;
  style?: React.CSSProperties;
}

export interface ImagePickerProps {
  value?: string;
  onChange: (imageUri: string) => void;
  onImageSelected?: (imageUri: string | null) => void;
  currentImage?: string | null;
  placeholder?: string;
  style?: React.CSSProperties;
  disabled?: boolean;
}

export interface FormField {
  name: string;
  key: string;
  label: string;
  type: 'text' | 'email' | 'phone' | 'number' | 'numeric' | 'textarea' | 'select' | 'date' | 'time' | 'switch' | 'image' | 'toggle';
  value?: string | number | boolean | Date;
  required?: boolean;
  placeholder?: string;
  options?: Array<{ label: string; value: string | number }>;
  validation?: (value: string | number | boolean | Date) => string | undefined;
  onChange?: (value: string | number | boolean | Date) => void;
  disabled?: boolean;
  multiline?: boolean;
  rightAffix?: string;
  left?: React.ReactNode;
  inputProps?: Record<string, unknown>;
}

// Missing type exports
export type QualityGrade = 'A' | 'B' | 'C' | 'D';

export type InventoryItemType = 'fabric' | 'accessory' | 'tool' | 'material' | 'other';

export interface AddOrderScreenProps {
  route?: {
    params?: {
      orderId?: string;
      customerId?: string;
      editMode?: boolean;
    };
  };
  navigation: any;
}



