declare global {
  namespace NodeJS {
    interface Timeout extends globalThis.Number {}
  }
  
  interface Window {
    open(url?: string, target?: string, features?: string): Window | null;
  }
  
  interface Document {
    createElement(tagName: string): HTMLElement;
    body: HTMLElement;
  }
  
  interface HTMLElement {
    appendChild<T extends Node>(child: T): T;
    removeChild<T extends Node>(child: T): T;
    click(): void;
    href: string;
    download: string;
  }
  
  interface Node {
    // Basic Node interface
  }
  
  interface BlobOptions {
    type?: string;
    lastModified?: number;
  }
  
  interface Blob {
    // Basic Blob interface
  }
  
  interface Performance {
    now(): number;
    memory?: {
      usedJSHeapSize: number;
      totalJSHeapSize: number;
      jsHeapSizeLimit?: number;
    };
  }
  
  interface Global {
    performance: Performance;
    gc?: () => void;
  }
  
  interface Storage {
    getItem(key: string): string | null;
    setItem(key: string, value: string): void;
    removeItem(key: string): void;
    clear(): void;
    key(index: number): string | null;
    readonly length: number;
  }
  
  const performance: Performance;
  const document: Document;
  const window: Window;
  const localStorage: Storage;
  const btoa: (str: string) => string;
  const Buffer: {
    from(data: string | Buffer, encoding?: string): Buffer;
    from(data: string, encoding: string): Buffer;
  };
  
  interface Buffer {
    toString(encoding?: string): string;
  }
}

export {}; 