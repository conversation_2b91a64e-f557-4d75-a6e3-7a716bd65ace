import { RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';



// Root Stack Navigator Param List
export type RootStackParamList = {
  Main: undefined;
  Products: undefined;
  Customers: undefined;
  CustomerDetails: {
    customerId: string;
  };
  Search: {
    query?: string;
    type?: string;
    data?: any[];
    searchFields?: string[];
    placeholder?: string;
    showFilters?: boolean;
    filters?: string[];
  };
  AddProduct: {
    editMode?: boolean;
    productId?: string;
  };
  AddOrder: {
    customerId?: string;
    editMode?: boolean;
    orderId?: string;
  };
  Orders: undefined;
  Financial: undefined;
  Reports: undefined;
  Profile: undefined;
  MyProfile: undefined;
  About: undefined;
  ActivityLog: undefined;
  ContactSupport: undefined;
  HelpFAQ: undefined;
  Notifications: undefined;
  NotificationsScreen: undefined;
  ImportData: undefined;
  PaymentMethods: undefined;
  Scan: undefined;
  
  // Tailor Shop Screens


  AddFabric: {
    editMode?: boolean;
    fabricId?: string;
  };
  StaffManagement: {
    selectMode?: boolean;
    onStaffSelect?: (staff: any) => void;
    returnTo?: string;
    requiredRole?: string;
    requiredSkill?: string;
  };
  OutletManagement: undefined;

  // QualityControl: undefined; // Removed
  Demo: undefined;

};

// Navigation Props
export type RootStackNavigationProp = StackNavigationProp<RootStackParamList>;

// Screen-specific navigation props
export type AddFabricScreenNavigationProp = StackNavigationProp<RootStackParamList, 'AddFabric'>;
export type StaffManagementScreenNavigationProp = StackNavigationProp<RootStackParamList, 'StaffManagement'>;
export type SearchScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Search'>;

// Screen-specific route props
export type AddFabricScreenRouteProp = RouteProp<RootStackParamList, 'AddFabric'>;
export type StaffManagementScreenRouteProp = RouteProp<RootStackParamList, 'StaffManagement'>;
export type SearchScreenRouteProp = RouteProp<RootStackParamList, 'Search'>;

// Screen Props Interfaces

export interface AddFabricScreenProps {
  navigation: AddFabricScreenNavigationProp;
  route: AddFabricScreenRouteProp;
}

export interface StaffManagementScreenProps {
  navigation: StaffManagementScreenNavigationProp;
  route: StaffManagementScreenRouteProp;
}

export interface SearchScreenProps {
  navigation: SearchScreenNavigationProp;
  route: SearchScreenRouteProp;
}

// Generic screen props for screens that don't need specific typing
export interface GenericScreenProps {
  navigation?: any;
  route?: any;
} 