interface ThemeColors {
  primary: string;
  onVariant: string;
  outline: string;
  background: string;
}

interface Theme {
  colors: ThemeColors;
  mode?: 'light' | 'dark';
}

export function getThemeWithFallback(theme: Theme | null | undefined): Theme {
  return theme && theme.colors
    ? theme
    : { 
        colors: { 
          primary: '#2563EB', 
          onVariant: '#64748B', 
          outline: '#eee', 
          background: '#fff' 
        } 
      };
}