import { Alert, Platform } from 'react-native';

import LoggingService from '../services/LoggingService';

// Try to import native modules, but handle gracefully if not available
let RNHTMLtoPDF: any = null;
let RNPrint: any = null;
let pdfAvailable = false, printAvailable = false;

try {
  RNHTMLtoPDF = require('react-native-html-to-pdf');
  pdfAvailable = true;
} catch (error: any) {
  LoggingService.warn('react-native-html-to-pdf not available in Expo Go:', 'PDF_GENERATOR', error instanceof Error ? error : undefined);
  RNHTMLtoPDF = null;
  pdfAvailable = false;
}

try {
  RNPrint = require('react-native-print');
  printAvailable = true;
} catch (error: any) {
  LoggingService.warn('react-native-print not available in Expo Go:', 'PDF_GENERATOR', error instanceof Error ? error : undefined);
  RNPrint = null;
  printAvailable = false;
}

interface Order {
  id: string | number;
  createdAt: string;
  total: number;
  items: OrderItem[];
  customer?: string;
  customerName?: string;
  status?: string;
  [key: string]: any;
}

interface OrderItem {
  name: string;
  quantity: number;
  price: number;
  [key: string]: any;
}

export class PDFInvoiceGenerator {
  static generateInvoiceHTML(order: Order): string {
    const invoiceDate = new Date().toLocaleDateString();
    const orderDate = new Date(order.createdAt).toLocaleDateString();
    const subtotal = order.total;
    const tax = subtotal * 0.08;
    const total = subtotal + tax;

    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice #${order.id}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Helvetica', 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #fff;
        }

        .invoice-header {
            background: linear-gradient(135deg, #2563EB, #1D4ED8);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }

        .business-name {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .business-info {
            font-size: 16px;
            opacity: 0.95;
            line-height: 1.4;
        }

        .invoice-title {
            background: #F8FAFC;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #2563EB;
        }

        .invoice-title h1 {
            font-size: 28px;
            color: #1E293B;
            margin-bottom: 5px;
        }

        .invoice-meta {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            gap: 20px;
        }

        .invoice-details, .customer-details {
            flex: 1;
            background: #F1F5F9;
            padding: 20px;
            border-radius: 8px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #334155;
            margin-bottom: 15px;
            border-bottom: 2px solid #E2E8F0;
            padding-bottom: 5px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .detail-label {
            font-weight: 600;
            color: #64748B;
        }

        .detail-value {
            color: #1E293B;
            font-weight: 500;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .items-table th {
            background: #334155;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .items-table td {
            padding: 15px;
            border-bottom: 1px solid #E2E8F0;
        }

        .items-table tr:last-child td {
            border-bottom: none;
        }

        .items-table tr:nth-child(even) {
            background: #F8FAFC;
        }

        .item-name {
            font-weight: 600;
            color: #1E293B;
        }

        .item-details {
            color: #64748B;
            font-size: 14px;
            margin-top: 4px;
        }

        .text-right {
            text-align: right;
        }

        .text-center {
            text-align: center;
        }

        .totals-section {
            background: #F8FAFC;
            padding: 25px;
            border-radius: 8px;
            margin-top: 30px;
            border: 1px solid #E2E8F0;
        }

        .totals-table {
            width: 100%;
            max-width: 400px;
            margin-left: auto;
        }

        .totals-table td {
            padding: 8px 0;
            border-bottom: 1px solid #E2E8F0;
        }

        .totals-table .total-row {
            font-size: 18px;
            font-weight: bold;
            color: #1E293B;
            border-top: 2px solid #2563EB;
            padding-top: 15px;
        }

        .totals-table .total-row td {
            border-bottom: none;
        }

        .status-section {
            background: #F0FDF4;
            border: 1px solid #BBF7D0;
            padding: 20px;
            border-radius: 8px;
            margin: 30px 0;
            text-align: center;
        }

        .status-completed {
            background: #F0FDF4;
            border-color: #BBF7D0;
            color: #166534;
        }

        .status-pending {
            background: #EFF6FF;
            border-color: #BFDBFE;
            color: #1D4ED8;
        }

        .status-progress {
            background: #FFF7ED;
            border-color: #FED7AA;
            color: #C2410C;
        }

        .status-cancelled {
            background: #FEF2F2;
            border-color: #FECACA;
            color: #DC2626;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #E2E8F0;
            color: #64748B;
        }

        .footer-message {
            font-size: 18px;
            font-weight: 600;
            color: #2563EB;
            margin-bottom: 10px;
        }

        @media print {
            body {
                padding: 0;
                margin: 0;
            }

            .invoice-header {
                background: #2563EB !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-header">
        <div class="business-name">Sweet Delights Bakery</div>
        <div class="business-info">
            123 Baker Street, Sweet City, SC 12345<br>
            Phone: ******-123-4567 | Email: <EMAIL>
        </div>
    </div>

    <div class="invoice-title">
        <h1>INVOICE #${order.id}</h1>
        <p>Professional Invoice for Order Services</p>
    </div>

    <div class="invoice-meta">
        <div class="invoice-details">
            <div class="section-title">Invoice Details</div>
            <div class="detail-row">
                <span class="detail-label">Invoice Date:</span>
                <span class="detail-value">${invoiceDate}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Order Date:</span>
                <span class="detail-value">${orderDate}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Order Time:</span>
                <span class="detail-value">${order.time}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Payment Terms:</span>
                <span class="detail-value">Due on Receipt</span>
            </div>
        </div>

        <div class="customer-details">
            <div class="section-title">Bill To</div>
            <div class="detail-row">
                <span class="detail-label">Customer:</span>
                <span class="detail-value">${order.customer}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Email:</span>
                <span class="detail-value">${order.email}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Phone:</span>
                <span class="detail-value">${order.phone}</span>
            </div>
        </div>
    </div>

    <table class="items-table">
        <thead>
            <tr>
                <th>Item Description</th>
                <th class="text-center">Quantity</th>
                <th class="text-right">Unit Price</th>
                <th class="text-right">Total</th>
            </tr>
        </thead>
        <tbody>
            ${order.items.map(item => `
                <tr>
                    <td>
                        <div class="item-name">${item.name}</div>
                        <div class="item-details">Fresh bakery item</div>
                    </td>
                    <td class="text-center">${item.quantity}</td>
                    <td class="text-right">$${item.price.toFixed(2)}</td>
                    <td class="text-right">$${(item.price * item.quantity).toFixed(2)}</td>
                </tr>
            `).join('')}
        </tbody>
    </table>

    <div class="totals-section">
        <table class="totals-table">
            <tr>
                <td>Subtotal:</td>
                <td class="text-right">$${subtotal.toFixed(2)}</td>
            </tr>
            <tr>
                <td>Tax (8%):</td>
                <td class="text-right">$${tax.toFixed(2)}</td>
            </tr>
            <tr class="total-row">
                <td>Total Amount:</td>
                <td class="text-right">$${total.toFixed(2)}</td>
            </tr>
        </table>
    </div>

    <div class="status-section status-${order.status?.toLowerCase().replace(' ', '') || 'pending'}">
        <strong>Order Status: ${order.status || 'Pending'}</strong>
        ${order.estimatedTime ? `<br>Estimated Completion: ${order.estimatedTime} minutes` : ''}
    </div>

    <div class="footer">
        <div class="footer-message">Thank you for your business!</div>
        <p>We appreciate your trust in Sweet Delights Bakery.<br>
        Visit us again soon for more delicious treats!</p>
    </div>
</body>
</html>`;
  }

  static async generatePDF(order: Order): Promise<any> {
    try {
      const htmlContent = this.generateInvoiceHTML(order);

      // For web platform, create a downloadable blob
      if (Platform.OS === 'web') {
        const blob = new Blob([htmlContent], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `Invoice_${order.id}_${new Date().toISOString().split('T')[0]}.html`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        return {
          filePath: `Invoice_${order.id}_${new Date().toISOString().split('T')[0]}.html`,
          base64: btoa(htmlContent)
        };
      }

      // Check if PDF generation is available
      if (!pdfAvailable || !RNHTMLtoPDF) {
        Alert.alert(
          'PDF Generation Not Available',
          'PDF generation is not available in Expo Go. Please use a development build or try the print option.',
          [
            { text: 'OK', style: 'default' },
            { text: 'Try Print', onPress: () => this.printInvoice(order) }
          ]
        );
        throw new Error('PDF generation not available in Expo Go');
      }

      const options = {
        html: htmlContent,
        fileName: `Invoice_${order.id}_${new Date().toISOString().split('T')[0]}`,
        directory: Platform.OS === 'ios' ? 'Documents' : 'Downloads',
        width: 612,
        height: 792,
        padding: 24,
        bgColor: '#FFFFFF',
      };

      const pdf = await RNHTMLtoPDF.convert(options);
      return pdf;
    } catch (error) {
      LoggingService.error('PDF generation error:', 'PDF_GENERATOR', error instanceof Error ? error : undefined);
      throw new Error('Failed to generate PDF invoice');
    }
  }

  static async printInvoice(order: Order): Promise<void> {
    try {
      const htmlContent = this.generateInvoiceHTML(order);

      // For web/Expo, we'll use the browser's print functionality
      if (Platform.OS === 'web') {
        const printWindow = window.open('', '_blank');
        if (printWindow) {
          printWindow.document.write(htmlContent);
          printWindow.document.close();
          printWindow.print();
        }
        return;
      }

      // Check if print is available
      if (!printAvailable || !RNPrint) {
        Alert.alert(
          'Print Not Available',
          'Print functionality is not available in Expo Go. The invoice HTML will be displayed instead.',
          [
            { text: 'OK', style: 'default' },
            {
              text: 'View HTML',
              onPress: () => {
                // Create a simple fallback - show the HTML content in an alert
                Alert.alert(
                  'Invoice HTML',
                  'Invoice HTML generated successfully. In a development build, this would be sent to the printer.',
                  [{ text: 'OK' }]
                );
              }
            }
          ]
        );
        return;
      }

      // For mobile, use RNPrint
      await RNPrint.print({
        html: htmlContent,
        jobName: `Invoice_${order.id}`,
      });
    } catch (error) {
      LoggingService.error('Print error:', 'PDF_GENERATOR', error instanceof Error ? error : undefined);
      throw new Error('Failed to print invoice. Please try generating PDF instead.');
    }
  }

  static async generateAndSharePDF(order: Order): Promise<any> {
    try {
      const pdf = await this.generatePDF(order);

      if (Platform.OS === 'web') {
        // For web, the PDF is already downloaded, just show success
        return pdf;
      } else if (Platform.OS === 'ios' && printAvailable && RNPrint) {
        await RNPrint.print({ filePath: pdf.filePath });
      } else {
        // For Android, we can use the Share API if available
        try {
          const { Share } = require('react-native');
          await Share.share({
            url: `file://${pdf.filePath}`,
            title: `Invoice #${order.id}`,
          });
        } catch (shareError) {
          Alert.alert(
            'Share Not Available',
            'PDF sharing is not available in Expo Go. The PDF has been generated successfully.',
            [{ text: 'OK' }]
          );
        }
      }

      return pdf;
    } catch (error) {
      LoggingService.error('PDF share error:', 'PDF_GENERATOR', error instanceof Error ? error : undefined);
      throw new Error('Failed to generate and share PDF');
    }
  }
}
