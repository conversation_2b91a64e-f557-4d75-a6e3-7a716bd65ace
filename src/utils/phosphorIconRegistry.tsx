/**
 * Phosphor Icons Registry for TailorZa
 * 
 * This file contains Phosphor Icons integration for the TailorZa app.
 * Icons are imported from phosphor-react-native for React Native compatibility.
 */

import {
  House,
  HouseSimple,
  Gear,
  GearSix,
  User,
  UserCircle,
  UserPlus,
  MagnifyingGlass,
  Plus,
  Minus,
  X,
  Check,
  Storefront,
  ShoppingCart,
  ShoppingBag,
  CreditCard,
  Money,
  Coins,
  Bank,
  Receipt,
  Calculator,
  Scissors,
  Ruler,
  TShirt,
  Dress,
  Pants,
  ChartLine,
  ChartBar,
  ChartPie,
  TrendUp,
  TrendDown,
  Database,
  Cloud,
  Phone,
  Envelope,
  ChatCircle,
  Headset,
  Microphone,
  VideoCamera,
  CheckCircle,
  Warning,
  Info,
  Question,
  Clock,
  Calendar,
  Timer,
  Bell,
  BellRinging,
  Moon,
  Sun,
  Lightbulb,
  Lock,
  Shield,
  Key,
  File,
  Folder,
  Image,
  Camera,
  Upload,
  Download,
  Users,
  Package,
  Truck,
  CaretRight,
  ArrowRight,
  ArrowLeft,
  ArrowUp,
  ArrowDown,
  Star,
  Heart,
  Eye,
  EyeSlash,
  Trash,
  PencilSimple,
  FloppyDisk,
  XCircle,
  Funnel,
  ArrowsDownUp,
  Share,
  Printer,
  ArrowUpRight,
  ArrowDownLeft,
  ArrowsClockwise,
  List,
  MapPin,
  MapTrifold,
  Globe,
  Flag,
  Bookmark,
  BookmarkSimple,
  MinusCircle,
  PlusCircle,
  Square,
  Triangle,
  Diamond,
  Hexagon,
  Octagon,
  ThumbsUp,
  ThumbsDown,
  Smiley,
  SmileySad,
  SmileyMeh,
  SmileyWink,
  SmileySticker,
  SmileyBlank,
  SmileyNervous,
  SmileyXEyes,
  SmileyAngry
} from 'phosphor-react-native';
import React from 'react';

// Type-safe icon names
export type PhosphorIconName = 
  | 'house' | 'house-simple'
  | 'gear' | 'gear-six'
  | 'user' | 'user-circle' | 'user-plus'
  | 'magnifying-glass'
  | 'plus' | 'minus' | 'x' | 'check'
  | 'storefront' | 'shopping-cart' | 'shopping-bag'
  | 'credit-card' | 'money' | 'coins' | 'bank'
  | 'receipt' | 'calculator'
  | 'scissors' | 'ruler'
  | 'tshirt' | 'dress' | 'pants'
  | 'chart-line' | 'chart-bar' | 'chart-pie'
  | 'trend-up' | 'trend-down'
  | 'database' | 'cloud'
  | 'phone' | 'envelope' | 'chat-circle'
  | 'headset' | 'microphone' | 'video-camera'
  | 'check-circle' | 'warning'
  | 'info' | 'question'
  | 'clock' | 'calendar' | 'timer'
  | 'bell' | 'bell-ringing'
  | 'moon' | 'sun' | 'lightbulb'
  | 'lock' | 'shield' | 'key'
  | 'file' | 'folder'
  | 'image' | 'camera' | 'upload' | 'download'
  | 'users'
  | 'package' | 'truck'
  | 'chevron-right' | 'arrow-right' | 'arrow-left' | 'arrow-up' | 'arrow-down'
  | 'star' | 'heart'
  | 'eye' | 'eye-slash'
  | 'trash' | 'edit' | 'save' | 'cancel'
  | 'search' | 'filter' | 'sort'
  | 'share' | 'print' | 'export' | 'import'
  | 'refresh' | 'settings' | 'home'
  | 'menu' | 'close' | 'back' | 'forward' | 'next' | 'previous'
  | 'location' | 'map' | 'globe' | 'world' | 'flag'
  | 'bookmark' | 'bookmark-simple'
  | 'notification' | 'alert' | 'alert-circle'
  | 'checkmark' | 'checkmark-circle' | 'x-circle' | 'warning-circle'
  | 'info-circle' | 'question-circle' | 'exclamation-circle'
  | 'minus-circle' | 'plus-circle'
  | 'square' | 'triangle' | 'diamond' | 'hexagon' | 'octagon'
  | 'thumbs-up' | 'thumbs-down'
  | 'smiley' | 'smiley-sad' | 'smiley-meh' | 'smiley-wink' | 'smiley-sticker'
  | 'smiley-blank' | 'smiley-nervous' | 'smiley-x-eyes' | 'smiley-angry';

// Icon mapping
const PHOSPHOR_ICONS: Record<PhosphorIconName, React.ComponentType<any>> = {
  // Navigation & UI
  'house': House,
  'house-simple': HouseSimple,
  'gear': Gear,
  'gear-six': GearSix,
  'user': User,
  'user-circle': UserCircle,
  'user-plus': UserPlus,
  'magnifying-glass': MagnifyingGlass,
  'plus': Plus,
  'minus': Minus,
  'x': X,
  'check': Check,
  
  // Business & Commerce
  'storefront': Storefront,
  'shopping-cart': ShoppingCart,
  'shopping-bag': ShoppingBag,
  'credit-card': CreditCard,
  'money': Money,
  'coins': Coins,
  'bank': Bank,
  'receipt': Receipt,
  'calculator': Calculator,
  
  // Tailoring & Fashion
  'scissors': Scissors,
  'ruler': Ruler,
  'tshirt': TShirt,
  'dress': Dress,
  'pants': Pants,
  
  // Data & Analytics
  'chart-line': ChartLine,
  'chart-bar': ChartBar,
  'chart-pie': ChartPie,
  'trend-up': TrendUp,
  'trend-down': TrendDown,
  'database': Database,
  'cloud': Cloud,
  
  // Communication
  'phone': Phone,
  'envelope': Envelope,
  'chat-circle': ChatCircle,
  'headset': Headset,
  'microphone': Microphone,
  'video-camera': VideoCamera,
  
  // Status & Actions
  'check-circle': CheckCircle,
  'warning': Warning,
  'info': Info,
  'question': Question,
  'clock': Clock,
  'calendar': Calendar,
  'timer': Timer,
  
  // Settings & Preferences
  'bell': Bell,
  'bell-ringing': BellRinging,
  'moon': Moon,
  'sun': Sun,
  'lightbulb': Lightbulb,
  'lock': Lock,
  'shield': Shield,
  'key': Key,
  
  // Files & Documents
  'file': File,
  'folder': Folder,
  'image': Image,
  'camera': Camera,
  'upload': Upload,
  'download': Download,
  
  // Social & People
  'users': Users,
  
  // Orders & Inventory
  'package': Package,
  'truck': Truck,
  
  // Navigation & Arrows
  'chevron-right': CaretRight,
  'arrow-right': ArrowRight,
  'arrow-left': ArrowLeft,
  'arrow-up': ArrowUp,
  'arrow-down': ArrowDown,
  
  // UI Elements
  'star': Star,
  'heart': Heart,
  'eye': Eye,
  'eye-slash': EyeSlash,
  'trash': Trash,
  'edit': PencilSimple,
  'save': FloppyDisk,
  'cancel': XCircle,
  'search': MagnifyingGlass,
  'filter': Funnel,
  'sort': ArrowsDownUp,
  'share': Share,
  'print': Printer,
  'export': ArrowUpRight,
  'import': ArrowDownLeft,
  'refresh': ArrowsClockwise,
  'settings': Gear,
  'home': House,
  'menu': List,
  'close': X,
  'back': ArrowLeft,
  'forward': ArrowRight,
  'next': CaretRight,
  'previous': CaretRight,
  
  // Location & Maps
  'location': MapPin,
  'map': MapTrifold,
  'globe': Globe,
  'world': Globe,
  'flag': Flag,
  
  // Bookmarks
  'bookmark': Bookmark,
  'bookmark-simple': BookmarkSimple,
  
  // Notifications & Alerts
  'notification': Bell,
  'alert': Warning,
  'alert-circle': Warning,
  
  // Status Circles
  'checkmark': Check,
  'checkmark-circle': CheckCircle,
  'x-circle': XCircle,
  'warning-circle': Warning,
  'info-circle': Info,
  'question-circle': Question,
  'exclamation-circle': Warning,
  'minus-circle': MinusCircle,
  'plus-circle': PlusCircle,
  
  // Shapes
  'square': Square,
  'triangle': Triangle,
  'diamond': Diamond,
  'hexagon': Hexagon,
  'octagon': Octagon,
  
  // Feedback
  'thumbs-up': ThumbsUp,
  'thumbs-down': ThumbsDown,
  
  // Smileys
  'smiley': Smiley,
  'smiley-sad': SmileySad,
  'smiley-meh': SmileyMeh,
  'smiley-wink': SmileyWink,
  'smiley-sticker': SmileySticker,
  'smiley-blank': SmileyBlank,
  'smiley-nervous': SmileyNervous,
  'smiley-x-eyes': SmileyXEyes,
  'smiley-angry': SmileyAngry
};

// Phosphor Icon Component
interface PhosphorIconProps {
  name: PhosphorIconName;
  size?: number;
  color?: string;
  weight?: 'thin' | 'light' | 'regular' | 'bold' | 'fill';
  style?: any;
}

export const PhosphorIcon: React.FC<PhosphorIconProps> = ({ 
  name, 
  size = 24, 
  color = '#000000',
  weight = 'regular',
  style 
}) => {
  const IconComponent = PHOSPHOR_ICONS[name];
  
  if (!IconComponent) {
    // Icon not found - return null silently in production
    if (__DEV__) {
      console.warn(`Phosphor Icon "${name}" not found`);
    }
    return null;
  }

  return (
    <IconComponent 
      size={size} 
      color={color}
      weight={weight}
      style={style} 
    />
  );
};

// Utility functions
export const hasPhosphorIcon = (name: string): name is PhosphorIconName => {
  return name in PHOSPHOR_ICONS;
};

export const getAvailablePhosphorIcons = (): PhosphorIconName[] => {
  return Object.keys(PHOSPHOR_ICONS) as PhosphorIconName[];
};

export default PhosphorIcon;
