/**
 * CommonHeader - Universal header component with search, profile, and notifications
 * Features responsive design and consistent styling across all screens
 */

import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, ViewStyle } from 'react-native';
import {
  Text,
  Surface,
  Badge,
  Avatar
} from 'react-native-paper';


import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import { useSafeInsets } from '../../hooks/useSafeInsets';
import LoggingService from '../../services/LoggingService';
import navigationService from '../../services/NavigationService';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';
import Search from '../forms/Search';



interface CommonHeaderProps {
  title?: string;
  subtitle?: string;
  searchPlaceholder?: string;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  showSearch?: boolean;
  showNotifications?: boolean;
  showProfile?: boolean;
  showBack?: boolean;
  onBackPress?: () => void;
  notificationCount?: number;
  onNotificationPress?: () => void;
  onProfilePress?: () => void;
  backgroundColor?: string;
  elevation?: 0 | 1 | 2 | 3 | 4 | 5;
  style?: ViewStyle;
  // New unified search props
  searchType?: string;
  searchData?: any[];
  searchFields?: string[];
  onSearchResult?: (result: any) => void;
}

const CommonHeader: React.FC<CommonHeaderProps> = ({
  title = "Sweet Delights",
  subtitle,
  searchPlaceholder = "Search...",
  searchValue = "",
  onSearchChange,
  showSearch = true,
  showNotifications = true,
  showProfile = true,
  showBack= false,
  onBackPress,
  notificationCount = 3,
  onNotificationPress,
  onProfilePress,
  backgroundColor,
  elevation = 4,
  style,
  // New unified search props
  searchType = "global",
  searchData = [],
  searchFields = ["name", "title", "description"],
  onSearchResult,
}) => {
  const theme = useTheme();
  const insets = useSafeInsets();
  const { state } = useData();


  const headerBackgroundColor = backgroundColor || theme.colors.surface;

  return (
    <Surface
      style={[
        styles.container,
        {
          backgroundColor: headerBackgroundColor,
          paddingTop: insets.top + 4,
          borderBottomColor: `${theme.colors.outline  }20`
        },
        style
      ]}
      elevation={elevation}
    >
      {/* Main Header Row */}
      <View style={styles.headerRow}>
        {/* Back*/}
        {showBack && (
          <TouchableOpacity onPress={onBackPress} style={styles.back}>
            <PhosphorIcon 
              name="arrow-left"
              size={24}
              color={theme.colors.on}
            />
          </TouchableOpacity>
        )}
        
        {/* Left Section - Title */}
        <View style={[styles.leftSection, showBack && styles.leftSectionWithBack]}>
          <Text
            variant="titleLarge"
            style={[styles.title, { 
              color: theme.colors.on,
              fontWeight: '700',
              letterSpacing: 0.3,
            }]}
            numberOfLines={1}
          >
            {title}
          </Text>
        </View>

        {/* Right Section - Actions */}
        <View style={styles.rightSection}>
          {showSearch && (
            <Search
              type={searchType as any}
              mode="icon"
              data={searchData}
              searchFields={searchFields}
              placeholder={searchPlaceholder}
              onSearch={onSearchChange}
              onResultSelect={onSearchResult}
              iconSize={24}
              iconColor={theme.colors.on}
              style={styles.icon}
            />
          )}

          {showNotifications && (
            <View style={styles.iconContainer}>
              <TouchableOpacity 
                onPress={onNotificationPress || (() => LoggingService.info('Notifications pressed', 'HEADER'))}
                style={[styles.icon, { backgroundColor: 'rgba(255, 255, 255, 0.2)' }]}
              >
                <PhosphorIcon 
                  name="bell"
                  size={24}
                  color={theme.colors.on}
                />
              </TouchableOpacity>
              {notificationCount > 0 && (
                <Badge
                  size={18}
                  style={[
                    styles.badge,
                    { backgroundColor: theme.colors.error }
                  ]}
                >
                  {notificationCount > 99 ? '99+' : notificationCount}
                </Badge>
              )}
            </View>
          )}

          {showProfile && (
            <TouchableOpacity
              style={styles.profileContainer}
              onPress={onProfilePress || (() => {
                LoggingService.info('Profile pressed - navigating to MyProfile', 'HEADER');
                try {
                  navigationService.navigate('MyProfile');
                } catch (error) {
                  LoggingService.error('Failed to navigate to MyProfile', 'HEADER', error as Error);
                }
              })}
            >
              {(state.settings as any)?.profileImage ? (
                <Avatar.Image
                  size={32}
                  source={{ uri: (state.settings as any).profileImage }}
                  style={styles.profileAvatar}
                />
              ) : (
                <Avatar.Text
                  size={32}
                  label={state.settings?.storeName?.split(' ').map(word => word[0]).join('').substring(0, 2) || 'SD'}
                  style={[styles.profileAvatar, { backgroundColor: theme.colors.primary }]}
                />
              )}
            </TouchableOpacity>
          )}
        </View>
      </View>


    </Surface>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingBottom: 8,
    borderBottomWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 0.5 },
    shadowOpacity: 0.03,
    shadowRadius: 1,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    marginHorizontal: 8,
    marginBottom: 6,
  },
  leftSection: {
    flex: 1,
    marginRight: 12,
  },
  leftSectionWithBack: {
    marginLeft: 8,
  },
  back: {
    margin: 0,
  },
  title: {
    fontWeight: '700',
    letterSpacing: 0.3,
    fontSize: 18,
  },
  subtitle: {
    marginTop: 1,
    opacity: 0.8,
    fontSize: 12,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    position: 'relative',
  },
  icon: {
    margin: 0,
    marginHorizontal: 2,
    width: 36,
    height: 36,
    borderRadius: 18,
  },
  badge: {
    position: 'absolute',
    top: 4,
    right: 4,
    minWidth: 14,
    height: 14,
    borderRadius: 7,
    fontSize: 8,
    fontWeight: '600',
  },
  profileContainer: {
    position: 'relative',
    marginHorizontal: 2,
  },
  profileAvatar: {
    borderWidth: 1.5,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  profile: {
    position: 'absolute',
    top: 0,
    left: 0,
    margin: 0,
  },
  searchRow: {
    paddingHorizontal: 12,
    marginHorizontal: 8,
  },
  searchbar: {
    borderRadius: 20,
    height: 40,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
  },
});

export default CommonHeader;
