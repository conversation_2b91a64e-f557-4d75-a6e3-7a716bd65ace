/**
 * OutletSelector - Component for switching between different outlets
 * Provides a dropdown/selector interface for outlet management
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  FlatList,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';

import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import { Outlet } from '../../types';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

interface OutletSelectorProps {
  onOutletChange?: (outlet: Outlet) => void;
  style?: any;
  showLabel?: boolean;
  disabled?: boolean;
}

interface OutletItemProps {
  outlet: Outlet;
  isSelected: boolean;
  onSelect: (outlet: Outlet) => void;
}

const OutletItem: React.FC<OutletItemProps> = ({ outlet, isSelected, onSelect }) => {
  const theme = useTheme();
  
  return (
    <TouchableOpacity
      style={[
        styles.outletItem,
        isSelected && styles.selectedOutletItem,
      ]}
      onPress={() => onSelect(outlet)}
      activeOpacity={0.7}
    >
      <View style={styles.outletItemContent}>
        <View style={styles.outletInfo}>
          <Text style={[styles.outletName, isSelected && styles.selectedOutletName]}>
            {outlet.name}
          </Text>
          <Text style={[styles.outletAddress, isSelected && styles.selectedOutletAddress]}>
            {outlet.address}
          </Text>
          <View style={styles.outletStatus}>
            <View style={[styles.statusDot, outlet.isActive ? styles.activeDot : styles.inactiveDot]} />
            <Text style={[styles.statusText, isSelected && styles.selectedStatusText]}>
              {outlet.isActive ? 'Active' : 'Inactive'}
            </Text>
          </View>
        </View>
        {isSelected && (
          <PhosphorIcon name="check-circle" size={24} color={theme.colors.primary} />
        )}
      </View>
    </TouchableOpacity>
  );
};

export const OutletSelector: React.FC<OutletSelectorProps> = ({
  onOutletChange,
  style,
  showLabel = true,
  disabled = false,
}) => {
  const { state, actions } = useData();
  const theme = useTheme();
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  const activeOutlet = state.activeOutlet;
  const outlets = state.outlets.filter(outlet => outlet.isActive);

  const handleOutletSelect = async (outlet: Outlet) => {
    try {
      setLoading(true);
      await actions.setActiveOutlet(outlet.id);
      setModalVisible(false);
      
      if (onOutletChange) {
        onOutletChange(outlet);
      }
      
      // Show success feedback
      Alert.alert('Success', `Switched to ${outlet.name}`);
    } catch (error) {
      Alert.alert('Error', 'Failed to switch outlet. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateOutlet = () => {
    setModalVisible(false);
    // Navigate to outlet creation screen
    // This would typically be handled by navigation
    Alert.alert('Create Outlet', 'Navigate to outlet creation screen');
  };

  const renderOutletItem = ({ item }: { item: Outlet }) => (
    <OutletItem
      outlet={item}
      isSelected={activeOutlet?.id === item.id}
      onSelect={handleOutletSelect}
    />
  );

  const renderEmptyState = () => (
    <View style={[styles.emptyState, { backgroundColor: theme.colors.surface }]}>
              <PhosphorIcon name="storefront" size={48} color={theme.colors.onVariant} />
      <Text style={[styles.emptyStateText, { color: theme.colors.onVariant }]}>
        No active outlets found
      </Text>
      <Text style={[styles.emptyStateSubtext, { color: theme.colors.onVariant }]}>
        Create an outlet to get started
      </Text>
    </View>
  );

  const renderHeader = () => (
    <View style={[styles.header, { backgroundColor: theme.colors.surface }]}>
      <Text style={[styles.headerTitle, { color: theme.colors.on}]}>
        Select Outlet
      </Text>
      <Text style={[styles.headerSubtitle, { color: theme.colors.onVariant }]}>
        Choose an outlet to manage
      </Text>
    </View>
  );

  const renderFooter = () => (
    <View style={[styles.footer, { backgroundColor: theme.colors.surface }]}>
      <TouchableOpacity
        style={[styles.create, { backgroundColor: theme.colors.primary }]}
        onPress={handleCreateOutlet}
        disabled={loading}
      >
                  <PhosphorIcon name="plus" size={20} color={theme.colors.onPrimary} />
        <Text style={[styles.createText, { color: theme.colors.onPrimary }]}>
          Create New Outlet
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={[styles.container, style]}>
      {showLabel && (
        <Text style={[styles.label, { color: theme.colors.on}]}>
          Current Outlet
        </Text>
      )}
      
      <TouchableOpacity
        style={[
          styles.selector,
          { backgroundColor: theme.colors.surface, borderColor: theme.colors.outline },
          disabled && styles.disabled
        ]}
        onPress={() => !disabled && setModalVisible(true)}
        disabled={disabled}
        activeOpacity={0.7}
      >
        <View style={styles.selectorContent}>
          <View style={styles.selectorInfo}>
            <Text style={[styles.selectorTitle, { color: theme.colors.on}]}>
              {activeOutlet?.name || 'Select Outlet'}
            </Text>
            {activeOutlet && (
              <Text style={[styles.selectorSubtitle, { color: theme.colors.onVariant }]}>
                {activeOutlet.address}
              </Text>
            )}
          </View>
          <PhosphorIcon 
            name="arrow-down" 
            size={20} 
            color={theme.colors.onVariant} 
          />
        </View>
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor: theme.colors.background }]}>
          {renderHeader()}
          
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={theme.colors.primary} />
              <Text style={[styles.loadingText, { color: theme.colors.onVariant }]}>
                Switching outlet...
              </Text>
            </View>
          ) : (
            <FlatList
              data={outlets}
              renderItem={renderOutletItem}
              keyExtractor={(item) => item.id}
              contentContainerStyle={styles.listContainer}
              ListEmptyComponent={renderEmptyState}
              showsVerticalScrollIndicator={false}
            />
          )}
          
          {renderFooter()}
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  selector: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  disabled: {
    opacity: 0.5,
  },
  selectorContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  selectorInfo: {
    flex: 1,
  },
  selectorTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  selectorSubtitle: {
    fontSize: 14,
    marginTop: 2,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
  },
  headerSubtitle: {
    fontSize: 14,
    marginTop: 4,
  },
  listContainer: {
    padding: 16,
  },
  outletItem: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  selectedOutletItem: {
    borderColor: '#3b82f6',
    backgroundColor: '#eff6ff',
  },
  outletItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  outletInfo: {
    flex: 1,
  },
  outletName: {
    fontSize: 16,
    fontWeight: '600',
  },
  outletAddress: {
    fontSize: 14,
    marginTop: 2,
  },
  outletStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  activeDot: {
    backgroundColor: '#10b981',
  },
  inactiveDot: {
    backgroundColor: '#ef4444',
  },
  statusText: {
    fontSize: 12,
  },
  selectedStatusText: {
    color: '#3b82f6',
  },
  selectedOutletName: {
    color: '#3b82f6',
  },
  selectedOutletAddress: {
    color: '#3b82f6',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
  },
  emptyStateSubtext: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 24,
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  create: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
  },
  createText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
});

export default OutletSelector;