// @ts-nocheck
/**
 * BottomNavBar - Complete navigation bar with Dash<PERSON>, Scan, Plus, Orders, Settings
 * Features floating plus button and active state management
 */

import React from 'react';
import { View, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { Text } from 'react-native-paper';


import { useTheme } from '../../context/ThemeContext';
import { useSafeInsets } from '../../hooks/useSafeInsets';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

const BottomNavBar = ({
  navigation,
  currentRoute,
  onTabPress,
  onPlusPress,
  style,
  backgroundColor,
  elevation = 8,
}) => {
  const theme = useTheme();
  const insets = useSafeInsets();

  const tabs = [
    {
      name: 'Dashboard',
      icon: 'house', // Using Phosphor house icon
      label: 'Dashboard',
      route: 'Dashboard'
    },
    {
      name: 'Orders',
      icon: 'package', // Using Phosphor package icon for orders
      label: 'Orders',
      route: 'Orders'
    },
    {
      name: 'plus',
      icon: 'plus',
      label: 'Add',
      isPlus: true
    },
    {
      name: 'Customers',
      icon: 'users',
      label: 'Customers',
      route: 'Customers'
    },
    {
      name: 'Settings',
      icon: 'gear', // Using Phosphor gear icon for settings
      label: 'Settings',
      route: 'Settings'
    },
  ];

  const handleTabPress = (tab) => {
    if (tab.isPlus) {
      if (onPlusPress) {
        onPlusPress();
      } else {
        // Default plus action - show quick add menu
        Alert.alert(
          'Quick Add',
          'What would you like to add?',
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'New Product',
              onPress: () => navigation?.navigate('Products'),
            },
            {
              text: 'New Order',
              onPress: () => navigation?.navigate('Orders'),
            },
            {
              text: 'Scan Item',
              onPress: () => navigation?.navigate('Scan'),
            },
          ]
        );
      }
      return;
    }

    // Regular tab navigation
    if (onTabPress && tab.route) {
      onTabPress(tab.route);
    } else if (navigation && tab.route) {
      navigation.navigate(tab.route);
    }
  };

  const isTabActive = (tabName) => {
    // Handle nested navigation routes (like Settings stack)
    if (tabName === 'Settings') {
      return currentRoute === 'Settings' ||
             currentRoute === 'SettingsMain' ||
             currentRoute === 'Profile' ||
             currentRoute === 'Reports';
    }
    if (tabName === 'Customers') {
      return currentRoute === 'Customers' ||
             currentRoute === 'CustomerDetails';
    }
    if (tabName === 'Products') {
      return currentRoute === 'Products' ||
             currentRoute === 'AddProduct';
    }
    return currentRoute === tabName || currentRoute === tabName.replace('Main', '');
  };

  const navBarBackgroundColor = backgroundColor || theme.colors.surface;

  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor: navBarBackgroundColor,
          paddingBottom: insets.bottom,
          borderTopColor: `${theme.colors.outline  }20`
        },
        style
      ]}
      elevation={elevation}
    >
      {tabs.map((tab) => {
        const isActive = isTabActive(tab.name);
        const isPlusTab = tab.isPlus;

        return (
          <TouchableOpacity
            key={tab.name}
            style={[
              styles.tab,
              isPlusTab && styles.plusTab,
              isPlusTab && { backgroundColor: theme.colors.primary }
            ]}
            onPress={() => handleTabPress(tab)}
            activeOpacity={0.7}
          >
            {isPlusTab ? (
              <View style={styles.plusIconContainer}>
                <PhosphorIcon
                  name={tab.icon}
                  size={24}
                  color={theme.colors.onPrimary}
                />
              </View>
            ) : (
              <>
                <PhosphorIcon
                  name={tab.icon}
                  size={24}
                  color={isActive ? theme.colors.primary : theme.colors.onVariant}
                />
                <Text
                  variant="labelSmall"
                  style={{
                    color: isActive ? theme.colors.primary : theme.colors.onVariant,
                    marginTop: 2,
                    fontWeight: isActive ? '600' : '400',
                    fontSize: 10
                  }}
                >
                  {tab.label}
                </Text>
              </>
            )}
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    height: 58,
    paddingTop: 4,
    paddingHorizontal: 12,
    borderTopWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 4,
    paddingHorizontal: 2,
  },
  plusTab: {
    width: 44,
    height: 44,
    borderRadius: 12,
    marginTop: 0,
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    alignSelf: 'center',
  },
  plusIconContainer: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default BottomNavBar;
