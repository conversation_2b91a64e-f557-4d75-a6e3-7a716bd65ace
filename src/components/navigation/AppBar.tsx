import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text, IconButton } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useTheme } from '../../context/ThemeContext';
import { SPACING, TYPOGRAPHY } from '../../theme/theme';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

interface AppBarAction {
  icon?: string;
  text?: string;
  onPress: () => void;
  disabled?: boolean;
  color?: string;
}

interface AppBarProps {
  title: string;
  onBackPress: () => void;
  actions?: AppBarAction[];
  showBackButton?: boolean;
  backgroundColor?: string;
  elevation?: number;
}

const AppBar: React.FC<AppBarProps> = ({
  title,
  onBackPress,
  actions = [],
  showBackButton = true,
  backgroundColor,
  elevation = 2,
}) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();

  const barBackgroundColor = backgroundColor || theme.colors.surface;

  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor: barBackgroundColor,
          paddingTop: insets.top,
          borderBottomColor: `${theme.colors.outline}20`,
          elevation,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: elevation },
          shadowOpacity: 0.1,
          shadowRadius: elevation,
        },
      ]}
    >
      <View style={styles.content}>
        {/* Left Section - Back Button */}
        <View style={styles.leftSection}>
          {showBackButton && (
            <TouchableOpacity
              onPress={onBackPress}
              style={styles.backButton}
            >
              <PhosphorIcon
                name="arrow-left"
                size={24}
                color={theme.colors.onSurface}
              />
            </TouchableOpacity>
          )}
        </View>

        {/* Center Section - Title */}
        <View style={styles.centerSection}>
          <Text
            style={[
              styles.title,
              {
                color: theme.colors.onSurface,
                fontSize: TYPOGRAPHY.fontSize.xl,
                fontWeight: TYPOGRAPHY.fontWeight.bold,
              },
            ]}
            numberOfLines={1}
          >
            {title}
          </Text>
        </View>

        {/* Right Section - Action Buttons */}
        <View style={styles.rightSection}>
          {actions.map((action, index) => (
            action.text ? (
              <TouchableOpacity
                key={index}
                onPress={action.onPress}
                disabled={action.disabled}
                style={[
                  styles.textButton,
                  { opacity: action.disabled ? 0.6 : 1 }
                ]}
              >
                <Text
                  style={[
                    styles.textButtonText,
                    {
                      color: action.color || theme.colors.primary,
                      fontSize: TYPOGRAPHY.fontSize.lg,
                      fontWeight: TYPOGRAPHY.fontWeight.bold,
                    }
                  ]}
                >
                  {action.text}
                </Text>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                key={index}
                onPress={action.onPress}
                disabled={action.disabled}
                style={[
                  styles.actionButton,
                  { opacity: action.disabled ? 0.6 : 1 }
                ]}
              >
                <PhosphorIcon
                  name={action.icon as any}
                  size={24}
                  color={action.color || theme.colors.onSurface}
                />
              </TouchableOpacity>
            )
          ))}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderBottomWidth: 1,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.sm,
    minHeight: 56,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: 48,
  },
  centerSection: {
    flex: 1,
    alignItems: 'flex-start',
    justifyContent: 'center',
    paddingHorizontal: SPACING.sm,
    marginLeft: -SPACING.xs,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: 48,
  },
  backButton: {
    padding: 8,
    margin: 0,
  },
  actionButton: {
    padding: 8,
    margin: 0,
    marginLeft: SPACING.xs,
  },
  textButton: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    marginLeft: SPACING.xs,
    borderRadius: 8,
  },
  textButtonText: {
    textAlign: 'center',
  },
  title: {
    textAlign: 'center',
  },
});

export default AppBar; 