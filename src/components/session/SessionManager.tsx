import React, { useEffect, useState, useRef } from 'react';
import { Alert, AppState, AppStateStatus } from 'react-native';

import { useAuth, useSessionInfo } from '../../context/AuthContext';
import LoggingService from '../../services/LoggingService';

/**
 * SessionManager - <PERSON>les session monitoring and automatic logout
 * 
 * Features:
 * - Session expiration monitoring
 * - Automatic logout on session expiry
 * - App state monitoring (background/foreground)
 * - Session warnings before expiry
 * - Inactivity detection
 * - Session refresh handling
 * 
 * @component SessionManager
 * @version 1.0.0
 */

interface SessionManagerProps {
  children: React.ReactNode;
  warningThreshold?: number; // Minutes before expiry to show warning
  inactivityTimeout?: number; // Minutes of inactivity before logout
}

const SessionManager: React.FC<SessionManagerProps> = ({
  children,
  warningThreshold = 5, // 5 minutes warning
  inactivityTimeout = 30, // 30 minutes inactivity
}) => {
  const { state, logout, forceLogout, refreshSession } = useAuth();
  const sessionInfo = useSessionInfo();
  
  // Refs for timers
  const warningTimerRef = useRef<NodeJS.Timeout | null>(null);
  const logoutTimerRef = useRef<NodeJS.Timeout | null>(null);
  const inactivityTimerRef = useRef<NodeJS.Timeout | null>(null);
  const appStateRef = useRef<AppStateStatus>(AppState.currentState);
  
  // State
  const [warningShown, setWarningShown] = useState<boolean>(false);
  const [lastActivity, setLastActivity] = useState<number>(Date.now());

  /**
   * Clear all timers
   */
  const clearAllTimers = (): void => {
    if (warningTimerRef.current) {
      clearTimeout(warningTimerRef.current);
      warningTimerRef.current = null;
    }
    if (logoutTimerRef.current) {
      clearTimeout(logoutTimerRef.current);
      logoutTimerRef.current = null;
    }
    if (inactivityTimerRef.current) {
      clearTimeout(inactivityTimerRef.current);
      inactivityTimerRef.current = null;
    }
  };

  /**
   * Update last activity timestamp
   */
  const updateActivity = (): void => {
    setLastActivity(Date.now());
    setWarningShown(false);
    
    // Reset inactivity timer
    if (inactivityTimerRef.current) {
      clearTimeout(inactivityTimerRef.current);
    }
    
    if (state.isAuthenticated) {
      inactivityTimerRef.current = setTimeout(() => {
        handleInactivityLogout();
      }, inactivityTimeout * 60 * 1000);
    }
  };

  /**
   * Handle inactivity logout
   */
  const handleInactivityLogout = async (): Promise<void> => {
    try {
      LoggingService.warn(`User inactive for ${inactivityTimeout} minutes, forcing logout`, 'SESSION');
      
      Alert.alert(
        'Session Expired',
        `You have been logged out due to ${inactivityTimeout} minutes of inactivity.`,
        [
          {
            text: 'OK',
            onPress: async () => {
              await forceLogout('inactivity');
            },
          },
        ],
        { cancelable: false }
      );
    } catch (error) {
      LoggingService.error('Inactivity logout failed', 'SESSION', error as Error);
    }
  };

  /**
   * Show session expiry warning
   */
  const showExpiryWarning = (timeUntilExpiry: number): void => {
    if (warningShown) return;
    
    setWarningShown(true);
    const minutesLeft = Math.ceil(timeUntilExpiry / (60 * 1000));
    
    LoggingService.warn(`Session expiring in ${minutesLeft} minutes`, 'SESSION');
    
    Alert.alert(
      'Session Expiring',
      `Your session will expire in ${minutesLeft} minute${minutesLeft !== 1 ? 's' : ''}. Would you like to extend your session?`,
      [
        {
          text: 'Logout Now',
          style: 'destructive',
          onPress: async () => {
            await logout();
          },
        },
        {
          text: 'Extend Session',
          onPress: async () => {
            try {
              await refreshSession();
              setWarningShown(false);
              LoggingService.info('Session extended by user', 'SESSION');
            } catch (error) {
              LoggingService.error('Session refresh failed', 'SESSION', error as Error);
              Alert.alert(
                'Session Refresh Failed',
                'Unable to extend your session. You will be logged out shortly.',
                [{ text: 'OK' }]
              );
            }
          },
        },
      ],
      { cancelable: false }
    );
  };

  /**
   * Handle automatic logout on session expiry
   */
  const handleSessionExpiry = async (): Promise<void> => {
    try {
      LoggingService.warn('Session expired, forcing logout', 'SESSION');
      
      Alert.alert(
        'Session Expired',
        'Your session has expired. Please log in again to continue.',
        [
          {
            text: 'OK',
            onPress: async () => {
              await forceLogout('expired');
            },
          },
        ],
        { cancelable: false }
      );
    } catch (error) {
      LoggingService.error('Session expiry logout failed', 'SESSION', error as Error);
    }
  };

  /**
   * Setup session monitoring timers
   */
  const setupSessionTimers = (): void => {
    if (!state.isAuthenticated || !sessionInfo?.timeUntilExpiry) {
      return;
    }

    clearAllTimers();

    const timeUntilExpiry = sessionInfo.timeUntilExpiry;
    const warningTime = warningThreshold * 60 * 1000; // Convert to milliseconds

    // Set warning timer
    if (timeUntilExpiry > warningTime) {
      warningTimerRef.current = setTimeout(() => {
        showExpiryWarning(timeUntilExpiry - warningTime);
      }, timeUntilExpiry - warningTime);
    } else if (timeUntilExpiry > 0 && !warningShown) {
      // Show warning immediately if we're already in warning period
      showExpiryWarning(timeUntilExpiry);
    }

    // Set logout timer
    if (timeUntilExpiry > 0) {
      logoutTimerRef.current = setTimeout(() => {
        handleSessionExpiry();
      }, timeUntilExpiry);
    }

    // Set inactivity timer
    inactivityTimerRef.current = setTimeout(() => {
      handleInactivityLogout();
    }, inactivityTimeout * 60 * 1000);

    LoggingService.info(`Session timers set - Warning: ${Math.ceil((timeUntilExpiry - warningTime) / 60000)}min, Expiry: ${Math.ceil(timeUntilExpiry / 60000)}min`, 'SESSION');
  };

  /**
   * Handle app state changes
   */
  const handleAppStateChange = (nextAppState: AppStateStatus): void => {
    const previousState = appStateRef.current;
    appStateRef.current = nextAppState;

    if (state.isAuthenticated) {
      if (previousState === 'background' && nextAppState === 'active') {
        // App came to foreground - refresh session and update activity
        LoggingService.info('App returned to foreground, refreshing session', 'SESSION');
        updateActivity();
        refreshSession();
      } else if (nextAppState === 'background') {
        // App went to background - log the event
        LoggingService.info('App went to background', 'SESSION');
      }
    }
  };

  /**
   * Setup session monitoring
   */
  useEffect(() => {
    if (state.isAuthenticated) {
      setupSessionTimers();
      updateActivity();
    } else {
      clearAllTimers();
      setWarningShown(false);
    }

    return () => {
      clearAllTimers();
    };
  }, [state.isAuthenticated, sessionInfo?.timeUntilExpiry]);

  /**
   * Setup app state monitoring
   */
  useEffect(() => {
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    
    return () => {
      subscription?.remove();
    };
  }, [state.isAuthenticated]);

  /**
   * Setup activity monitoring
   */
  useEffect(() => {
    if (state.isAuthenticated) {
      // Update activity on any state change
      updateActivity();
    }
  }, [state.user, state.isAuthenticated]);

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      clearAllTimers();
    };
  }, []);

  // Render children without any UI changes
  return <>{children}</>;
};

export default SessionManager;