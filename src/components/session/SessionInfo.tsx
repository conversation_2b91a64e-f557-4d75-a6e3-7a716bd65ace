import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Card, Button, Chip } from 'react-native-paper';


import { useAuth, useCurrentUser, useSessionInfo } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY } from '../../theme/theme';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

/**
 * SessionInfo - Displays current session information
 * 
 * Features:
 * - Session status and details
 * - Time until expiry
 * - Last activity timestamp
 * - Session refresh functionality
 * - User role and permissions
 * - Device information
 * 
 * @component SessionInfo
 * @version 1.0.0
 */

interface SessionInfoProps {
  showDetails?: boolean;
  onRefreshSession?: () => void;
}

const SessionInfo: React.FC<SessionInfoProps> = ({
  showDetails = true,
  onRefreshSession,
}) => {
  const theme = useTheme();
  const { state, refreshSession } = useAuth();
  const currentUser = useCurrentUser();
  const sessionInfo = useSessionInfo();

  // State for real-time updates
  const [currentTime, setCurrentTime] = useState<number>(Date.now());

  // Update current time every minute
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(Date.now());
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, []);

  /**
   * Format time duration
   */
  const formatDuration = (milliseconds: number): string => {
    const minutes = Math.floor(milliseconds / (60 * 1000));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}d ${hours % 24}h`;
    } else if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else {
      return `${minutes}m`;
    }
  };

  /**
   * Format timestamp
   */
  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString();
  };

  /**
   * Get session status
   */
  const getSessionStatus = (): {
    status: 'active' | 'expiring' | 'expired';
    color: string;
    icon: string;
    text: string;
  } => {
    if (!sessionInfo?.timeUntilExpiry) {
      return {
        status: 'expired',
        color: theme.colors.error,
        icon: 'warning',
        text: 'Expired',
      };
    }

    const timeLeft = sessionInfo.timeUntilExpiry;
    const fiveMinutes = 5 * 60 * 1000;

    if (timeLeft <= 0) {
      return {
        status: 'expired',
        color: theme.colors.error,
        icon: 'warning',
        text: 'Expired',
      };
    } else if (timeLeft <= fiveMinutes) {
      return {
        status: 'expiring',
        color: theme.colors.error,
        icon: 'clock',
        text: 'Expiring Soon',
      };
    } else {
      return {
        status: 'active',
        color: theme.colors.primary,
        icon: 'checkmark-circle',
        text: 'Active',
      };
    }
  };

  const sessionStatus = getSessionStatus();

  const handleRefreshSession = async (): Promise<void> => {
    try {
      LoggingService.info('Refreshing session', 'SESSION');
      await refreshSession();
      onRefreshSession?.();
    } catch (error) {
      LoggingService.error('Failed to refresh session', 'SESSION', error as Error);
    }
  };

  if (!showDetails) {
    return (
      <Card style={[styles.compactCard, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.compactContent}>
          <View style={styles.statusRow}>
            <PhosphorIcon 
              name={sessionStatus.icon as any} 
              size={16} 
              color={sessionStatus.color} 
            />
            <Text variant="bodySmall" style={{ color: sessionStatus.color, marginLeft: 4 }}>
              {sessionStatus.text}
            </Text>
          </View>
          {sessionInfo?.timeUntilExpiry && sessionInfo.timeUntilExpiry > 0 && (
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              {formatDuration(sessionInfo.timeUntilExpiry)} remaining
            </Text>
          )}
        </View>
      </Card>
    );
  }

  return (
    <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.statusSection}>
            <PhosphorIcon 
              name={sessionStatus.icon as any} 
              size={24} 
              color={sessionStatus.color} 
            />
            <View style={styles.statusInfo}>
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                Session Status
              </Text>
              <Text variant="bodySmall" style={{ color: sessionStatus.color, fontWeight: '500' }}>
                {sessionStatus.text}
              </Text>
            </View>
          </View>
          <Chip 
            mode="outlined" 
            style={[styles.statusChip, { borderColor: sessionStatus.color }]}
            textStyle={{ color: sessionStatus.color }}
          >
            {sessionStatus.status.toUpperCase()}
          </Chip>
        </View>

        {/* Session Details */}
        <View style={styles.details}>
          {sessionInfo?.timeUntilExpiry && sessionInfo.timeUntilExpiry > 0 && (
            <View style={styles.detailRow}>
              <PhosphorIcon name="clock" size={16} color={theme.colors.onSurfaceVariant} />
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginLeft: 8 }}>
                Time Remaining: {formatDuration(sessionInfo.timeUntilExpiry)}
              </Text>
            </View>
          )}

          {sessionInfo?.lastActivity && (
            <View style={styles.detailRow}>
              <PhosphorIcon name="clock" size={16} color={theme.colors.onSurfaceVariant} />
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginLeft: 8 }}>
                Last Activity: {formatTimestamp(sessionInfo.lastActivity)}
              </Text>
            </View>
          )}

          {currentUser?.role && (
            <View style={styles.detailRow}>
              <PhosphorIcon name="user" size={16} color={theme.colors.onSurfaceVariant} />
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginLeft: 8 }}>
                Role: {currentUser.role}
              </Text>
            </View>
          )}


        </View>

        {/* Actions */}
        <View style={styles.actions}>
          <Button
            mode="outlined"
            onPress={handleRefreshSession}
            icon="refresh"
            style={styles.refreshButton}
            textColor={theme.colors.primary}
          >
            Refresh Session
          </Button>
        </View>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    margin: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
  },
  compactCard: {
    margin: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
  },
  content: {
    padding: SPACING.lg,
  },
  compactContent: {
    padding: SPACING.md,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  statusSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusInfo: {
    marginLeft: SPACING.md,
  },
  statusChip: {
    borderRadius: BORDER_RADIUS.xl,
  },
  details: {
    marginBottom: SPACING.lg,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  refreshButton: {
    borderRadius: BORDER_RADIUS.md,
  },
});

export default SessionInfo;