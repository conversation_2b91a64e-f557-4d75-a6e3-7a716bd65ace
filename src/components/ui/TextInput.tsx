import React, { useState, useEffect } from 'react';
import { ViewStyle, View, StyleSheet, Animated, TextInput as RNTextInput } from 'react-native';
import { Text } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY } from '../../theme/theme';

type InputType = 'text' | 'email' | 'number' | 'password' | 'search' | 'date';

interface Validators {
  [key: string]: (value: string, minLength?: number) => string;
}

const validators: Validators = {
  email: (value: string) => !value || /\S+@\S+\.\S+/.test(value) ? '' : 'Invalid email address',
  number: (value: string) => value === '' || (!isNaN(Number(value)) && isFinite(Number(value))) ? '' : 'Invalid number',
  password: (value: string, minLength: number = 8) => value.length >= minLength ? '' : `Password must be at least ${minLength} characters`,
  text: (value: string) => '',
};

interface UnifiedTextInputProps {
  label: string;
  value: string;
  onChangeText: (value: string) => void;
  type?: InputType;
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  validate?: (value: string) => string;
  error?: string;
  onBlur?: (e: any) => void;
  onFocus?: (e: any) => void;
  style?: ViewStyle;
  rightAffix?: string;
  placeholder?: string;
  disabled?: boolean;
  [key: string]: any;
}

/**
 * Airbnb-style TextInput component with floating label animation
 */
const TextInput: React.FC<UnifiedTextInputProps> = ({
  label,
  value,
  onChangeText,
  type = 'text',
  required = false,
  minLength,
  maxLength,
  validate,
  error: externalError,
  onBlur,
  onFocus,
  style,
  rightAffix,
  placeholder,
  disabled = false,
  ...rest
}) => {
  const [error, setError] = useState('');
  const [touched, setTouched] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [labelAnimation] = useState(new Animated.Value(value ? 1 : 0));
  const theme = useTheme();

  useEffect(() => {
    if (externalError !== undefined) setError(externalError);
  }, [externalError]);

  useEffect(() => {
    // Animate label when value changes or input is focused
    Animated.timing(labelAnimation, {
      toValue: value || isFocused ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [value, isFocused, labelAnimation]);

  const runValidation = (val: string): string => {
    let err = '';
    if (required && (!val || val.trim() === '')) err = `${label || 'This field'} is required`;
    else if (type && validators[type]) err = validators[type](val, minLength);
    if (!err && minLength && val.length < minLength) err = `${label || 'This field'} must be at least ${minLength} characters`;
    if (!err && maxLength && val.length > maxLength) err = `${label || 'This field'} must be at most ${maxLength} characters`;
    if (!err && typeof validate === 'function') err = validate(val) || '';
    setError(err);
    return err;
  };

  const handleChange = (val: string): void => {
    onChangeText(val);
    if (touched) runValidation(val);
  };

  const handleBlur = (e: any): void => {
    setIsFocused(false);
    setTouched(true);
    runValidation(value);
    if (onBlur) onBlur(e);
  };

  const handleFocus = (e: any): void => {
    setIsFocused(true);
    if (onFocus) onFocus(e);
  };

  // Create placeholder text with optional indicator
  const getPlaceholderText = () => {
    if (placeholder) return placeholder;
    const baseText = label;
    return required ? baseText : `${baseText} (optional)`;
  };

  const labelStyle = {
    transform: [{
      translateY: labelAnimation.interpolate({
        inputRange: [0, 1],
        outputRange: [0, -16],
      }),
    }],
    fontSize: labelAnimation.interpolate({
      inputRange: [0, 1],
      outputRange: [TYPOGRAPHY.fontSize.lg, TYPOGRAPHY.fontSize.sm],
    }),
    color: labelAnimation.interpolate({
      inputRange: [0, 1],
      outputRange: [theme.colors.onSurfaceVariant, isFocused ? theme.colors.onSurface : theme.colors.onSurfaceVariant],
    }),
  };

  return (
    <View style={[styles.container, style]}>
      <View style={styles.inputWrapper}>
        <Animated.Text style={[styles.floatingLabel, labelStyle]}>
          {getPlaceholderText()}
        </Animated.Text>
        
        <RNTextInput
          value={value}
          onChangeText={handleChange}
          onBlur={handleBlur}
          onFocus={handleFocus}
          editable={!disabled}
          placeholder=""
          style={[
            styles.textInput,
            {
              borderColor: isFocused 
                ? theme.colors.onSurface 
                : error 
                  ? theme.colors.error 
                  : theme.colors.outline,
              borderWidth: isFocused ? 2 : 1,
              color: theme.colors.onSurface,
            }
          ]}
          secureTextEntry={type === 'password'}
          keyboardType={type === 'email' ? 'email-address' : type === 'number' ? 'numeric' : 'default'}
          autoCapitalize={type === 'email' ? 'none' : 'sentences'}
          {...rest}
        />
        
        {rightAffix && (
          <Text style={[styles.affix, { color: theme.colors.onSurfaceVariant }]}>
            {rightAffix}
          </Text>
        )}
      </View>
      
      {!!error && (
        <Text style={[styles.errorText, { color: theme.colors.error }]}>{error}</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.sm,
  },
  inputWrapper: {
    position: 'relative',
  },
  floatingLabel: {
    position: 'absolute',
    left: SPACING.md,
    top: 0,
    bottom: 0,
    zIndex: 1,
    paddingHorizontal: 0,
    fontWeight: TYPOGRAPHY.fontWeight.medium,
    textAlignVertical: 'center',
    includeFontPadding: true,
  },
  textInput: {
    backgroundColor: 'transparent',
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: TYPOGRAPHY.fontWeight.medium,
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 1,
    paddingHorizontal: SPACING.md,
    paddingTop: SPACING.lg,
    paddingBottom: SPACING.md,
    minHeight: 60,
  },
  affix: {
    position: 'absolute',
    right: SPACING.md,
    top: SPACING.md,
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: TYPOGRAPHY.fontWeight.medium,
  },
  errorText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    marginTop: SPACING.xs,
    marginLeft: SPACING.xs,
  },
});

export default TextInput;