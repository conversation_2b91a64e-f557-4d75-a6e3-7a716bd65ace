/**
 * EmptyState - Consistent empty state component for all screens
 * Provides unified empty state with icon, title, description, and optional action
 */

import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { Text, Button } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';


interface UnifiedEmptyStateProps {
  // Content
  icon?: string;
  title?: string;
  description: string;
  
  // Action
  actionLabel: string;
  onActionPress: () => void;
  
  // Type-specific presets
  type: string; // 'products', 'orders', 'activities', 'search', 'error'
  
  // Styling
  style: ViewStyle;
  iconSize?: number;
  iconColor: string;
  
  // Search specific
  searchQuery: string;
}

interface PresetConfig {
  icon: string;
  title: string;
  description: string;
  actionLabel?: string;
}

const EmptyState: React.FC<UnifiedEmptyStateProps> = ({
  // Content
  icon = 'package',
  title = 'No items found',
  description,
  
  // Action
  actionLabel,
  onActionPress,
  
  // Type-specific presets
  type, // 'products', 'orders', 'activities', 'search', 'error'
  
  // Styling
  style,
  iconSize = 64,
  iconColor,
  
  // Search specific
  searchQuery,
}) => {
  const theme = useTheme();

  // Get preset configuration based on type
  const getPresetConfig = (): PresetConfig => {
    switch (type) {
      case 'products':
        return {
          icon: 'package',
          title: 'No products found',
          description: 'Start by adding your first product to get started.',
          actionLabel: 'Add Product',
        };
      
      case 'orders':
        return {
          icon: 'receipt',
          title: 'No orders found',
          description: 'Orders will appear here once customers start placing them.',
          actionLabel: 'Create Order',
        };
      
      case 'activities':
        return {
          icon: 'clock',
          title: 'No activities found',
          description: 'Activity history will appear here as you use the app.',
        };
      
      case 'search':
        return {
          icon: 'magnifying-glass',
          title: searchQuery ? `No results for "${searchQuery}"` : 'Start searching',
          description: searchQuery 
            ? 'Try adjusting your search terms or filters.'
            : 'Enter a search term to find items.',
        };
      
      case 'error':
        return {
          icon: 'warning',
          title: 'Something went wrong',
          description: 'Unable to load data. Please try again.',
          actionLabel: 'Retry',
        };
      
      case 'network':
        return {
          icon: 'wifi-off',
          title: 'No internet connection',
          description: 'Please check your connection and try again.',
          actionLabel: 'Retry',
        };
      
      default:
        return {
          icon,
          title,
          description,
          actionLabel,
        };
    }
  };

  const config = getPresetConfig();
  const finalIcon = icon !== 'package' ? icon : config.icon;
  const finalTitle = title !== 'No items found' ? title : config.title;
  const finalDescription = description || config.description;
  const finalActionLabel = actionLabel || config.actionLabel;

  return (
    <View style={[styles.container, style]}>
      <View style={[styles.iconContainer, { backgroundColor: `${iconColor  }15` }]}>
        <PhosphorIcon 
          name={finalIcon as any} 
          size={iconSize} 
          color={iconColor || theme.colors.onVariant} 
        />
      </View>
      
      <Text 
        variant="headlineSmall" 
        style={[styles.title, { color: theme.colors.on}]}
      >
        {finalTitle}
      </Text>
      
      <Text 
        variant="bodyMedium" 
        style={[styles.description, { color: theme.colors.onVariant }]}
      >
        {finalDescription}
      </Text>
      
      {finalActionLabel && onActionPress && (
        <Button
          mode="contained"
          onPress={onActionPress}
          style={styles.actionButton}
          icon={type === 'products' ? 'plus' : type === 'orders' ? 'plus' : undefined}
        >
          {finalActionLabel}
        </Button>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    minHeight: 200,
  },
  iconContainer: {
    width: 96,
    height: 96,
    borderRadius: 48,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    textAlign: 'center',
    marginBottom: 8,
    fontWeight: '600',
  },
  description: {
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
    maxWidth: 280,
  },
  actionButton: {
    minWidth: 140,
  },
});

export default EmptyState;