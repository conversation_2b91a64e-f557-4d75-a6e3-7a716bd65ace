import React, { useRef, useEffect } from 'react';
import { Pressable, Animated, StyleSheet, ViewStyle } from 'react-native';

import { useTheme } from '../../context/ThemeContext';

const SWITCH_WIDTH = 48;
const SWITCH_HEIGHT = 28;
const THUMB_SIZE = 22;

interface UnifiedSwitchProps {
  value: boolean;
  onValueChange?: (value: boolean) => void;
  disabled?: boolean;
  style?: ViewStyle;
}

const Switch: React.FC<UnifiedSwitchProps> = ({ value, onValueChange, disabled = false, style }) => {
  const theme = useTheme();

  const anim = useRef(new Animated.Value(value ? 1 : 0)).current;

  useEffect(() => {
    Animated.timing(anim, {
      toValue: value ? 1 : 0,
      duration: 180,
      useNativeDriver: false,
    }).start();
  }, [value]);

  const thumbTranslate = anim.interpolate({
    inputRange: [0, 1],
    outputRange: [3, SWITCH_WIDTH - THUMB_SIZE - 3],
  });

  // Use theme colors
  const activeColor = theme.colors.primary;
  const inactiveColor = theme.colors.surfaceVariant || '#E0E0E0';
  const thumbOnColor = theme.colors.primary;
  const thumbOffColor = theme.colors.onVariant || '#BDBDBD';

  return (
    <Pressable
      onPress={() => !disabled && onValueChange && onValueChange(!value)}
      style={[
        styles.switch,
        {
          backgroundColor: value ? `${activeColor  }22` : inactiveColor,
          opacity: disabled ? 0.5 : 1,
        },
        style,
      ]}
      accessibilityRole="switch"
      accessibilityState={{ checked: value, disabled }}
    >
      <Animated.View
        style={[
          styles.thumb,
          {
            backgroundColor: value ? thumbOnColor : thumbOffColor,
            transform: [{ translateX: thumbTranslate }],
            shadowOpacity: value ? 0.2 : 0.08,
          },
        ]}
      />
    </Pressable>
  );
};

const styles = StyleSheet.create({
  switch: {
    width: SWITCH_WIDTH,
    height: SWITCH_HEIGHT,
    borderRadius: SWITCH_HEIGHT / 2,
    justifyContent: 'center',
    backgroundColor: '#E0E0E0',
    padding: 0,
  },
  thumb: {
    width: THUMB_SIZE,
    height: THUMB_SIZE,
    borderRadius: THUMB_SIZE / 2,
    position: 'absolute',
    top: 3,
    left: 0,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowRadius: 2,
    shadowOpacity: 0.08,
  },
});

export default Switch;