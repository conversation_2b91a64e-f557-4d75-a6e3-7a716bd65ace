import React, { useState, useEffect, useRef } from 'react';
import { TouchableOpacity, View, StyleSheet, Animated, ScrollView, TextInput as RNTextInput } from 'react-native';
import { Text } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY, SHADOWS } from '../../theme/theme';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';
import BottomSheet from '../bottomsheets/BottomSheet';

interface DropdownOption {
  label: string;
  value: string;
}

interface DropdownProps {
  label?: string;
  placeholder?: string;
  value?: string;
  options: DropdownOption[];
  onValueChange: (value: string) => void;
  disabled?: boolean;
  required?: boolean;
  error?: string;
  style?: any;
  width?: number | string;
  flex?: number;
}

const Dropdown: React.FC<DropdownProps> = ({
  label,
  placeholder = 'Select an option',
  value,
  options,
  onValueChange,
  disabled = false,
  required = false,
  error,
  style,
  width,
  flex,
}) => {
  const theme = useTheme();
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [isFocused, setIsFocused] = useState<boolean>(false);
  const [labelAnimation] = useState(new Animated.Value(value ? 1 : 0));
  const [searchQuery, setSearchQuery] = useState<string>('');
  const bottomSheetRef = useRef<any>(null);

  const selectedOption = options.find(option => option.value === value);
  const displayText = selectedOption ? selectedOption.label : '';

  // Filter options based on search query
  const filteredOptions = React.useMemo(() => 
    options.filter(option =>
      option.label.toLowerCase().includes(searchQuery.toLowerCase())
    ), [options, searchQuery]
  );

  useEffect(() => {
    // Animate label when value changes or dropdown is focused
    Animated.timing(labelAnimation, {
      toValue: (value && selectedOption) || isFocused ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [value, selectedOption, isFocused, labelAnimation]);



  const handleSelect = (optionValue: string) => {
    onValueChange(optionValue);
    setIsOpen(false);
    setIsFocused(false);
    setSearchQuery('');
    bottomSheetRef.current?.close();
  };

  const handleInputPress = () => {
    if (!disabled) {
      setIsOpen(true);
      setIsFocused(true);
      setSearchQuery('');
      bottomSheetRef.current?.expand();
    }
  };

  const handleInputFocus = () => {
    if (!disabled) {
      setIsFocused(true);
      setIsOpen(true);
      setSearchQuery('');
    }
  };

  const handleBottomSheetClose = () => {
    setIsOpen(false);
    setIsFocused(false);
    setSearchQuery('');
  };

  const labelStyle = {
    transform: [{
      translateY: labelAnimation.interpolate({
        inputRange: [0, 1],
        outputRange: [0, -24],
      }),
    }],
    fontSize: labelAnimation.interpolate({
      inputRange: [0, 1],
      outputRange: [TYPOGRAPHY.fontSize.lg, TYPOGRAPHY.fontSize.sm],
    }),
    color: labelAnimation.interpolate({
      inputRange: [0, 1],
      outputRange: [theme.colors.onSurfaceVariant, isFocused ? theme.colors.onSurface : theme.colors.onSurfaceVariant],
    }),
  };

  const renderBottomSheetContent = () => (
    <View style={styles.bottomSheetContent}>
      {/* Search Input - Compact and fixed at top */}
      <View style={[styles.searchContainer, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.searchInputWrapper}>
          <PhosphorIcon 
            name="search" 
            size={18} 
            color={theme.colors.onSurface}
            style={styles.searchIcon}
          />
          <RNTextInput
            placeholder="Search category..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            autoCorrect={false}
            autoCapitalize="none"
            autoComplete="off"
            spellCheck={false}
            returnKeyType="search"
            clearButtonMode="while-editing"
            keyboardType="default"
            textContentType="none"
            style={[
              styles.searchInput,
              {
                borderColor: theme.colors.outline,
                color: theme.colors.onSurface,
                backgroundColor: theme.colors.surface,
              }
            ]}
            placeholderTextColor={theme.colors.onSurfaceVariant}
          />
        </View>
      </View>

      {/* Scrollable Options List */}
      <ScrollView 
        style={styles.optionsScroll}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        keyboardDismissMode="on-drag"
        contentContainerStyle={styles.optionsContentContainer}
      >
        {filteredOptions.length > 0 ? (
          filteredOptions.map((option) => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.optionItem,
                {
                  backgroundColor: option.value === value 
                    ? theme.colors.primaryContainer 
                    : 'transparent',
                }
              ]}
              onPress={() => handleSelect(option.value)}
            >
              <Text style={[
                styles.optionText,
                {
                  color: option.value === value 
                    ? theme.colors.onPrimaryContainer 
                    : theme.colors.onSurface,
                  fontWeight: option.value === value 
                    ? TYPOGRAPHY.fontWeight.semibold 
                    : TYPOGRAPHY.fontWeight.medium,
                }
              ]}>
                {option.label}
              </Text>
              {option.value === value && (
                <PhosphorIcon 
                  name="check" 
                  size={20} 
                  color={theme.colors.onPrimaryContainer}
                />
              )}
            </TouchableOpacity>
          ))
        ) : (
          <View style={styles.noResultsContainer}>
            <Text style={[styles.noResultsText, { color: theme.colors.onSurfaceVariant }]}>
              No options found
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );

  return (
    <View style={[styles.container, { width, flex }, style]}>
      <View style={styles.dropdownWrapper}>
        {(isFocused || selectedOption) && (
          <Animated.Text style={[styles.floatingLabel, labelStyle]}>
            {label}
            {required && <Text style={{ color: theme.colors.error }}> *</Text>}
          </Animated.Text>
        )}
        
                <View style={styles.inputWrapper}>
          <TouchableOpacity
            style={[
              styles.textInput,
              {
                borderColor: isFocused 
                  ? theme.colors.onSurface 
                  : error 
                    ? theme.colors.error 
                    : theme.colors.outline,
                borderWidth: isFocused ? 2 : 1,
                backgroundColor: disabled ? theme.colors.surfaceVariant : 'transparent',
              }
            ]}
            onPress={handleInputPress}
            disabled={disabled}
            activeOpacity={0.7}
          >
            <Text
              style={[
                styles.inputText,
                {
                  color: displayText ? theme.colors.onSurface : theme.colors.onSurfaceVariant,
                }
              ]}
              numberOfLines={1}
            >
              {displayText || (label ? '' : placeholder)}
            </Text>
          </TouchableOpacity>
          
          <View style={styles.dropdownIcon}>
            <PhosphorIcon 
              name="arrow-down" 
              size={20} 
              color={theme.colors.onSurfaceVariant} 
            />
          </View>
        </View>
      </View>
      
      {error && (
        <Text style={[styles.errorText, { color: theme.colors.error }]}>
          {error}
        </Text>
      )}

      {/* Bottom Sheet */}
      <BottomSheet
        ref={bottomSheetRef}
        title={label || 'Select Category'}
        onClose={handleBottomSheetClose}
        snapPoints={['60%']}
      >
        {renderBottomSheetContent()}
      </BottomSheet>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.sm,
  },
  dropdownWrapper: {
    position: 'relative',
  },
  floatingLabel: {
    position: 'absolute',
    left: SPACING.md,
    top: 0,
    zIndex: 1,
    paddingHorizontal: 0,
    fontWeight: TYPOGRAPHY.fontWeight.medium,
  },
  inputContainer: {
    position: 'relative',
  },
  inputWrapper: {
    position: 'relative',
  },
  textInput: {
    backgroundColor: 'transparent',
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: TYPOGRAPHY.fontWeight.medium,
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 1,
    paddingHorizontal: SPACING.md,
    paddingTop: SPACING.lg,
    paddingBottom: SPACING.md,
    paddingRight: 48,
    minHeight: 60,
    justifyContent: 'center',
  },
  inputText: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: TYPOGRAPHY.fontWeight.medium,
  },
  dropdownIcon: {
    position: 'absolute',
    right: SPACING.md,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    width: 32,
  },
  bottomSheetContent: {
    flex: 1,
  },
  titleContainer: {
    paddingHorizontal: SPACING.lg,
    paddingTop: SPACING.md,
    paddingBottom: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  titleText: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: TYPOGRAPHY.fontWeight.semibold,
  },
  bottomSheetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingTop: SPACING.lg,
    paddingBottom: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  bottomSheetTitle: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: TYPOGRAPHY.fontWeight.semibold,
  },
  closeButton: {
    padding: SPACING.xs,
  },
  searchContainer: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  searchInputWrapper: {
    position: 'relative',
  },

  searchInput: {
    height: 48,
    borderWidth: 1,
    borderRadius: BORDER_RADIUS.md,
    paddingHorizontal: SPACING.md,
    paddingLeft: 40,
    paddingVertical: SPACING.sm,
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: TYPOGRAPHY.fontWeight.medium,
  },
  searchIcon: {
    position: 'absolute',
    left: SPACING.md,
    top: 15,
    zIndex: 2,
  },

  optionsScroll: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
    paddingTop: SPACING.sm,
  },
  optionsContentContainer: {
    paddingBottom: SPACING.lg,
  },
  optionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.xs,
  },
  optionText: {
    fontSize: TYPOGRAPHY.fontSize.md,
    flex: 1,
  },
  noResultsContainer: {
    padding: SPACING.xl,
    alignItems: 'center',
  },
  noResultsText: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontStyle: 'italic',
  },
  errorText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    marginTop: SPACING.xs,
    marginLeft: SPACING.xs,
  },

});

export default Dropdown; 