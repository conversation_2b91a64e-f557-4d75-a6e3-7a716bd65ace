// @ts-nocheck
/**
 * Card - Reusable card component
 * Provides consistent design for products, orders, and other items
 */

import React from 'react';
import { View, StyleSheet, TouchableOpacity, Image } from 'react-native';
import {
  Text,
  Surface,
  IconButton,
  Menu,
  Chip,
  Button,
} from 'react-native-paper';


import { useTheme } from '../../context/ThemeContext';
import { SPACING, BORDER_RADIUS, COMPONENT_SIZES, SHADOWS, getBorderColor, getThemedShadow } from '../../theme/theme';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

const Card = ({
  // Content props
  title,
  subtitle,
  description,
  price,
  status,
  image,
  icon,
  iconColor,
  iconBackgroundColor,

  // Status/Badge props
  statusColor,
  statusBackgroundColor,
  badge,
  badgeColor,

  // Action props
  onPress,
  onLongPress,
  actions = [],
  primaryAction,
  secondaryAction,

  // Menu props
  menuItems = [],
  menuVisible,
  onMenuToggle,
  onMenuDismiss,

  // Style props
  style,
  contentStyle,
  disabled = false,

  // Layout props
  layout = 'default', // 'default', 'compact', 'detailed'
  showImage = true,
  showIcon = true,
  showActions = true,
}) => {
  const theme = useTheme();

  const renderIcon = () => {
    if (!showIcon || !icon) return null;

    return (
      <View style={[
        styles.iconContainer,
        { backgroundColor: iconBackgroundColor || `${theme.colors.primary  }15` }
      ]}>
        <PhosphorIcon name={icon} size={COMPONENT_SIZES.icon.lg} color={iconColor || theme.colors.primary} />
      </View>
    );
  };

  const renderImage = () => {
    if (!showImage || !image) return null;

    return (
      <Image source={{ uri: image }} style={styles.cardImage} />
    );
  };

  const renderStatus = () => {
    if (!status) return null;

    return (
      <View style={[
        styles.statusBadge,
        { backgroundColor: statusBackgroundColor || theme.colors.primaryContainer }
      ]}>
        <Text variant="bodySmall" style={{
          color: statusColor || theme.colors.onPrimaryContainer,
          fontWeight: '600'
        }}>
          {status}
        </Text>
      </View>
    );
  };

  const renderBadge = () => {
    if (!badge) return null;

    return (
      <Chip
        mode="flat"
        textStyle={{ color: badgeColor || theme.colors.onSurfaceVariant }}
        style={styles.badge}
      >
        {badge}
      </Chip>
    );
  };

  const renderActions = () => {
    if (!showActions || (actions.length === 0 && menuItems.length === 0 && !primaryAction && !secondaryAction)) {
      return null;
    }

    return (
      <View style={styles.actionButtons}>
        {/* Custom actions */}
        {actions.map((action, index) => (
          <IconButton
            key={index}
            icon={action.icon}
            size={action.size || 20}
            iconColor={action.color || theme.colors.onSurfaceVariant}
            onPress={action.onPress}
            disabled={action.disabled}
            style={action.style}
          />
        ))}

        {/* Menu */}
        {menuItems.length > 0 && (
          <Menu
            visible={menuVisible}
            onDismiss={onMenuDismiss}
            anchor={
              <IconButton
                icon="dots-vertical"
                size={20}
                iconColor={theme.colors.onSurfaceVariant}
                onPress={onMenuToggle}
              />
            }
          >
            {menuItems.map((item, index) => (
              <Menu.Item
                key={index}
                onPress={item.onPress}
                title={item.title}
                leadingIcon={item.icon}
                disabled={item.disabled}
              />
            ))}
          </Menu>
        )}

        {/* Primary/Secondary actions */}
        {secondaryAction && (
          <Button
            mode="outlined"
            onPress={secondaryAction.onPress}
            disabled={secondaryAction.disabled}
            compact
            style={styles.actionButton}
          >
            {secondaryAction.title}
          </Button>
        )}

        {primaryAction && (
          <Button
            mode="contained"
            onPress={primaryAction.onPress}
            disabled={primaryAction.disabled}
            compact
            style={styles.primaryActionButton}
            buttonColor={primaryAction.color}
            textColor={primaryAction.textColor}
            icon={primaryAction.icon}
          >
            {primaryAction.title}
          </Button>
        )}
      </View>
    );
  };

  const renderContent = () => {
    return (
      <View style={[styles.cardContent, contentStyle]}>
        <View style={styles.cardHeader}>
          {renderIcon()}
          <View style={styles.cardInfo}>
            <View style={styles.titleRow}>
              <Text variant="titleMedium" style={{ fontWeight: '600', flex: 1 }}>
                {title}
              </Text>
              {renderStatus()}
              {renderBadge()}
            </View>

            {subtitle && (
              <Text variant="bodySmall" style={{
                color: theme.colors.onSurfaceVariant,
                marginTop: 2
              }}>
                {subtitle}
              </Text>
            )}

            {description && (
              <Text variant="bodySmall" style={{
                color: theme.colors.onSurfaceVariant,
                marginTop: 4
              }}>
                {description}
              </Text>
            )}

            {price && (
              <Text variant="titleLarge" style={{
                color: theme.colors.onSurface,
                fontWeight: '700',
                marginTop: 8
              }}>
                {typeof price === 'number' ? `৳${price.toFixed(2)}` : price}
              </Text>
            )}
          </View>
        </View>

        {renderActions()}
      </View>
    );
  };

  const CardWrapper = onPress ? TouchableOpacity : View;
  const cardProps = onPress ? {
    onPress,
    onLongPress,
    disabled,
    activeOpacity: 0.7,
    delayPressIn: 0,
    delayPressOut: 100,
  } : {};

  return (
    <CardWrapper {...cardProps}>
      <Surface style={[
        styles.card,
        {
          backgroundColor: theme.colors.surface,
          borderColor: getBorderColor(theme),
        },
        disabled && styles.disabledCard,
        style
      ]} elevation={0}>
        {renderImage()}
        {renderContent()}
      </Surface>
    </CardWrapper>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: SPACING.md,
    borderRadius: BORDER_RADIUS.xl,
    borderWidth: 1,
    overflow: 'hidden',
  },
  disabledCard: {
    opacity: 0.6,
  },
  cardImage: {
    width: '100%',
    height: 150,
    resizeMode: 'cover',
  },
  cardContent: {
    padding: SPACING.lg,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: SPACING.md,
  },
  iconContainer: {
    width: COMPONENT_SIZES.avatar.md,
    height: COMPONENT_SIZES.avatar.md,
    borderRadius: BORDER_RADIUS.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  cardInfo: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  statusBadge: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.xl,
    marginLeft: SPACING.sm,
  },
  badge: {
    marginLeft: SPACING.sm,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    gap: SPACING.sm,
    marginTop: SPACING.sm,
  },
  actionButton: {
    borderRadius: BORDER_RADIUS.md,
  },
  primaryActionButton: {
    borderRadius: BORDER_RADIUS.md,
  },
});

export default Card;
