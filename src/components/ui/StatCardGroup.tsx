import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';

import { SPACING } from '../../theme/theme';
import { StatCard, StatCardGroupProps } from '../../types';
import InfoCard from '../cards/InfoCard';

const StatCardGroup: React.FC<StatCardGroupProps> = ({
  title,
  cards = [],
  columns = 2,
  showTitle = true,
  titleStyle = {},
  containerStyle = {},
  onCardPress
}) => {
  const handleCardPress = (card: StatCard, _index: number): void => {
    if (onCardPress) {
      onCardPress(card);
    } else if (card.onPress) {
      card.onPress();
    }
  };

  const isOddCount = cards.length % columns !== 0;
  const lastCardIndex = cards.length - 1;

  return (
    <View style={[styles.container, containerStyle]}>
      {showTitle && title && (
        <Text variant="titleLarge" style={[styles.title, titleStyle as any]}>
          {title}
        </Text>
      )}

      <View style={[styles.grid, { marginHorizontal: -2, marginVertical: 0 }]}>
        {cards.map((card, index) => {
          const isLastCard = index === lastCardIndex;
          const shouldSpanFullWidth = isOddCount && isLastCard;

          return (
            <View
              key={card.key || index}
              style={[
                styles.cardWrapper,
                {
                  width: shouldSpanFullWidth ? '100%' : `${100 / columns}%`,
                  paddingHorizontal: 2,
                  paddingVertical: 0
                }
              ]}
            >
              <InfoCard
                type="stat"
                title={card.value.toString()} // Show data/numbers as title
                value={card.value}
                subtitle={card.title} // Show card name as subtitle
                description=""
                icon={card.icon}
                iconColor={card.iconColor || card.color}
                iconBackground=""
                elevation={card.elevation || 1}
                onPress={() => handleCardPress(card, index)}
                style={{ marginHorizontal: 2, marginVertical: -3 }}
                data={null}
                growth={null}
                status=""
                statusColor=""
                {...card.props}
              />
            </View>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.md,
  },
  title: {
    fontWeight: '700',
    marginBottom: SPACING.md,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  cardWrapper: {
    marginBottom: SPACING.xs,
  },
});

export default StatCardGroup;
