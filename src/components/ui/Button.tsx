import React from 'react';
import { TouchableOpacity, View, ActivityIndicator, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { Text } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';


// Helper to check if a color is dark (hex only)
function isColorDark(hex: string): boolean {
  if (!hex || typeof hex !== 'string' || !hex.startsWith('#')) return true; // fallback: treat as dark
  let c = hex.replace('#', '');
  if (c.length === 3) c = c.split('').map(x => x + x).join('');
  if (c.length !== 6) return true; // fallback: treat as dark
  const r = parseInt(c.substr(0, 2), 16);
  const g = parseInt(c.substr(2, 2), 16);
  const b = parseInt(c.substr(4, 2), 16);
  return (0.299 * r + 0.587 * g + 0.114 * b) < 186;
}

type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger' | 'text';
type ButtonSize = 'xs' | 'sm' | 'md' | 'lg' | 'fullWidth';
type IconPosition = 'left' | 'right';

interface ButtonStyle {
  backgroundColor?: string;
  borderColor?: string;
  paddingVertical?: number;
  paddingHorizontal?: number;
  fontSize?: number;
  borderRadius?: number;
  width?: '100%' | number;
}

interface UnifiedButtonProps {
  variant?: ButtonVariant;
  size?: ButtonSize;
  icon?: string;
  iconPosition?: IconPosition;
  loading?: boolean;
  disabled?: boolean;
  rounded?: boolean;
  onPress?: () => void;
  children?: React.ReactNode;
  style?: ViewStyle;
  textStyle?: TextStyle;
  textColor?: string;
  accessibilityLabel?: string;
  [key: string]: any;
}

const VARIANT_STYLES = (theme: any): Record<ButtonVariant, ButtonStyle> => ({
  primary: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  secondary: {
    backgroundColor: theme.colors.secondary,
    borderColor: theme.colors.secondary,
  },
  outline: {
    backgroundColor: 'transparent',
    borderColor: theme.colors.primary,
  },
  ghost: {
    backgroundColor: 'transparent',
    borderColor: 'transparent',
  },
  danger: {
    backgroundColor: '#ef4444',
    borderColor: '#ef4444',
  },
  text: {
    backgroundColor: 'transparent',
    borderColor: 'transparent',
  },
});

const SIZE_STYLES = (theme: any): Record<ButtonSize, ButtonStyle> => ({
  xs: {
    paddingVertical: 2,
    paddingHorizontal: 8,
    fontSize: 12,
    borderRadius: 6, // minimal rounded for extra small buttons
  },
  sm: {
    paddingVertical: 4,
    paddingHorizontal: 10,
    fontSize: 13,
    borderRadius: 8, // semi-rounded for small buttons
  },
  md: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    fontSize: 16,
    borderRadius: theme.borderRadius.lg,
  },
  lg: {
    paddingVertical: 16,
    paddingHorizontal: 28,
    fontSize: 18,
    borderRadius: theme.borderRadius.lg,
  },
  fullWidth: {
    width: '100%',
  },
});

const Button: React.FC<UnifiedButtonProps> = ({
  variant = 'primary',
  size = 'md',
  icon,
  iconPosition = 'left',
  loading = false,
  disabled = false,
  rounded = true,
  onPress,
  children,
  style,
  textStyle,
  textColor: textColorProp,
  accessibilityLabel,
  ...props
}) => {
  const theme = useTheme();
  const variantStyles = VARIANT_STYLES(theme)[variant] || VARIANT_STYLES(theme).primary;
  const sizeStyles = SIZE_STYLES(theme)[size] || SIZE_STYLES(theme).md;
  const isFullWidth = size === 'fullWidth';
  const borderRadius = rounded ? 9999 : 8;
  let bgColor = variantStyles.backgroundColor || '#222';
  // Only use hex for luminance check, fallback to #222 if not
  if (!bgColor.startsWith('#')) bgColor = '#222';
  const darkBg = isColorDark(bgColor);
  // Use manual override if provided, else auto
  const textColor = textColorProp || (variant === 'text' ? theme.colors.primary : (variant === 'primary' ? '#111' : (darkBg ? '#fff' : '#111')));
  const iconColor = textColor;

  // Icon rendering: support string (icon name), function, or React element
  const renderIcon = (iconProp: any, color: string, iconSide: string): React.ReactElement | null => {
    if (!iconProp) return null;
    if (typeof iconProp === 'function') {
      return iconProp(color);
    }
    if (typeof iconProp === 'string') {
      return <PhosphorIcon name={iconProp as any} size={20} color={color} style={iconSide === 'left' ? { marginRight: children ? 8 : 0 } : { marginLeft: children ? 8 : 0 }} />;
    }
    if (React.isValidElement(iconProp)) {
      return React.cloneElement(iconProp, {
        style: [(iconProp.props as any)?.style, iconSide === 'left' ? { marginRight: children ? 8 : 0 } : { marginLeft: children ? 8 : 0 }],
      } as any);
    }
    return null;
  };

  return (
    <TouchableOpacity
      style={[
        styles.button,
        {
          backgroundColor: variantStyles.backgroundColor,
          borderColor: variantStyles.borderColor,
          borderWidth: variant === 'outline' ? 2 : 0,
          borderRadius,
          opacity: disabled ? 0.6 : 1,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          paddingVertical: sizeStyles.paddingVertical,
          paddingHorizontal: sizeStyles.paddingHorizontal,
          // Remove shadow for text buttons
          shadowOpacity: variant === 'text' ? 0 : 0.08,
          elevation: variant === 'text' ? 0 : 2,
        },
        isFullWidth && SIZE_STYLES(theme).fullWidth,
        style,
      ]}
      activeOpacity={0.8}
      onPress={disabled || loading ? undefined : onPress}
      disabled={disabled || loading}
      accessibilityRole="button"
      accessibilityLabel={accessibilityLabel || (typeof children === 'string' ? children : undefined)}
      {...props}
    >
      {loading ? (
        <ActivityIndicator color={iconColor} style={{ marginRight: children ? 8 : 0 }} />
      ) : icon && iconPosition === 'left' ? (
        renderIcon(icon, iconColor, 'left')
      ) : null}
      {children ? (
        <Text style={[{
          color: textColor,
          fontSize: variant === 'primary' ? Math.max((sizeStyles.fontSize || 16) - 2, 12) : (sizeStyles.fontSize || 16),
          fontWeight: 'bold',
        }, textStyle]}>{children}</Text>
      ) : null}
      {icon && iconPosition === 'right' && !loading ? (
        renderIcon(icon, iconColor, 'right')
      ) : null}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    marginVertical: 4,
    minHeight: 44,
    minWidth: 44,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 4,
    shadowOffset: { width: 0, height: 2 },
    elevation: 2,
  },
});

export default Button; 