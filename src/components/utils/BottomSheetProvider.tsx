// @ts-nocheck
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import React from 'react';
import { View, StyleSheet } from 'react-native';

const BottomSheetProvider = ({ children }) => {
  return (
    <View style={styles.container}>
      <BottomSheetModalProvider>
        {children}
      </BottomSheetModalProvider>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
});

export default BottomSheetProvider;
