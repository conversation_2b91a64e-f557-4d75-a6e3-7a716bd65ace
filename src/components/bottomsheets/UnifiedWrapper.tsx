import React, { forwardRef, useImperativeHandle, useRef } from 'react';

import { useTheme } from '../../context/ThemeContext';
import BottomSheet from './BottomSheet';
import BottomSheetContentFactory from './BottomSheetContentFactory';

interface UnifiedWrapperProps {
  type: string;
  title?: string;
  data?: any;
  mode?: 'add' | 'edit' | 'view';
  onSave?: (data: any) => void;
  onAction?: (actionId: string) => void;
  onClose?: () => void;
}

export interface UnifiedWrapperRef {
  open: () => void;
  close: () => void;
}

const UnifiedWrapper = forwardRef<UnifiedWrapperRef, UnifiedWrapperProps>(
  ({ type, title, data = {}, mode = 'view', onSave, onAction, onClose }, ref) => {
    const theme = useTheme();
    const bottomSheetRef = useRef(null);

    useImperativeHandle(ref, () => ({
      open: () => bottomSheetRef.current?.expand(),
      close: () => bottomSheetRef.current?.close(),
    }));

    const getSnapPoints = () => {
      switch (type) {
        case 'quick-actions':
          return ['40%'];
        case 'order-details':
          return ['60%', '90%'];
        case 'security-settings':
          return ['50%', '70%'];
        case 'edit-profile':
          return ['60%', '90%'];
        case 'add-customer':
        case 'edit-customer':
          return ['60%', '90%'];
        case 'add-product':
        case 'edit-product':
          return ['70%', '95%'];
        case 'outlet':
          return ['60%', '90%'];
        case 'pdf-invoice':
          return ['80%', '95%'];
        case 'payment-analytics':
        case 'profit-loss':
        case 'tax-summary':
          return ['85%', '95%'];
        default:
          return ['60%', '90%'];
      }
    };

    const getTitle = () => {
      if (title) return title;

      switch (type) {
        case 'quick-actions':
          return 'Quick Actions';
        case 'order-details':
          return 'Order Details';
        case 'security-settings':
          return 'Change Password';
        case 'edit-profile':
          return 'Edit Profile';
        case 'add-customer':
          return 'Add Customer';
        case 'edit-customer':
          return 'Edit Customer';
        case 'add-product':
          return 'Add Product';
        case 'edit-product':
          return 'Edit Product';
        case 'outlet':
          return 'Outlet Management';
        case 'pdf-invoice':
          return 'Invoice Preview';
        case 'payment-analytics':
          return 'Payment Analytics';
        case 'profit-loss':
          return 'Profit & Loss Report';
        case 'tax-summary':
          return 'Tax Summary';
        default:
          return 'Bottom Sheet';
      }
    };

    const renderContent = () => {
        return (
            <BottomSheetContentFactory
                type={type}
                data={data}
                mode={mode}
                onSave={onSave}
                onAction={onAction}
            />
        );
    };

    return (
      <BottomSheet
        ref={bottomSheetRef}
        title={getTitle()}
        snapPoints={getSnapPoints()}
        onClose={onClose}
      >
        {renderContent()}
      </BottomSheet>
    );
  }
);

UnifiedWrapper.displayName = 'UnifiedWrapper';

export default UnifiedWrapper; 