import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { ScrollView } from 'react-native';
import { Button, Text } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import BottomSheet from './BottomSheet';

interface UnifiedWrapperProps {
  type: string;
  title?: string;
  data?: any;
  mode?: 'add' | 'edit' | 'view';
  onSave?: (data: any) => void;
  onAction?: (actionId: string) => void;
  onClose?: () => void;
}

export interface UnifiedWrapperRef {
  open: () => void;
  close: () => void;
}

const UnifiedWrapper = forwardRef<UnifiedWrapperRef, UnifiedWrapperProps>(
  ({ type, title, data = {}, mode = 'view', onSave, onAction, onClose }, ref) => {
    const theme = useTheme();
    const bottomSheetRef = useRef(null);

    useImperativeHandle(ref, () => ({
      open: () => bottomSheetRef.current?.expand(),
      close: () => bottomSheetRef.current?.close(),
    }));

    const getSnapPoints = () => {
      switch (type) {
        case 'quick-actions':
          return ['40%'];
        case 'order-details':
          return ['60%', '90%'];
        default:
          return ['60%', '90%'];
      }
    };

    const renderContent = () => {
      switch (type) {
        case 'add-customer':
        case 'edit-customer':
          return (
            <ScrollView style={{ flex: 1 }}>
              <Text>Customer Form - {mode}</Text>
              <Button mode="contained" onPress={() => onSave?.(data)}>
                {mode === 'edit' ? 'Update' : 'Save'}
              </Button>
            </ScrollView>
          );

        case 'quick-actions':
          return (
            <ScrollView style={{ flex: 1 }}>
              <Button mode="outlined" onPress={() => onAction?.('add-product')}>
                Add Product
              </Button>
              <Button mode="outlined" onPress={() => onAction?.('add-order')}>
                New Order
              </Button>
              <Button mode="outlined" onPress={() => onAction?.('add-customer')}>
                Add Customer
              </Button>
            </ScrollView>
          );

        case 'order-details':
          return (
            <ScrollView style={{ flex: 1 }}>
              <Text variant="titleMedium">Order Details</Text>
              <Text>Customer: {data.customer || 'N/A'}</Text>
              <Text>Total: ${data.total || '0.00'}</Text>
            </ScrollView>
          );

        default:
          return (
            <ScrollView style={{ flex: 1 }}>
              <Text>Content for {type}</Text>
            </ScrollView>
          );
      }
    };

    return (
      <BottomSheet
        ref={bottomSheetRef}
        title={title || type}
        snapPoints={getSnapPoints()}
        onClose={onClose}
      >
        {renderContent()}
      </BottomSheet>
    );
  }
);

UnifiedWrapper.displayName = 'UnifiedWrapper';

export default UnifiedWrapper; 