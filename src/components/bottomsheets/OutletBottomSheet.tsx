/**
 * OutletBottomSheet - Unified Outlet Form
 * Redesigned with unified design inspired by QuickActionsBottomSheet
 */

import BottomSheet, { BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import React, { useState, forwardRef, useImperativeHandle, useCallback, useRef } from 'react';
import { ScrollView, View, StyleSheet, Alert } from 'react-native';
import {
  Text,
  Portal,
  Surface,
  Menu,
  Button as PaperButton,
} from 'react-native-paper';

import { useData } from '../../context/DataContext';
import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY } from '../../theme/theme';
import { Outlet, OutletFormData } from '../../types';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';
import Button from '../ui/Button';
import TextInput from '../ui/TextInput';

interface OutletBottomSheetProps {
  onSave?: (outlet: Outlet) => void;
  onCancel?: () => void;
  outlet?: Outlet | null;
  mode?: 'add' | 'edit';
}

const OutletBottomSheet = forwardRef<any, OutletBottomSheetProps>(({
  onSave,
  onCancel,
  outlet = null,
  mode = 'add'
}, ref) => {
  const theme = useTheme();
  const { state, actions } = useData();

  const isEditing = mode === 'edit' && outlet !== null;
  const editingOutlet = isEditing ? outlet : null;

  const [outletData, setOutletData] = useState<OutletFormData>({
    name: editingOutlet?.name || '',
    address: editingOutlet?.address || '',
    phone: editingOutlet?.phone || '',
    email: editingOutlet?.email || '',
    managerName: editingOutlet?.managerName || '',
    operatingHours: editingOutlet?.operatingHours || {
      monday: '09:00-18:00',
      tuesday: '09:00-18:00',
      wednesday: '09:00-18:00',
      thursday: '09:00-18:00',
      friday: '09:00-18:00',
      saturday: '09:00-18:00',
      sunday: 'Closed',
    },
    settings: {
      taxRate: editingOutlet?.settings?.taxRate || 8,
      currency: editingOutlet?.settings?.currency || 'BDT',
      timezone: editingOutlet?.settings?.timezone || 'Asia/Dhaka',
      defaultMeasurementUnit: editingOutlet?.settings?.defaultMeasurementUnit || 'cm',
    },
  });



  const [errors, setErrors] = useState<Record<string, string>>({});
  const [managerMenuVisible, setManagerMenuVisible] = useState(false);

  const bottomSheetRef = useRef<any>(null);

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    present: (_config: any) => {
      LoggingService.debug('OutletBottomSheet present method called', 'OUTLET_BOTTOM_SHEET');
      // Reset form data when presenting
      setOutletData({
        name: editingOutlet?.name || '',
        address: editingOutlet?.address || '',
        phone: editingOutlet?.phone || '',
        email: editingOutlet?.email || '',
        managerName: editingOutlet?.managerName || '',
        operatingHours: editingOutlet?.operatingHours || {
          monday: '09:00-18:00',
          tuesday: '09:00-18:00',
          wednesday: '09:00-18:00',
          thursday: '09:00-18:00',
          friday: '09:00-18:00',
          saturday: '09:00-18:00',
          sunday: 'Closed',
        },
        settings: {
          taxRate: editingOutlet?.settings?.taxRate || 8,
          currency: editingOutlet?.settings?.currency || 'BDT',
          timezone: editingOutlet?.settings?.timezone || 'Asia/Dhaka',
          defaultMeasurementUnit: editingOutlet?.settings?.defaultMeasurementUnit || 'cm',
        },
      });
      setErrors({});
      // Expand the bottom sheet
      LoggingService.debug('bottomSheetRef.current:', 'OUTLET_BOTTOM_SHEET', bottomSheetRef.current);
      bottomSheetRef.current?.expand();
    },
    dismiss: () => {
      LoggingService.debug('OutletBottomSheet dismiss method called', 'OUTLET_BOTTOM_SHEET');
      // Close the bottom sheet
      bottomSheetRef.current?.close();
    },
  }));

  const handleSheetChanges = useCallback((index: number) => {
    LoggingService.debug('Sheet index changed to:', 'OUTLET_BOTTOM_SHEET', index);
    if (index === -1) {
      onCancel?.();
    }
  }, [onCancel]);

  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="close"
      />
    ),
    []
  );

  const validateRequired = (value: string, fieldName: string): string | null => {
    if (!value || value.trim().length === 0) {
      return `${fieldName} is required`;
    }
    return null;
  };

  const validateEmail = (email: string): string | null => {
    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return 'Please enter a valid email address';
    }
    return null;
  };

  const validatePhone = (phone: string): string | null => {
    if (phone && !/^[+]?[1-9][\d]{0,15}$/.test(phone.replace(/\s/g, ''))) {
      return 'Please enter a valid phone number';
    }
    return null;
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required field validations
    const nameError = validateRequired(outletData.name, 'Outlet name');
    if (nameError) newErrors.name = nameError;

    const addressError = validateRequired(outletData.address, 'Address');
    if (addressError) newErrors.address = addressError;

    const managerError = validateRequired(outletData.managerName || '', 'Manager name');
    if (managerError) newErrors.managerName = managerError;

    // Optional field validations
    if (outletData.email) {
      const emailError = validateEmail(outletData.email);
      if (emailError) newErrors.email = emailError;
    }

    if (outletData.phone) {
      const phoneError = validatePhone(outletData.phone);
      if (phoneError) newErrors.phone = phoneError;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async (): Promise<void> => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please fix the errors before saving.');
      return;
    }

    try {
      const outletPayload = {
        name: outletData.name,
        address: outletData.address,
        phone: outletData.phone,
        email: outletData.email,
        managerName: outletData.managerName,
        isActive: true, // Default to active
        managerId: editingOutlet?.managerId || 'default-manager-id', // This should be the actual manager ID
        operatingHours: outletData.operatingHours,
        settings: {
          taxRate: Number(outletData.settings.taxRate),
          currency: outletData.settings.currency,
          timezone: outletData.settings.timezone,
          defaultMeasurementUnit: outletData.settings.defaultMeasurementUnit,
        },
      };

      let savedOutlet: Outlet;

      if (isEditing && editingOutlet) {
        savedOutlet = await actions.updateOutlet(editingOutlet.id, outletPayload);
        LoggingService.info('Outlet updated successfully', 'OUTLET_FORM', { outletId: savedOutlet.id });
      } else {
        savedOutlet = await actions.createOutlet(outletPayload);
        LoggingService.info('Outlet created successfully', 'OUTLET_FORM', { outletId: savedOutlet.id });
      }

      onSave?.(savedOutlet);
      bottomSheetRef.current?.close();
    } catch (error) {
      LoggingService.error('Failed to save outlet', 'OUTLET_FORM', error instanceof Error ? error : new Error(String(error)));
      Alert.alert('Error', 'Failed to save outlet. Please try again.');
    }
  };

  const handleInputChange = (field: keyof OutletFormData, value: string | boolean): void => {
    setOutletData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <Portal>
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={['60%', '90%']}
        onChange={handleSheetChanges}
        backdropComponent={renderBackdrop}
        keyboardBehavior="interactive"
        keyboardBlurBehavior="restore"
        android_keyboardInputMode="adjustResize"
        enablePanDownToClose={true}
        backgroundStyle={{ backgroundColor: theme.colors.surface }}
        handleIndicatorStyle={{ backgroundColor: theme.colors.onSurfaceVariant }}
        style={styles.bottomSheet}
      >
        <BottomSheetView style={styles.container}>
          {/* Header */}
          <View style={[styles.header, { borderBottomColor: `${theme.colors.outline}20` }]}>
            <View style={styles.headerLeft}>
              <View style={[styles.iconContainer, { backgroundColor: `${theme.colors.primary}15` }]}>
                <PhosphorIcon name="storefront" size={24} color={theme.colors.primary} />
              </View>
              <View style={styles.titleContainer}>
                <Text style={[styles.title, { color: theme.colors.onSurface }]}>
                  {isEditing ? 'Edit Outlet' : 'Add Outlet'}
                </Text>
                <Text style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
                  {isEditing ? 'Update outlet information' : 'Create a new outlet'}
                </Text>
              </View>
            </View>
          </View>

          {/* Content */}
          <ScrollView 
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
          >
            <TextInput
              label="Outlet Name"
              value={outletData.name}
              onChangeText={(value) => handleInputChange('name', value)}
              error={errors.name}
              placeholder="Enter outlet name"
              style={styles.input}
            />

            <TextInput
              label="Address"
              value={outletData.address}
              onChangeText={(value) => handleInputChange('address', value)}
              error={errors.address}
              placeholder="Enter outlet address"
              multiline
              numberOfLines={2}
              style={styles.input}
            />

            <TextInput
              label="Phone Number"
              value={outletData.phone || ''}
              onChangeText={(value) => handleInputChange('phone', value)}
              error={errors.phone}
              placeholder="Enter phone number"
              keyboardType="phone-pad"
              style={styles.input}
            />

            <TextInput
              label="Email"
              value={outletData.email || ''}
              onChangeText={(value) => handleInputChange('email', value)}
              error={errors.email}
              placeholder="Enter email address"
              keyboardType="email-address"
              autoCapitalize="none"
              style={styles.input}
            />

            <View style={styles.input}>
              <Text style={[styles.label, { color: theme.colors.onSurface, marginBottom: 8 }]}>
                Manager
              </Text>
              <Menu
                visible={managerMenuVisible}
                onDismiss={() => setManagerMenuVisible(false)}
                anchor={
                  <Surface style={[styles.dropdownButton, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
                    <PaperButton
                      mode="text"
                      onPress={() => setManagerMenuVisible(true)}
                      contentStyle={styles.dropdownContent}
                      labelStyle={{ color: theme.colors.onSurface }}
                      icon="chevron-down"
                      compact
                    >
                      {outletData.managerName || 'Select Manager'}
                    </PaperButton>
                  </Surface>
                }
              >
                {state.staff
                  .filter(staff => staff.role === 'manager' || staff.role === 'assistant')
                  .map((staff) => (
                    <Menu.Item
                      key={staff.id}
                      onPress={() => {
                        handleInputChange('managerName', staff.name);
                        setManagerMenuVisible(false);
                      }}
                      title={`${staff.name} (${staff.role})`}
                    />
                  ))}
                {state.staff.filter(staff => staff.role === 'manager' || staff.role === 'assistant').length === 0 && (
                  <Menu.Item
                    onPress={() => {
                      // Navigate to staff creation or show message
                      Alert.alert('No Managers', 'No managers found. Please create a staff member with manager role first.');
                      setManagerMenuVisible(false);
                    }}
                    title="No managers available - Create a manager first"
                  />
                )}
              </Menu>
              {errors.managerName && (
                <Text style={[styles.errorText, { color: theme.colors.error, fontSize: 12, marginTop: 4 }]}>
                  {errors.managerName}
                </Text>
              )}
            </View>



            <View style={styles.buttonContainer}>
              <Button
                onPress={handleSave}
                style={styles.saveButton}
                textStyle={styles.saveButtonText}
              >
                {isEditing ? 'Update Outlet' : 'Create Outlet'}
              </Button>
            </View>
          </ScrollView>
        </BottomSheetView>
      </BottomSheet>
    </Portal>
  );
});

const styles = StyleSheet.create({
  bottomSheet: {
    zIndex: 1000,
    elevation: 1000,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: BORDER_RADIUS.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: TYPOGRAPHY.fontWeight.bold,
    marginBottom: 2,
  },
  subtitle: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    lineHeight: TYPOGRAPHY.lineHeight.normal * TYPOGRAPHY.fontSize.sm,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: SPACING.lg,
    paddingBottom: SPACING.xl,
  },
  input: {
    marginBottom: SPACING.md,
  },
  switchContainer: {
    padding: SPACING.lg,
    borderRadius: BORDER_RADIUS.lg,
    marginBottom: SPACING.lg,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  switchLabel: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  switchText: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: TYPOGRAPHY.fontWeight.medium,
    marginLeft: SPACING.sm,
  },
  switchDescription: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    lineHeight: TYPOGRAPHY.lineHeight.normal * TYPOGRAPHY.fontSize.sm,
  },
  buttonContainer: {
    marginTop: SPACING.lg,
  },
  saveButton: {
    backgroundColor: '#2563EB',
    borderRadius: BORDER_RADIUS.lg,
    paddingVertical: SPACING.md,
  },
  saveButtonText: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: TYPOGRAPHY.fontWeight.semibold,
    color: '#FFFFFF',
  },
  label: {
    fontSize: TYPOGRAPHY.fontSize.md,
    fontWeight: TYPOGRAPHY.fontWeight.medium,
  },
  dropdownButton: {
    borderRadius: BORDER_RADIUS.md,
    overflow: 'hidden',
  },
  dropdownContent: {
    paddingHorizontal: SPACING.md,
  },
  errorText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
  },
});

OutletBottomSheet.displayName = 'OutletBottomSheet';

export default OutletBottomSheet; 