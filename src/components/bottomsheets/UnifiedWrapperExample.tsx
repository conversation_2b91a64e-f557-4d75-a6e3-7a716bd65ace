import React, { useRef } from 'react';
import { View } from 'react-native';
import { Button, Text } from 'react-native-paper';

import UnifiedWrapper, { UnifiedWrapperRef } from './UnifiedWrapper';

/**
 * Example component showing how to use UnifiedWrapper
 * This replaces multiple individual bottom sheet components
 */
const UnifiedWrapperExample: React.FC = () => {
  const bottomSheetRef = useRef<UnifiedWrapperRef>(null);

  const handleSaveCustomer = (data: any) => {
    console.log('Saving customer:', data);
    // Your save logic here
  };

  const handleQuickAction = (actionId: string) => {
    console.log('Quick action:', actionId);
    // Handle different actions
    switch (actionId) {
      case 'add-product':
        // Open product form
        break;
      case 'add-order':
        // Open order form
        break;
      case 'add-customer':
        // Open customer form
        break;
    }
  };

  const handleClose = () => {
    console.log('Bottom sheet closed');
  };

  return (
    <View style={{ padding: 16 }}>
      <Text variant="titleLarge" style={{ marginBottom: 16 }}>
        Unified Bottom Sheet Examples
      </Text>

      {/* Quick Actions Example */}
      <Button
        mode="contained"
        onPress={() => {
          // This replaces QuickActionsBottomSheet
          bottomSheetRef.current?.open();
        }}
        style={{ marginBottom: 8 }}
      >
        Show Quick Actions
      </Button>

      {/* Customer Form Example */}
      <Button
        mode="outlined"
        onPress={() => {
          // This replaces AddCustomerBottomSheet
          bottomSheetRef.current?.open();
        }}
        style={{ marginBottom: 8 }}
      >
        Add Customer
      </Button>

      {/* Order Details Example */}
      <Button
        mode="outlined"
        onPress={() => {
          // This replaces OrderDetailsBottomSheet
          bottomSheetRef.current?.open();
        }}
        style={{ marginBottom: 8 }}
      >
        View Order Details
      </Button>

      {/* Unified Bottom Sheet */}
      <UnifiedWrapper
        ref={bottomSheetRef}
        type="quick-actions" // Change this to test different types
        title="Quick Actions"
        mode="view"
        onSave={handleSaveCustomer}
        onAction={handleQuickAction}
        onClose={handleClose}
        data={{
          // Pass any data needed for the specific type
          customer: 'John Doe',
          total: 150.00,
          status: 'Pending'
        }}
      />
    </View>
  );
};

export default UnifiedWrapperExample; 