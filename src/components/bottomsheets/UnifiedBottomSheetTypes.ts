import { ReactNode } from 'react';

// Base bottom sheet state
export interface BottomSheetState {
  isVisible: boolean;
  type: BottomSheetType | null;
  title: string;
  snapPoints: string[];
  data: any;
  mode: 'add' | 'edit' | 'view';
}

// All possible bottom sheet types
export type BottomSheetType = 
  | 'add-customer'
  | 'edit-customer'
  | 'add-product'
  | 'edit-product'
  | 'add-order'
  | 'edit-order'
  | 'order-details'
  | 'quick-actions'
  | 'payment-analytics'
  | 'profit-loss'
  | 'tax-summary'
  | 'cash-reconciliation'
  | 'expense'
  | 'edit-profile'
  | 'security-settings'
  | 'outlet'
  | 'pdf-invoice';

// Action types for the reducer
export type BottomSheetAction = 
  | { type: 'OPEN'; payload: OpenBottomSheetPayload }
  | { type: 'CLOSE' }
  | { type: 'UPDATE_DATA'; payload: any }
  | { type: 'SET_MODE'; payload: 'add' | 'edit' | 'view' };

// Payload for opening a bottom sheet
export interface OpenBottomSheetPayload {
  type: BottomSheetType;
  title?: string;
  data?: any;
  mode?: 'add' | 'edit' | 'view';
  snapPoints?: string[];
}

// Configuration for each bottom sheet type
export interface BottomSheetConfig {
  title: string;
  snapPoints: string[];
  component: ReactNode;
  defaultMode: 'add' | 'edit' | 'view';
}

// Callback types
export interface BottomSheetCallbacks {
  onSave?: (data: any) => void;
  onClose?: () => void;
  onAction?: (actionId: string) => void;
  onDelete?: (id: string) => void;
}

// Form field configuration
export interface FormField {
  name: string;
  key: string;
  label: string;
  type: 'text' | 'email' | 'phone' | 'number' | 'select' | 'textarea' | 'date' | 'image';
  value: any;
  onChange: (value: any) => void;
  required?: boolean;
  validation?: (value: any) => string;
  options?: Array<{ label: string; value: any }>;
  placeholder?: string;
}

// Action item for quick actions
export interface ActionItem {
  id: string;
  title: string;
  icon: string;
  color: string;
  backgroundColor: string;
  onPress?: () => void;
} 