// @ts-nocheck
/**
 * ExpenseBottomSheet - Unified Expense Form
 * Redesigned with unified design inspired by QuickActionsBottomSheet
 */

import BottomSheet, { BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import React, { useState, useCallback, forwardRef, useImperativeHandle, useMemo } from 'react';
import { View, StyleSheet, Alert, ScrollView, Keyboard } from 'react-native';
import {
  Text,
  IconButton,
  Portal,
} from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';


// import { Picker } from '@react-native-picker/picker'; // Will be implemented later
// import DateTimePicker from '@react-native-community/datetimepicker'; // Will be implemented later
import { useFinancial } from '../../context/FinancialContext';
import { useTheme } from '../../context/ThemeContext';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';
import { FINANCIAL_CONFIG } from '../config/constants';
import FormSection from '../forms/FormSection';
import Button from '../ui/Button';


// Add validation helpers at the top
const validateNumber = (val) => !isNaN(val) && Number(val) > 0;

const ExpenseBottomSheet = forwardRef((props, ref) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const { addExpense, updateExpense } = useFinancial();
  const bottomSheetRef = React.useRef(null);

  const snapPoints = useMemo(() => ['90%'], []);

  const [mode, setMode] = useState('add'); // 'add' or 'edit'
  const [expense, setExpense] = useState({
    description: '',
    amount: '',
    category: FINANCIAL_CONFIG.EXPENSE_CATEGORIES[0],
    paymentMethod: FINANCIAL_CONFIG.PAYMENT_METHODS[0],
    date: new Date().toISOString().split('T')[0],
    notes: '',
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  const handleSheetChanges = useCallback((index) => {
    if (index === -1) {
      Keyboard.dismiss();
      setExpense({
        description: '',
        amount: '',
        category: FINANCIAL_CONFIG.EXPENSE_CATEGORIES[0],
        paymentMethod: FINANCIAL_CONFIG.PAYMENT_METHODS[0],
        date: new Date().toISOString().split('T')[0],
        notes: '',
      });
      setErrors({});
    }
  }, []);

  const renderBackdrop = useCallback(
    (props) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="close"
      />
    ),
    []
  );

  useImperativeHandle(ref, () => ({
    expand: (expenseData = null) => {
      if (expenseData) {
        setMode('edit');
        setExpense(expenseData);
      } else {
        setMode('add');
        setExpense({
          description: '',
          amount: '',
          category: FINANCIAL_CONFIG.EXPENSE_CATEGORIES[0],
          paymentMethod: FINANCIAL_CONFIG.PAYMENT_METHODS[0],
          date: new Date().toISOString().split('T')[0],
          notes: '',
        });
      }
      setErrors({});
      bottomSheetRef.current?.expand();
    },
    close: () => {
      bottomSheetRef.current?.close();
    },
  }));

  const validateExpense = useCallback(() => {
    const newErrors = {};

    // Description validation
    if (!expense.description.trim()) {
      newErrors.description = 'Description is required';
    } else if (expense.description.length < 3) {
      newErrors.description = 'Description must be at least 3 characters';
    }

    // Amount validation
    if (!expense.amount) {
      newErrors.amount = 'Amount is required';
    } else {
      const amount = parseFloat(expense.amount);
      if (isNaN(amount) || amount <= 0) {
        newErrors.amount = 'Amount must be a positive number';
      } else if (amount > 10000) {
        newErrors.amount = 'Amount cannot exceed $10,000';
      }
    }

    // Date validation
    if (!expense.date) {
      newErrors.date = 'Date is required';
    } else {
      const expenseDate = new Date(expense.date);
      const today = new Date();
      if (expenseDate > today) {
        newErrors.date = 'Date cannot be in the future';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [expense]);

  const handleSave = useCallback(async () => {
    if (!validateExpense()) {
      return;
    }

    setLoading(true);
    try {
      const expenseData = {
        ...expense,
        amount: parseFloat(expense.amount),
      };

      if (mode === 'add') {
        await addExpense(expenseData);
        Alert.alert('Success', 'Expense added successfully!');
      } else {
        await updateExpense(expense.id, expenseData);
        Alert.alert('Success', 'Expense updated successfully!');
      }

      bottomSheetRef.current?.close();
    } catch (error) {
      Alert.alert('Error', error.message || 'Failed to save expense');
    } finally {
      setLoading(false);
    }
  }, [expense, mode, validateExpense, addExpense, updateExpense]);



  const getTotalAmount = () => {
    return expense.amount ? `$${parseFloat(expense.amount || 0).toFixed(2)}` : 'Enter amount';
  };

  // Field config for FormSection (expense fields)
  const expenseFields = [
    {
      key: 'description',
      label: 'Description',
      type: 'text',
      value: expense.description,
      onChange: (val) => setExpense(prev => ({ ...prev, description: val })),
      required: true,
      validation: (val) => !val.trim() ? 'Description is required' : (val.length < 3 ? 'Description must be at least 3 characters' : ''),
      inputProps: { placeholder: 'e.g., Flour purchase, Electricity bill' },
    },
    {
      key: 'amount',
      label: 'Amount',
      type: 'numeric',
      value: expense.amount,
      onChange: (val) => setExpense(prev => ({ ...prev, amount: val })),
      required: true,
      validation: (val) => !val ? 'Amount is required' : (isNaN(val) || Number(val) <= 0 ? 'Amount must be a positive number' : (Number(val) > 10000 ? 'Amount cannot exceed $10,000' : '')),
      inputProps: { placeholder: '0.00' },
    },
    {
      key: 'category',
      label: 'Category',
      type: 'text',
      value: expense.category,
      onChange: (val) => setExpense(prev => ({ ...prev, category: val })),
      required: true,
      validation: (val) => !val.trim() ? 'Category is required' : '',
      inputProps: { placeholder: 'e.g., Ingredients, Utilities, Staff Wages' },
    },
    {
      key: 'paymentMethod',
      label: 'Payment Method',
      type: 'text',
      value: expense.paymentMethod,
      onChange: (val) => setExpense(prev => ({ ...prev, paymentMethod: val })),
      required: true,
      validation: (val) => !val.trim() ? 'Payment method is required' : '',
      inputProps: { placeholder: 'e.g., Cash, Credit Card, Bank Transfer' },
    },
    {
      key: 'date',
      label: 'Date',
      type: 'text',
      value: expense.date,
      onChange: (val) => setExpense(prev => ({ ...prev, date: val })),
      required: true,
      validation: (val) => !val ? 'Date is required' : (/^\d{4}-\d{2}-\d{2}$/.test(val) ? '' : 'Date must be YYYY-MM-DD'),
      inputProps: { placeholder: 'YYYY-MM-DD' },
    },
    {
      key: 'notes',
      label: 'Notes (Optional)',
      type: 'text',
      value: expense.notes,
      onChange: (val) => setExpense(prev => ({ ...prev, notes: val })),
      required: false,
      multiline: true,
      inputProps: { numberOfLines: 3, placeholder: 'Additional notes about this expense...' },
    },
  ];

  return (
    <Portal>
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        backdropComponent={renderBackdrop}
        keyboardBehavior="interactive"
        keyboardBlurBehavior="restore"
        android_keyboardInputMode="adjustResize"
        enablePanDownToClose={true}
        backgroundStyle={{ backgroundColor: theme.colors.surface }}
        handleIndicatorStyle={{ backgroundColor: theme.colors.onVariant }}
        style={styles.bottomSheet}
      >
        <BottomSheetView style={styles.container}>
          {/* Header - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.header, { borderBottomColor: `${theme.colors.outline  }20` }]}>
            <View style={styles.headerLeft}>
              <View style={[styles.iconContainer, { backgroundColor: `${theme.colors.error  }15` }]}>
                <PhosphorIcon name="receipt" size={24} color={theme.colors.error} />
              </View>
              <View style={styles.titleContainer}>
                <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.on}]}>
                  {mode === 'add' ? 'Add Expense' : 'Edit Expense'}
                </Text>
                <Text variant="bodyMedium" style={[styles.subtitle, { color: theme.colors.onVariant }]}>
                  {getTotalAmount()} • {expense.category || 'Select category'}
                </Text>
              </View>
            </View>
            <IconButton
              icon="close"
              size={20}
              onPress={() => bottomSheetRef.current?.close()}
              iconColor={theme.colors.onVariant}
            />
          </View>

          {/* Content */}
          <ScrollView
            style={styles.content}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            <View style={styles.form}>
              <FormSection
                fields={expenseFields}
                errors={errors}
                onFieldError={(key, error) => setErrors(prev => ({ ...prev, [key]: error }))}
                sectionTitle="Expense Details"
              />
            </View>

            {/* Bottom padding for safe area */}
            <View style={{ height: Math.max(insets.bottom + 80, 100) }} />
          </ScrollView>

          {/* Footer - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.footer, { paddingBottom: insets.bottom, borderTopColor: `${theme.colors.outline  }20` }]}>
            <View style={styles.buttonRow}>
              <Button
                mode="outlined"
                onPress={() => bottomSheetRef.current?.close()}
                style={styles.button}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                mode="contained"
                onPress={handleSave}
                style={styles.button}
                loading={loading}
                disabled={loading}
              >
                {mode === 'add' ? 'Add Expense' : 'Update Expense'}
              </Button>
            </View>
          </View>
        </BottomSheetView>
      </BottomSheet>
    </Portal>
  );
});

const styles = StyleSheet.create({
  bottomSheet: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontWeight: '700',
    lineHeight: 28,
  },
  subtitle: {
    marginTop: 2,
    lineHeight: 20,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  form: {
    paddingBottom: 32,
  },
  input: {
    marginBottom: 16,
  },
  footer: {
    paddingHorizontal: 16,
    paddingTop: 12,
    borderTopWidth: 1,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
  },
  button: {
    flex: 1,
  },
  errorText: {
    color: '#F44336',
    marginTop: -12,
    marginBottom: 8,
  },
});

ExpenseBottomSheet.displayName = 'ExpenseBottomSheet';

export default ExpenseBottomSheet;
