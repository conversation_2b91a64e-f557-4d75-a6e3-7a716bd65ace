// @ts-nocheck
/**
 * EditProfileBottomSheet - Unified Profile Editor
 * Redesigned with unified design inspired by QuickActionsBottomSheet
 */

import BottomSheet, { BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import React, { forwardRef, useState, useEffect, useCallback, useMemo } from 'react';
import { View, StyleSheet, ScrollView, Keyboard, Dimensions } from 'react-native';
import { Text, Avatar, IconButton, Portal } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';


import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import FormSection from '../forms/FormSection';
import ImagePicker from '../forms/ImagePicker';
import Button from '../ui/Button';

const EditProfileBottomSheet = forwardRef(({ profile, onSave, onClose }, ref) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const bottomSheetRef = React.useRef(null);
  const imagePickerRef = React.useRef(null);

  const snapPoints = useMemo(() => ['90%'], []);

  const handleSheetChanges = useCallback((index) => {
    if (index === -1) {
      Keyboard.dismiss();
      onClose?.();
    }
  }, [onClose]);

  const renderBackdrop = useCallback(
    (props) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="close"
      />
    ),
    []
  );

  React.useImperativeHandle(ref, () => ({
    expand: () => {
      bottomSheetRef.current?.expand();
    },
    close: () => {
      bottomSheetRef.current?.close();
    },
  }));

  const [formData, setFormData] = useState({
    storeName: '',
    ownerName: '',
    email: '',
    phone: '',
    address: '',
    taxRate: '',
    profileImage: '',
  });

  const [errors, setErrors] = useState({});

  // Field config for FormSection (profile fields)
  const profileFields = [
    {
      key: 'storeName',
      label: 'Store Name',
      type: 'text',
      value: formData.storeName,
      onChange: (val) => setFormData(prev => ({ ...prev, storeName: val })),
      required: true,
      validation: (val) => val.trim() !== '' ? '' : 'Store name is required',
    },
    {
      key: 'ownerName',
      label: 'Owner Name',
      type: 'text',
      value: formData.ownerName,
      onChange: (val) => setFormData(prev => ({ ...prev, ownerName: val })),
      required: true,
      validation: (val) => val.trim() !== '' ? '' : 'Owner name is required',
    },
    {
      key: 'email',
      label: 'Email',
      type: 'email',
      value: formData.email,
      onChange: (val) => setFormData(prev => ({ ...prev, email: val })),
      required: false,
      validation: (val) => val === '' || (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val)) ? '' : 'Invalid email address',
    },
    {
      key: 'phone',
      label: 'Phone',
      type: 'phone',
      value: formData.phone,
      onChange: (val) => setFormData(prev => ({ ...prev, phone: val })),
      required: false,
      validation: (val) => val === '' || (/^\d{8,15}$/.test(val)) ? '' : 'Invalid phone number',
    },
    {
      key: 'address',
      label: 'Address',
      type: 'text',
      value: formData.address,
      onChange: (val) => setFormData(prev => ({ ...prev, address: val })),
      required: false,
    },
    {
      key: 'taxRate',
      label: 'Tax Rate (%)',
      type: 'numeric',
      value: formData.taxRate,
      onChange: (val) => setFormData(prev => ({ ...prev, taxRate: val })),
      required: true,
      validation: (val) => !isNaN(val) && Number(val) >= 0 && Number(val) <= 50 ? '' : 'Tax rate must be between 0 and 50',
    },
  ];

  useEffect(() => {
    if (profile) {
      setFormData({
        storeName: profile.storeName || '',
        ownerName: profile.ownerName || '',
        email: profile.email || '',
        phone: profile.phone || '',
        address: profile.address || '',
        taxRate: (profile.taxRate * 100).toString() || '8',
        profileImage: profile.profileImage || '',
      });
    }
  }, [profile]);

  const handleSave = () => {
    try {
      const updatedProfile = {
        ...formData,
        taxRate: parseFloat(formData.taxRate) / 100 || 0.08,
      };
      onSave(updatedProfile);
      bottomSheetRef.current?.close();
    } catch (error) {
      LoggingService.error('Error saving profile', 'PROFILE', error);
    }
  };





  return (
    <Portal>
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        backdropComponent={renderBackdrop}
        keyboardBehavior="interactive"
        keyboardBlurBehavior="restore"
        android_keyboardInputMode="adjustResize"
        enablePanDownToClose={true}
        backgroundStyle={{ backgroundColor: theme.colors.surface }}
        handleIndicatorStyle={{ backgroundColor: theme.colors.onVariant }}
        style={styles.bottomSheet}
      >
        <BottomSheetView style={[styles.container, { maxHeight: Dimensions.get('window').height * 0.9 }]}>
          {/* Header - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.header, { borderBottomColor: `${theme.colors.outline  }20` }]}>
            <View style={styles.titleContainer}>
              <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.on}]}>
                Edit Profile
              </Text>
            </View>
            <IconButton
              icon="close"
              size={20}
              onPress={() => bottomSheetRef.current?.close()}
              iconColor={theme.colors.onVariant}
            />
          </View>

          {/* Content */}
          <ScrollView
            style={styles.content}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
        {/* Avatar Section */}
        <View style={styles.avatarSection}>
          <View style={{ alignItems: 'center', justifyContent: 'center', position: 'relative' }}>
            {formData.profileImage ? (
              <Avatar.Image
                size={100}
                source={{ uri: formData.profileImage }}
                style={{ backgroundColor: theme.colors.primary }}
              />
            ) : (
              <Avatar.Text
                size={100}
                label={formData.storeName.split(' ').map(word => word[0]).join('').substring(0, 2)}
                style={{ backgroundColor: theme.colors.primary }}
              />
            )}
            {/* Camera Icon Overlay */}
            <IconButton
              icon="camera"
              size={28}
              style={{
                position: 'absolute',
                bottom: 0,
                right: 0,
                backgroundColor: theme.colors.surface,
                borderRadius: 20,
                elevation: 2,
              }}
              onPress={async () => {
                // Open the gallery directly
                if (imagePickerRef.current && imagePickerRef.current.openGallery) {
                  imagePickerRef.current.openGallery();
                }
              }}
            />
          </View>
          {/* Remove button if image is present */}
          {formData.profileImage ? (
            <Button
              mode="text"
              onPress={() => setFormData(prev => ({ ...prev, profileImage: '' }))}
              style={{ marginTop: 4 }}
              textColor={theme.colors.error}
              icon="delete"
            >
              Remove Image
            </Button>
          ) : null}
          <Text variant="bodyMedium" style={{ color: theme.colors.onVariant, textAlign: 'center', marginTop: 8 }}>
            Store Avatar
          </Text>
          {/* Inline ImagePicker, always rendered but hidden UI */}
          <ImagePicker
            ref={imagePickerRef}
            currentImage={formData.profileImage}
            onImageSelected={(uri) => {
              setFormData(prev => ({ ...prev, profileImage: uri }));
            }}
            placeholder={formData.profileImage ? 'Change Profile Image' : 'Upload Profile Image'}
            style={{ display: 'none' }}
          />
        </View>

        {/* Form Fields */}
        <View style={styles.form}>
          <FormSection
            fields={profileFields}
            errors={errors}
            onFieldError={(key, error) => setErrors(prev => ({ ...prev, [key]: error }))}
            sectionTitle="Profile Details"
          />
            </View>

            {/* Bottom padding for safe area */}
            <View style={{ height: Math.max(insets.bottom + 80, 100) }} />
          </ScrollView>

          {/* Footer - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.footer, { paddingBottom: insets.bottom, borderTopColor: `${theme.colors.outline  }20` }]}>
            <View style={styles.actions}>
              <Button
                mode="outlined"
                onPress={() => bottomSheetRef.current?.close()}
                style={styles.actionButton}
              >
                Cancel
              </Button>
              <Button
                mode="contained"
                onPress={handleSave}
                style={styles.actionButton}
                icon="check"
              >
                Save Changes
              </Button>
            </View>
          </View>
        </BottomSheetView>
      </BottomSheet>
    </Portal>
  );
});

const styles = StyleSheet.create({
  bottomSheet: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontWeight: '700',
    lineHeight: 28,
  },
  subtitle: {
    marginTop: 2,
    lineHeight: 20,
  },
  content: {
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  avatarSection: {
    alignItems: 'center',
    marginBottom: 24,
  },
  avatarContainer: {
    borderRadius: 50,
    padding: 8,
  },
  form: {
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: 12,
    marginTop: 16,
  },
  input: {
    marginBottom: 12,
  },
  footer: {
    paddingHorizontal: 16,
    paddingTop: 12,
    borderTopWidth: 1,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
});

EditProfileBottomSheet.displayName = 'EditProfileBottomSheet';

export default EditProfileBottomSheet;
