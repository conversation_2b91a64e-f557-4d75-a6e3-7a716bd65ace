// @ts-nocheck
import React, { forwardRef, useState } from 'react';
import { View } from 'react-native';
import { Text, Button } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import FormSection from '../forms/FormSection';

import BottomSheet from './BottomSheet';

const SecuritySettingsBottomSheet = forwardRef(({ onPasswordChange }, ref) => {
  const theme = useTheme();
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState({});
  const [success, setSuccess] = useState(false);

  const handleInputChange = (field, value) => {
    setPasswordData(prev => ({ ...prev, [field]: value }));
    setErrors(prev => ({ ...prev, [field]: undefined }));
    setSuccess(false);
  };

  const handlePasswordChange = () => {
    const newErrors = {};
    if (!passwordData.currentPassword) newErrors.currentPassword = 'Current password is required';
    if (!passwordData.newPassword) newErrors.newPassword = 'New password is required';
    else if (passwordData.newPassword.length < 8) newErrors.newPassword = 'Password must be at least 8 characters';
    if (passwordData.newPassword !== passwordData.confirmPassword) newErrors.confirmPassword = 'Passwords do not match';
    setErrors(newErrors);
    if (Object.keys(newErrors).length === 0) {
      setSuccess(true);
      setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' });
      if (onPasswordChange) onPasswordChange();
    }
  };

  const passwordFields = [
    {
      key: 'currentPassword',
      label: 'Current Password',
      type: 'password',
      value: passwordData.currentPassword,
      onChange: (val) => setPasswordData(prev => ({ ...prev, currentPassword: val })),
      required: true,
      validation: (val) => val ? '' : 'Current password is required',
    },
    {
      key: 'newPassword',
      label: 'New Password',
      type: 'password',
      value: passwordData.newPassword,
      onChange: (val) => setPasswordData(prev => ({ ...prev, newPassword: val })),
      required: true,
      validation: (val) => !val ? 'New password is required' : (val.length < 8 ? 'Password must be at least 8 characters' : ''),
    },
    {
      key: 'confirmPassword',
      label: 'Confirm New Password',
      type: 'password',
      value: passwordData.confirmPassword,
      onChange: (val) => setPasswordData(prev => ({ ...prev, confirmPassword: val })),
      required: true,
      validation: (val) => val !== passwordData.newPassword ? 'Passwords do not match' : '',
    },
  ];

  return (
    <BottomSheet
      ref={ref}
      title="Change Password"
      icon="lock-reset"
      snapPoints={["50%", "70%"]}
    >
      <View style={{ padding: 16 }}>
        <FormSection
          fields={passwordFields}
          errors={errors}
          onFieldError={(key, error) => setErrors(prev => ({ ...prev, [key]: error }))}
          sectionTitle="Password Change"
        />
        {success && <Text style={{ color: theme.colors.primary, marginBottom: 8 }}>Password changed successfully!</Text>}
        <Button mode="contained" onPress={handlePasswordChange} style={{ marginTop: 8 }}>
          Change Password
        </Button>
      </View>
    </BottomSheet>
  );
});

SecuritySettingsBottomSheet.displayName = 'SecuritySettingsBottomSheet';

export default SecuritySettingsBottomSheet; 