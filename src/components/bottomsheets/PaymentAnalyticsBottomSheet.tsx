// @ts-nocheck
/**
 * PaymentAnalyticsBottomSheet - Unified Payment Analytics
 * Redesigned with unified design inspired by QuickActionsBottomSheet
 */

import BottomSheet, { BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import React, { forwardRef, useImperativeHandle, useCallback, useMemo } from 'react';
import { View, StyleSheet, ScrollView, Keyboard } from 'react-native';
import {
  Text,
  Button,
  Card,
  DataTable,
  ProgressBar,
  IconButton,
  Portal,
} from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useTheme } from '../../context/ThemeContext';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

// import { PieChart } from 'react-native-chart-kit'; // Commented out for now
import { FINANCIAL_CONFIG } from '../config/constants';

const PaymentAnalyticsBottomSheet = forwardRef(({ data }, ref) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const bottomSheetRef = React.useRef(null);

  // Safety check for data structure
  const normalizedData = useMemo(() => {
    if (!data) {
      return {
        period: { startDate: new Date(), endDate: new Date() },
        summary: { totalRevenue: 0, totalOrders: 0, averageOrderValue: 0 },
        byPaymentMethod: {}
      };
    }
    
    return {
      period: data.period || { startDate: new Date(), endDate: new Date() },
      summary: data.summary || { totalRevenue: 0, totalOrders: 0, averageOrderValue: 0 },
      byPaymentMethod: data.byPaymentMethod || {}
    };
  }, [data]);

  const snapPoints = useMemo(() => ['90%'], []);

  const handleSheetChanges = useCallback((index) => {
    if (index === -1) {
      Keyboard.dismiss();
    }
  }, []);

  const renderBackdrop = useCallback(
    (props) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="close"
      />
    ),
    []
  );

  useImperativeHandle(ref, () => ({
    expand: () => {
      bottomSheetRef.current?.expand();
    },
    close: () => {
      bottomSheetRef.current?.close();
    },
  }));

  const formatCurrency = (amount) => {
    return `${FINANCIAL_CONFIG.CURRENCY.SYMBOL}${amount.toFixed(FINANCIAL_CONFIG.CURRENCY.DECIMAL_PLACES)}`;
  };

  const formatPercentage = (value) => {
    return `${value.toFixed(1)}%`;
  };

  const getChartData = () => {
    if (!normalizedData.byPaymentMethod) return [];

    const colors = [
      '#4CAF50', '#2196F3', '#FF9800', '#9C27B0',
      '#F44336', '#00BCD4', '#795548', '#607D8B'
    ];

    return Object.entries(normalizedData.byPaymentMethod)
      .filter(([_, methodData]) => methodData.totalAmount > 0)
      .map(([method, methodData], index) => ({
        name: method,
        amount: methodData.totalAmount,
        percentage: methodData.percentage,
        color: colors[index % colors.length],
        legendFontColor: theme.colors.onSurface,
        legendFontSize: 12,
      }));
  };

  const getMostPopularPaymentMethod = () => {
    if (!normalizedData.byPaymentMethod) return null;

    return Object.entries(normalizedData.byPaymentMethod)
      .reduce((max, [method, methodData]) =>
        methodData.totalAmount > (max?.data?.totalAmount || 0)
          ? { method, data: methodData }
          : max,
        null
      );
  };

  const chartData = getChartData();
  const mostPopular = getMostPopularPaymentMethod();

  const getAnalyticsInfo = () => {
    if (!normalizedData.summary) return 'Loading analytics...';
    return `${formatCurrency(normalizedData.summary.totalRevenue)} • ${normalizedData.summary.totalOrders} orders • ${mostPopular?.method || 'N/A'} top method`;
  };

  return (
    <Portal>
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        backdropComponent={renderBackdrop}
        keyboardBehavior="interactive"
        keyboardBlurBehavior="restore"
        android_keyboardInputMode="adjustResize"
        enablePanDownToClose={true}
        backgroundStyle={{ backgroundColor: theme.colors.surface }}
        handleIndicatorStyle={{ backgroundColor: theme.colors.onVariant }}
        style={styles.bottomSheet}
      >
        <BottomSheetView style={styles.container}>
          {/* Header - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.header, { borderBottomColor: `${theme.colors.outline  }20` }]}>
            <View style={styles.headerLeft}>
              <View style={[styles.iconContainer, { backgroundColor: `${theme.colors.primary  }15` }]}>
                <PhosphorIcon name="chart-pie" size={24} color={theme.colors.primary} />
              </View>
              <View style={styles.titleContainer}>
                <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.on}]}>
                  Payment Analytics
                </Text>
                <Text variant="bodyMedium" style={[styles.subtitle, { color: theme.colors.onVariant }]}>
                  {new Date(normalizedData.period.startDate).toLocaleDateString()} - {new Date(normalizedData.period.endDate).toLocaleDateString()} • {getAnalyticsInfo()}
                </Text>
              </View>
            </View>
            <IconButton
              icon="close"
              size={20}
              onPress={() => bottomSheetRef.current?.close()}
              iconColor={theme.colors.onVariant}
            />
          </View>

          {/* Content */}
          <ScrollView
            style={styles.content}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            {/* Summary Cards */}
            <View style={styles.summaryGrid}>
              <Card style={[styles.summaryCard, { backgroundColor: theme.colors.primaryContainer }]}>
                <Card.Content style={styles.summaryContent}>
              <PhosphorIcon name="money" size={24} color={theme.colors.primary} />
              <Text variant="bodySmall" style={{ color: theme.colors.onPrimaryContainer }}>
                Total Revenue
              </Text>
              <Text variant="titleMedium" style={{ color: theme.colors.onPrimaryContainer, fontWeight: '700' }}>
                {formatCurrency(normalizedData.summary.totalRevenue)}
              </Text>
            </Card.Content>
          </Card>

          <Card style={[styles.summaryCard, { backgroundColor: theme.colors.secondaryContainer }]}>
            <Card.Content style={styles.summaryContent}>
              <PhosphorIcon name="receipt" size={24} color={theme.colors.secondary} />
              <Text variant="bodySmall" style={{ color: theme.colors.onSecondaryContainer }}>
                Total Orders
              </Text>
              <Text variant="titleMedium" style={{ color: theme.colors.onSecondaryContainer, fontWeight: '700' }}>
                {normalizedData.summary.totalOrders}
              </Text>
            </Card.Content>
          </Card>

          <Card style={[styles.summaryCard, { backgroundColor: theme.colors.tertiaryContainer }]}>
            <Card.Content style={styles.summaryContent}>
              <PhosphorIcon name="chart-line" size={24} color={theme.colors.tertiary} />
              <Text variant="bodySmall" style={{ color: theme.colors.onTertiaryContainer }}>
                Avg Order Value
              </Text>
              <Text variant="titleMedium" style={{ color: theme.colors.onTertiaryContainer, fontWeight: '700' }}>
                {formatCurrency(normalizedData.summary.averageOrderValue)}
              </Text>
            </Card.Content>
          </Card>
        </View>

        {/* Most Popular Payment Method */}
        {mostPopular && (
          <Card style={[styles.popularCard, { backgroundColor: theme.colors.surface }]}>
            <Card.Content>
              <View style={styles.popularHeader}>
                <PhosphorIcon name="trophy" size={24} color="#FFD700" />
                <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginLeft: 8 }}>
                  Most Popular Payment Method
                </Text>
              </View>
              <View style={styles.popularContent}>
                <Text variant="headlineSmall" style={{ color: theme.colors.primary, fontWeight: '700' }}>
                  {mostPopular.method}
                </Text>
                <Text variant="bodyMedium" style={{ color: theme.colors.onVariant }}>
                  {formatCurrency(mostPopular.data.totalAmount)} • {formatPercentage(mostPopular.data.percentage)} of total revenue
                </Text>
                <Text variant="bodySmall" style={{ color: theme.colors.onVariant }}>
                  {mostPopular.data.orderCount} orders • Avg: {formatCurrency(mostPopular.data.averageOrderValue)}
                </Text>
              </View>
            </Card.Content>
          </Card>
        )}

        {/* Chart Placeholder - Will be implemented later */}
        {chartData.length > 0 && (
          <Card style={[styles.chartCard, { backgroundColor: theme.colors.surface }]}>
            <Card.Content>
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginBottom: 16 }}>
                Revenue Distribution
              </Text>
              <View style={styles.chartContainer}>
                <Text variant="bodyMedium" style={{ color: theme.colors.onVariant, textAlign: 'center' }}>
                  Chart visualization will be available in the next update
                </Text>
              </View>
            </Card.Content>
          </Card>
        )}

        {/* Detailed Breakdown */}
        <Card style={[styles.detailCard, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginBottom: 16 }}>
              Detailed Breakdown
            </Text>

            <DataTable>
              <DataTable.Header>
                <DataTable.Title>Payment Method</DataTable.Title>
                <DataTable.Title numeric>Orders</DataTable.Title>
                <DataTable.Title numeric>Amount</DataTable.Title>
                <DataTable.Title numeric>%</DataTable.Title>
              </DataTable.Header>

              {Object.entries(normalizedData.byPaymentMethod)
                .filter(([_, methodData]) => methodData.totalAmount > 0)
                .sort(([,a], [,b]) => b.totalAmount - a.totalAmount)
                .map(([method, methodData]) => (
                  <DataTable.Row key={method}>
                    <DataTable.Cell>
                      <View style={styles.methodCell}>
                        <View style={[
                          styles.methodIndicator,
                          { backgroundColor: chartData.find(d => d.name === method)?.color || theme.colors.primary }
                        ]} />
                        <Text variant="bodyMedium" style={{ color: theme.colors.on}}>
                          {method}
                        </Text>
                      </View>
                    </DataTable.Cell>
                    <DataTable.Cell numeric>
                      <Text variant="bodyMedium" style={{ color: theme.colors.on}}>
                        {methodData.orderCount}
                      </Text>
                    </DataTable.Cell>
                    <DataTable.Cell numeric>
                      <Text variant="bodyMedium" style={{ color: theme.colors.on}}>
                        {formatCurrency(methodData.totalAmount)}
                      </Text>
                    </DataTable.Cell>
                    <DataTable.Cell numeric>
                      <Text variant="bodyMedium" style={{ color: theme.colors.on}}>
                        {formatPercentage(methodData.percentage)}
                      </Text>
                    </DataTable.Cell>
                  </DataTable.Row>
                ))}
            </DataTable>

            {/* Progress Bars */}
            <View style={styles.progressSection}>
              <Text variant="titleSmall" style={{ color: theme.colors.onSurface, marginBottom: 12 }}>
                Revenue Share
              </Text>
              {Object.entries(normalizedData.byPaymentMethod)
                .filter(([_, methodData]) => methodData.totalAmount > 0)
                .sort(([,a], [,b]) => b.totalAmount - a.totalAmount)
                .map(([method, methodData]) => (
                  <View key={method} style={styles.progressItem}>
                    <View style={styles.progressHeader}>
                      <Text variant="bodySmall" style={{ color: theme.colors.on}}>
                        {method}
                      </Text>
                      <Text variant="bodySmall" style={{ color: theme.colors.onVariant }}>
                        {formatPercentage(methodData.percentage)}
                      </Text>
                    </View>
                    <ProgressBar
                      progress={methodData.percentage / 100}
                      color={chartData.find(d => d.name === method)?.color || theme.colors.primary}
                      style={styles.progressBar}
                    />
                  </View>
                ))}
              </View>
              </Card.Content>
            </Card>

            {/* Bottom padding for safe area */}
            <View style={{ height: Math.max(insets.bottom + 80, 100) }} />
          </ScrollView>

          {/* Footer - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.footer, { paddingBottom: insets.bottom, borderTopColor: `${theme.colors.outline  }20` }]}>
            <Button
              mode="contained"
              onPress={() => {
                // Export functionality would go here
                // console.log('Export payment analytics');
              }}
              icon="download"
              style={styles.exportButton}
            >
              Export Analytics
            </Button>
          </View>
        </BottomSheetView>
      </BottomSheet>
    </Portal>
  );
});

const styles = StyleSheet.create({
  bottomSheet: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontWeight: '700',
    lineHeight: 28,
  },
  subtitle: {
    marginTop: 2,
    lineHeight: 20,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  summaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  summaryCard: {
    flex: 1,
    minWidth: '30%',
    borderRadius: 12,
  },
  summaryContent: {
    alignItems: 'center',
    gap: 4,
  },
  popularCard: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
  popularHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  popularContent: {
    alignItems: 'center',
    gap: 4,
  },
  chartCard: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
  chartContainer: {
    alignItems: 'center',
  },
  detailCard: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
  methodCell: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  methodIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  progressSection: {
    marginTop: 16,
  },
  progressItem: {
    marginBottom: 12,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
  },
  footer: {
    paddingHorizontal: 16,
    paddingTop: 12,
    borderTopWidth: 1,
    alignItems: 'center',
  },
  exportButton: {
    borderRadius: 25,
    minWidth: 200,
  },
});

PaymentAnalyticsBottomSheet.displayName = 'PaymentAnalyticsBottomSheet';

export default PaymentAnalyticsBottomSheet;
