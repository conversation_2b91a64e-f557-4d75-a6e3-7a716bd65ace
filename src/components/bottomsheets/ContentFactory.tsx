import React from 'react';
import { ScrollView, View } from 'react-native';
import { Button, Text, Surface } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import FormSection from '../forms/FormSection';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

interface ContentFactoryProps {
  type: string;
  data: any;
  mode: 'add' | 'edit' | 'view';
  onSave?: (data: any) => void;
  onAction?: (actionId: string) => void;
  onClose?: () => void;
}

const ContentFactory: React.FC<ContentFactoryProps> = ({
  type,
  data,
  mode,
  onSave,
  onAction,
  onClose,
}) => {
  const theme = useTheme();

  // Customer form fields
  const getCustomerFields = () => [
    {
      name: 'name',
      key: 'name',
      label: 'Name',
      type: 'text' as const,
      value: data.name || '',
      onChange: (val: string) => console.log('name:', val),
      required: true,
    },
    {
      name: 'phone',
      key: 'phone',
      label: 'Phone',
      type: 'phone' as const,
      value: data.phone || '',
      onChange: (val: string) => console.log('phone:', val),
      required: true,
    },
    {
      name: 'email',
      key: 'email',
      label: 'Email',
      type: 'email' as const,
      value: data.email || '',
      onChange: (val: string) => console.log('email:', val),
      required: false,
    },
  ];

  // Quick actions
  const getQuickActions = () => [
    {
      id: 'add-product',
      title: 'Add Product',
      icon: 'plus-circle',
      color: theme.colors.primary,
    },
    {
      id: 'add-order',
      title: 'New Order',
      icon: 'clipboard-plus',
      color: theme.colors.secondary,
    },
    {
      id: 'add-customer',
      title: 'Add Customer',
      icon: 'account-plus',
      color: theme.colors.tertiary,
    },
  ];

  const renderCustomerForm = () => (
    <ScrollView style={{ flex: 1 }}>
      <FormSection
        fields={getCustomerFields()}
        errors={{}}
        onFieldError={() => {}}
        sectionTitle="Customer Details"
      />
      <Button
        mode="contained"
        onPress={() => onSave?.(data)}
        style={{ marginTop: 16 }}
        buttonColor={theme.colors.primary}
      >
        {mode === 'edit' ? 'Update Customer' : 'Save Customer'}
      </Button>
    </ScrollView>
  );

  const renderQuickActions = () => (
    <ScrollView style={{ flex: 1 }}>
      {getQuickActions().map((action) => (
        <Surface key={action.id} style={{ marginVertical: 8, padding: 16, borderRadius: 8 }}>
          <Button
            mode="outlined"
            onPress={() => onAction?.(action.id)}
            icon={action.icon}
            textColor={action.color}
          >
            {action.title}
          </Button>
        </Surface>
      ))}
    </ScrollView>
  );

  const renderOrderDetails = () => (
    <ScrollView style={{ flex: 1 }}>
      <Surface style={{ padding: 16, marginBottom: 16, borderRadius: 8 }}>
        <Text variant="titleMedium">Order #{data.id || 'N/A'}</Text>
        <Text variant="bodyMedium">Customer: {data.customer || 'N/A'}</Text>
        <Text variant="bodyMedium">Total: ${data.total || '0.00'}</Text>
        <Text variant="bodyMedium">Status: {data.status || 'Pending'}</Text>
      </Surface>
    </ScrollView>
  );

  const renderPlaceholder = () => (
    <ScrollView style={{ flex: 1 }}>
      <View style={{ padding: 16, alignItems: 'center' }}>
        <PhosphorIcon name="info" size={48} color={theme.colors.onSurfaceVariant} />
        <Text variant="titleMedium" style={{ marginTop: 16 }}>
          {type} Content
        </Text>
        <Text variant="bodyMedium" style={{ marginTop: 8, textAlign: 'center' }}>
          This content type is not yet implemented in the unified system.
        </Text>
      </View>
    </ScrollView>
  );

  // Render based on type
  switch (type) {
    case 'add-customer':
    case 'edit-customer':
      return renderCustomerForm();
    
    case 'quick-actions':
      return renderQuickActions();
    
    case 'order-details':
      return renderOrderDetails();
    
    default:
      return renderPlaceholder();
  }
};

export default ContentFactory; 