// @ts-nocheck
/**
 * ProfitLossBottomSheet - Unified P&L Statement
 * Redesigned with unified design inspired by QuickActionsBottomSheet
 */

import BottomSheet, { BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import React, { forwardRef, useImperativeHandle, useCallback, useMemo } from 'react';
import { View, StyleSheet, ScrollView, Keyboard } from 'react-native';
import {
  Text,
  Button,
  Card,
  Divider,
  DataTable,
  IconButton,
  Portal,
} from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';
import { FINANCIAL_CONFIG } from '../config/constants';
// import * as FileSystem from 'expo-file-system'; // Will be implemented later
// import * as Sharing from 'expo-sharing'; // Will be implemented later

const ProfitLossBottomSheet = forwardRef(({ data }, ref) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const bottomSheetRef = React.useRef(null);



  const snapPoints = useMemo(() => ['90%'], []);

  const handleSheetChanges = useCallback((index) => {
    if (index === -1) {
      Keyboard.dismiss();
    }
  }, []);

  const renderBackdrop = useCallback(
    (props) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="close"
      />
    ),
    []
  );

  useImperativeHandle(ref, () => ({
    expand: () => {
      bottomSheetRef.current?.expand();
    },
    close: () => {
      bottomSheetRef.current?.close();
    },
  }));

  const formatCurrency = (amount) => {
    return `${FINANCIAL_CONFIG.CURRENCY.SYMBOL}${amount.toFixed(FINANCIAL_CONFIG.CURRENCY.DECIMAL_PLACES)}`;
  };

  const formatPercentage = (value) => {
    return `${value.toFixed(1)}%`;
  };

  const exportToPDF = async () => {
    // Export functionality will be implemented later
    LoggingService.info('Export P&L statement - feature coming soon', 'FINANCIAL');
  };



  if (!data) return null;

  // Normalize data structure to handle different formats
  const normalizedData = {
    revenue: {
      totalSales: data.revenue?.totalSales ?? data.revenue ?? 0,
      orderCount: data.revenue?.orderCount ?? 0,
      averageOrderValue: data.revenue?.averageOrderValue ?? 0
    },
    expenses: {
      total: data.expenses?.total ?? data.expenses ?? 0,
      byCategory: data.expenses?.byCategory ?? {}
    },
    taxes: {
      salesTax: data.taxes?.salesTax ?? 0,
      incomeTax: data.taxes?.incomeTax ?? 0,
      total: data.taxes?.total ?? 0
    },
    profit: {
      gross: data.profit?.gross ?? data.grossProfit ?? 0,
      net: data.profit?.net ?? data.netProfit ?? 0,
      margin: data.profit?.margin ?? data.profitMargin ?? 0
    },
    period: data.period ?? {
      startDate: new Date().toISOString(),
      endDate: new Date().toISOString()
    }
  };

  const getProfitStatus = () => {
    const netProfit = normalizedData.profit.net;
    const margin = normalizedData.profit.margin;
    return `${netProfit >= 0 ? 'Profit' : 'Loss'}: ${formatCurrency(Math.abs(netProfit))} • ${formatPercentage(margin)} margin`;
  };

  return (
    <Portal>
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        backdropComponent={renderBackdrop}
        keyboardBehavior="interactive"
        keyboardBlurBehavior="restore"
        android_keyboardInputMode="adjustResize"
        enablePanDownToClose={true}
        backgroundStyle={{ backgroundColor: theme.colors.surface }}
        handleIndicatorStyle={{ backgroundColor: theme.colors.onVariant }}
        style={styles.bottomSheet}
      >
        <BottomSheetView style={styles.container}>
          {/* Header - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.header, { borderBottomColor: `${theme.colors.outline  }20` }]}>
            <View style={styles.headerLeft}>
              <View style={[styles.iconContainer, { backgroundColor: `${theme.colors.primary  }15` }]}>
                <PhosphorIcon name="chart-line" size={24} color={theme.colors.primary} />
              </View>
              <View style={styles.titleContainer}>
                <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.on}]}>
                  Profit & Loss Statement
                </Text>
                <Text variant="bodyMedium" style={[styles.subtitle, { color: theme.colors.onVariant }]}>
                  {normalizedData.period ? `${new Date(normalizedData.period.startDate).toLocaleDateString()} - ${new Date(normalizedData.period.endDate).toLocaleDateString()} • ` : ''}{getProfitStatus()}
                </Text>
              </View>
            </View>
            <IconButton
              icon="close"
              size={20}
              onPress={() => bottomSheetRef.current?.close()}
              iconColor={theme.colors.onVariant}
            />
          </View>

          {/* Content */}
          <ScrollView
            style={styles.content}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            {/* Revenue Section */}
            <Card style={[styles.section, { backgroundColor: theme.colors.surface }]}>
              <Card.Content>
            <View style={styles.sectionHeader}>
              <PhosphorIcon name="trending-up" size={24} color={theme.colors.primary} />
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginLeft: 8 }}>
                Revenue
              </Text>
            </View>

            <DataTable>
              <DataTable.Row>
                <DataTable.Cell>Total Sales ({normalizedData.revenue.orderCount} orders)</DataTable.Cell>
                <DataTable.Cell numeric>{formatCurrency(normalizedData.revenue.totalSales)}</DataTable.Cell>
              </DataTable.Row>
              <DataTable.Row>
                <DataTable.Cell>Average Order Value</DataTable.Cell>
                <DataTable.Cell numeric>{formatCurrency(normalizedData.revenue.averageOrderValue)}</DataTable.Cell>
              </DataTable.Row>
            </DataTable>

            <Divider style={styles.divider} />
            <View style={styles.totalRow}>
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface, fontWeight: '700' }}>
                Total Revenue
              </Text>
              <Text variant="titleMedium" style={{ color: theme.colors.primary, fontWeight: '700' }}>
                {formatCurrency(normalizedData.revenue.totalSales)}
              </Text>
            </View>
          </Card.Content>
        </Card>

        {/* Expenses Section */}
        <Card style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <PhosphorIcon name="trending-down" size={24} color="#F44336" />
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginLeft: 8 }}>
                Expenses
              </Text>
            </View>

            <DataTable>
              {Object.entries(normalizedData.expenses.byCategory).map(([category, amount]) => (
                <DataTable.Row key={category}>
                  <DataTable.Cell>{category}</DataTable.Cell>
                  <DataTable.Cell numeric>{formatCurrency(amount)}</DataTable.Cell>
                </DataTable.Row>
              ))}
            </DataTable>

            <Divider style={styles.divider} />
            <View style={styles.totalRow}>
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface, fontWeight: '700' }}>
                Total Expenses
              </Text>
              <Text variant="titleMedium" style={{ color: '#F44336', fontWeight: '700' }}>
                {formatCurrency(normalizedData.expenses.total)}
              </Text>
            </View>
          </Card.Content>
        </Card>

        {/* Taxes Section */}
        <Card style={[styles.section, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <PhosphorIcon name="calculator" size={24} color="#FF9800" />
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginLeft: 8 }}>
                Taxes
              </Text>
            </View>

            <DataTable>
              <DataTable.Row>
                <DataTable.Cell>Sales Tax ({formatPercentage(FINANCIAL_CONFIG.TAX_RATES.SALES_TAX * 100)})</DataTable.Cell>
                <DataTable.Cell numeric>{formatCurrency(normalizedData.taxes.salesTax)}</DataTable.Cell>
              </DataTable.Row>
              <DataTable.Row>
                <DataTable.Cell>Income Tax ({formatPercentage(FINANCIAL_CONFIG.TAX_RATES.INCOME_TAX * 100)})</DataTable.Cell>
                <DataTable.Cell numeric>{formatCurrency(normalizedData.taxes.incomeTax)}</DataTable.Cell>
              </DataTable.Row>
            </DataTable>

            <Divider style={styles.divider} />
            <View style={styles.totalRow}>
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface, fontWeight: '700' }}>
                Total Taxes
              </Text>
              <Text variant="titleMedium" style={{ color: '#FF9800', fontWeight: '700' }}>
                {formatCurrency(normalizedData.taxes.total)}
              </Text>
            </View>
          </Card.Content>
        </Card>

        {/* Profit Summary */}
        <Card style={[styles.section, { backgroundColor: theme.colors.primaryContainer }]}>
          <Card.Content>
            <View style={styles.sectionHeader}>
              <PhosphorIcon name="chart-line" size={24} color={theme.colors.primary} />
              <Text variant="titleMedium" style={{ color: theme.colors.onPrimaryContainer, marginLeft: 8 }}>
                Profit Summary
              </Text>
            </View>

            <DataTable>
              <DataTable.Row>
                <DataTable.Cell>
                  <Text style={{ color: theme.colors.onPrimaryContainer }}>Gross Profit</Text>
                </DataTable.Cell>
                <DataTable.Cell numeric>
                  <Text style={{
                    color: normalizedData.profit.gross >= 0 ? '#4CAF50' : '#F44336',
                    fontWeight: '600'
                  }}>
                    {formatCurrency(normalizedData.profit.gross)}
                  </Text>
                </DataTable.Cell>
              </DataTable.Row>
              <DataTable.Row>
                <DataTable.Cell>
                  <Text style={{ color: theme.colors.onPrimaryContainer }}>Profit Margin</Text>
                </DataTable.Cell>
                <DataTable.Cell numeric>
                  <Text style={{ color: theme.colors.onPrimaryContainer }}>
                    {formatPercentage(normalizedData.profit.margin)}
                  </Text>
                </DataTable.Cell>
              </DataTable.Row>
            </DataTable>

            <Divider style={styles.divider} />
            <View style={styles.totalRow}>
              <Text variant="titleLarge" style={{ color: theme.colors.onPrimaryContainer, fontWeight: '700' }}>
                Net Profit
              </Text>
              <Text variant="titleLarge" style={{
                color: normalizedData.profit.net >= 0 ? '#4CAF50' : '#F44336',
                fontWeight: '700'
              }}>
                {formatCurrency(normalizedData.profit.net)}
              </Text>
              </View>
              </Card.Content>
            </Card>

            {/* Bottom padding for safe area */}
            <View style={{ height: Math.max(insets.bottom + 80, 100) }} />
          </ScrollView>

          {/* Footer - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.footer, { paddingBottom: insets.bottom, borderTopColor: `${theme.colors.outline  }20` }]}>
            <Button
              mode="contained"
              onPress={exportToPDF}
              icon="download"
              style={styles.exportButton}
            >
              Export Statement
            </Button>
          </View>
        </BottomSheetView>
      </BottomSheet>
    </Portal>
  );
});

const styles = StyleSheet.create({
  bottomSheet: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontWeight: '700',
    lineHeight: 28,
  },
  subtitle: {
    marginTop: 2,
    lineHeight: 20,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  section: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  divider: {
    marginVertical: 8,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 8,
  },
  footer: {
    paddingHorizontal: 16,
    paddingTop: 12,
    borderTopWidth: 1,
    alignItems: 'center',
  },
  exportButton: {
    borderRadius: 25,
    minWidth: 200,
  },
});

ProfitLossBottomSheet.displayName = 'ProfitLossBottomSheet';

export default ProfitLossBottomSheet;
