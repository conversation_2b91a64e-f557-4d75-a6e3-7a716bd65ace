import React from 'react';
import { ScrollView, View } from 'react-native';
import { Button, Text } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { BottomSheetType } from './UnifiedBottomSheetTypes';
import FormSection from '../forms/FormSection';
import { FormField } from './UnifiedBottomSheetTypes';

interface BottomSheetContentFactoryProps {
  type: BottomSheetType;
  data: any;
  mode: 'add' | 'edit' | 'view';
  onSave?: (data: any) => void;
  onAction?: (actionId: string) => void;
}

const BottomSheetContentFactory: React.FC<BottomSheetContentFactoryProps> = ({
  type,
  data,
  mode,
  onSave,
  onAction,
}) => {
  const theme = useTheme();

  // Customer form fields
  const getCustomerFields = (): FormField[] => [
    {
      name: 'name',
      key: 'name',
      label: 'Full Name',
      type: 'text',
      value: data.name || '',
      onChange: (val: string) => console.log('name changed:', val),
      required: true,
    },
    {
      name: 'phone',
      key: 'phone',
      label: 'Phone Number',
      type: 'phone',
      value: data.phone || '',
      onChange: (val: string) => console.log('phone changed:', val),
      required: true,
    },
    {
      name: 'email',
      key: 'email',
      label: 'Email Address',
      type: 'email',
      value: data.email || '',
      onChange: (val: string) => console.log('email changed:', val),
      required: false,
    },
    {
      name: 'gender',
      key: 'gender',
      label: 'Gender',
      type: 'toggle',
      value: data.gender || '',
      onChange: (val: string) => console.log('gender changed:', val),
      required: false,
      options: [
        { label: 'Male', value: 'male' },
        { label: 'Female', value: 'female' },
        { label: 'Other', value: 'other' },
      ],
    },
    {
      name: 'address',
      key: 'address',
      label: 'Address',
      type: 'textarea',
      value: data.address || '',
      onChange: (val: string) => console.log('address changed:', val),
      required: false,
      multiline: true,
    },
  ];

  // Product form fields
  const getProductFields = (): FormField[] => [
    {
      name: 'name',
      key: 'name',
      label: 'Product Name',
      type: 'text',
      value: data.name || '',
      onChange: (val: string) => console.log('name changed:', val),
      required: true,
    },
    {
      name: 'price',
      key: 'price',
      label: 'Price',
      type: 'number',
      value: data.price || '',
      onChange: (val: string) => console.log('price changed:', val),
      required: true,
    },
    {
      name: 'stock',
      key: 'stock',
      label: 'Stock',
      type: 'number',
      value: data.stock || '',
      onChange: (val: string) => console.log('stock changed:', val),
      required: true,
    },
  ];

  // Quick actions
  const getQuickActions = () => [
    {
      id: 'add-product',
      title: 'Add Product',
      icon: 'plus-circle',
      color: theme.colors.primary,
      backgroundColor: theme.colors.primaryContainer,
    },
    {
      id: 'add-order',
      title: 'New Order',
      icon: 'clipboard-plus',
      color: theme.colors.secondary,
      backgroundColor: theme.colors.secondaryContainer,
    },
    {
      id: 'add-customer',
      title: 'Add Customer',
      icon: 'account-plus',
      color: theme.colors.tertiary,
      backgroundColor: theme.colors.tertiaryContainer,
    },
  ];

  // Security settings form fields
  const getSecurityFields = (): FormField[] => [
    {
      name: 'currentPassword',
      key: 'currentPassword',
      label: 'Current Password',
      type: 'text',
      value: data.currentPassword || '',
      onChange: (val: string) => console.log('currentPassword changed:', val),
      required: true,
    },
    {
      name: 'newPassword',
      key: 'newPassword',
      label: 'New Password',
      type: 'text',
      value: data.newPassword || '',
      onChange: (val: string) => console.log('newPassword changed:', val),
      required: true,
    },
    {
      name: 'confirmPassword',
      key: 'confirmPassword',
      label: 'Confirm New Password',
      type: 'text',
      value: data.confirmPassword || '',
      onChange: (val: string) => console.log('confirmPassword changed:', val),
      required: true,
    },
  ];

  // Profile form fields
  const getProfileFields = (): FormField[] => [
    {
      name: 'profileImage',
      key: 'profileImage',
      label: 'Profile Photo',
      type: 'image',
      value: data.profileImage || '',
      onChange: (val: string) => console.log('profileImage changed:', val),
      required: false,
    },
    {
      name: 'businessName',
      key: 'businessName',
      label: 'Business Name',
      type: 'text',
      value: data.businessName || data.storeName || '',
      onChange: (val: string) => console.log('businessName changed:', val),
      required: true,
    },
    {
      name: 'ownerName',
      key: 'ownerName',
      label: 'Owner Name',
      type: 'text',
      value: data.ownerName || '',
      onChange: (val: string) => console.log('ownerName changed:', val),
      required: true,
    },
    {
      name: 'email',
      key: 'email',
      label: 'Email Address',
      type: 'email',
      value: data.email || '',
      onChange: (val: string) => console.log('email changed:', val),
      required: true,
    },
    {
      name: 'phone',
      key: 'phone',
      label: 'Phone Number',
      type: 'phone',
      value: data.phone || '',
      onChange: (val: string) => console.log('phone changed:', val),
      required: true,
    },
    {
      name: 'address',
      key: 'address',
      label: 'Business Address',
      type: 'textarea',
      value: data.address || '',
      onChange: (val: string) => console.log('address changed:', val),
      required: false,
      multiline: true,
    },
    {
      name: 'taxRate',
      key: 'taxRate',
      label: 'Tax Rate (%)',
      type: 'numeric',
      value: data.taxRate ? (data.taxRate * 100).toString() : '8',
      onChange: (val: string) => console.log('taxRate changed:', val),
      required: true,
    },
    {
      name: 'currency',
      key: 'currency',
      label: 'Currency',
      type: 'select',
      value: data.currency || 'USD',
      onChange: (val: string) => console.log('currency changed:', val),
      required: true,
      options: [
        { label: 'US Dollar (USD)', value: 'USD' },
        { label: 'Euro (EUR)', value: 'EUR' },
        { label: 'British Pound (GBP)', value: 'GBP' },
        { label: 'South African Rand (ZAR)', value: 'ZAR' },
      ],
    },
    {
      name: 'notifications',
      key: 'notifications',
      label: 'Enable Notifications',
      type: 'switch',
      value: data.notifications !== false,
      onChange: (val: boolean) => console.log('notifications changed:', val),
      required: false,
    },
    {
      name: 'darkMode',
      key: 'darkMode',
      label: 'Dark Mode',
      type: 'switch',
      value: data.darkMode || false,
      onChange: (val: boolean) => console.log('darkMode changed:', val),
      required: false,
    },
    {
      name: 'autoBackup',
      key: 'autoBackup',
      label: 'Auto Backup',
      type: 'switch',
      value: data.autoBackup !== false,
      onChange: (val: boolean) => console.log('autoBackup changed:', val),
      required: false,
    },
  ];

  // Outlet form fields
  const getOutletFields = (): FormField[] => [
    {
      name: 'name',
      key: 'name',
      label: 'Outlet Name',
      type: 'text',
      value: data.name || '',
      onChange: (val: string) => console.log('outlet name changed:', val),
      required: true,
    },
    {
      name: 'address',
      key: 'address',
      label: 'Address',
      type: 'textarea',
      value: data.address || '',
      onChange: (val: string) => console.log('outlet address changed:', val),
      required: true,
    },
    {
      name: 'phone',
      key: 'phone',
      label: 'Phone',
      type: 'phone',
      value: data.phone || '',
      onChange: (val: string) => console.log('outlet phone changed:', val),
      required: false,
    },
    {
      name: 'email',
      key: 'email',
      label: 'Email',
      type: 'email',
      value: data.email || '',
      onChange: (val: string) => console.log('outlet email changed:', val),
      required: false,
    },
  ];

  const renderCustomerForm = () => {
    const customerFields = getCustomerFields();

    return (
      <ScrollView style={{ flex: 1, backgroundColor: theme.colors.background }}>
        <View style={{ padding: 16 }}>
          <Text variant="titleMedium" style={{ marginBottom: 24, color: theme.colors.primary, textAlign: 'center' }}>
            {mode === 'edit' ? 'Edit Customer' : 'Add New Customer'}
          </Text>

          {/* Fields without section wrapper */}
          <View style={{ marginBottom: 24 }}>
            {customerFields.map((field) => (
              <View key={field.key} style={{ marginBottom: 16 }}>
                <FormSection
                  fields={[field]}
                  errors={{}}
                  onFieldError={() => {}}
                  sectionTitle=""
                  style={{ elevation: 0, backgroundColor: 'transparent' }}
                />
              </View>
            ))}
          </View>

          <Button
            mode="contained"
            onPress={() => onSave?.(data)}
            style={{ marginTop: 8, marginBottom: 16 }}
            buttonColor={theme.colors.primary}
            contentStyle={{ paddingVertical: 8 }}
          >
            {mode === 'edit' ? 'Update Customer' : 'Save Customer'}
          </Button>
        </View>
      </ScrollView>
    );
  };

  const renderProductForm = () => (
    <ScrollView style={{ flex: 1 }}>
      <FormSection
        fields={getProductFields()}
        errors={{}}
        onFieldError={() => {}}
        sectionTitle="Product Details"
      />
      <Button
        mode="contained"
        onPress={() => onSave?.(data)}
        style={{ marginTop: 16 }}
        buttonColor={theme.colors.primary}
      >
        {mode === 'edit' ? 'Update Product' : 'Save Product'}
      </Button>
    </ScrollView>
  );

  const renderQuickActions = () => (
    <ScrollView style={{ flex: 1 }}>
      {getQuickActions().map((action) => (
        <Button
          key={action.id}
          mode="outlined"
          onPress={() => onAction?.(action.id)}
          style={{ marginVertical: 8 }}
          icon={action.icon}
        >
          {action.title}
        </Button>
      ))}
    </ScrollView>
  );

  const renderOrderDetails = () => (
    <ScrollView style={{ flex: 1 }}>
      <View style={{ padding: 16 }}>
        <Text variant="titleMedium" style={{ marginBottom: 8 }}>Order #{data.id || 'N/A'}</Text>
        <Text variant="bodyMedium" style={{ marginBottom: 4 }}>Customer: {data.customer || 'N/A'}</Text>
        <Text variant="bodyMedium" style={{ marginBottom: 4 }}>Total: ${data.total || '0.00'}</Text>
        <Text variant="bodyMedium" style={{ marginBottom: 4 }}>Status: {data.status || 'Unknown'}</Text>
        <Text variant="bodyMedium" style={{ marginBottom: 4 }}>Date: {data.date || 'N/A'}</Text>

        {data.items && data.items.length > 0 && (
          <View style={{ marginTop: 16 }}>
            <Text variant="titleSmall" style={{ marginBottom: 8 }}>Items:</Text>
            {data.items.map((item: any, index: number) => (
              <Text key={index} variant="bodySmall" style={{ marginBottom: 2 }}>
                • {item.name} x{item.quantity} - ${item.price}
              </Text>
            ))}
          </View>
        )}
      </View>
    </ScrollView>
  );

  const renderSecuritySettings = () => (
    <ScrollView style={{ flex: 1 }}>
      <FormSection
        fields={getSecurityFields()}
        errors={{}}
        onFieldError={() => {}}
        sectionTitle="Change Password"
      />
      <Button
        mode="contained"
        onPress={() => onSave?.(data)}
        style={{ marginTop: 16 }}
        buttonColor={theme.colors.primary}
      >
        Update Password
      </Button>
    </ScrollView>
  );

  const renderEditProfile = () => {
    const profileFields = getProfileFields();

    return (
      <ScrollView style={{ flex: 1, backgroundColor: theme.colors.background }}>
        <View style={{ padding: 16 }}>
          <Text variant="titleMedium" style={{ marginBottom: 24, color: theme.colors.primary, textAlign: 'center' }}>
            Edit Profile
          </Text>

          {/* Fields without section wrapper */}
          <View style={{ marginBottom: 24 }}>
            {profileFields.map((field) => (
              <View key={field.key} style={{ marginBottom: 16 }}>
                <FormSection
                  fields={[field]}
                  errors={{}}
                  onFieldError={() => {}}
                  sectionTitle=""
                  style={{ elevation: 0, backgroundColor: 'transparent' }}
                />
              </View>
            ))}
          </View>

          <Button
            mode="contained"
            onPress={() => onSave?.(data)}
            style={{ marginTop: 8, marginBottom: 16 }}
            buttonColor={theme.colors.primary}
            contentStyle={{ paddingVertical: 8 }}
          >
            Save Profile
          </Button>
        </View>
      </ScrollView>
    );
  };

  const renderOutletForm = () => (
    <ScrollView style={{ flex: 1 }}>
      <FormSection
        fields={getOutletFields()}
        errors={{}}
        onFieldError={() => {}}
        sectionTitle="Outlet Details"
      />
      <Button
        mode="contained"
        onPress={() => onSave?.(data)}
        style={{ marginTop: 16 }}
        buttonColor={theme.colors.primary}
      >
        {mode === 'edit' ? 'Update Outlet' : 'Save Outlet'}
      </Button>
    </ScrollView>
  );

  const renderPDFInvoice = () => (
    <ScrollView style={{ flex: 1 }}>
      <View style={{ padding: 16 }}>
        {/* Invoice Header */}
        <View style={{ marginBottom: 24 }}>
          <Text variant="headlineSmall" style={{ marginBottom: 8 }}>
            Invoice #{data?.id || 'N/A'}
          </Text>
          <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
            Date: {data?.date || new Date().toLocaleDateString()}
          </Text>
        </View>

        {/* Customer Info */}
        <View style={{ marginBottom: 24 }}>
          <Text variant="titleMedium" style={{ marginBottom: 8 }}>Customer Details</Text>
          <Text variant="bodyMedium">Name: {data?.customer || 'N/A'}</Text>
          <Text variant="bodyMedium">Phone: {data?.customerPhone || 'N/A'}</Text>
          <Text variant="bodyMedium">Email: {data?.customerEmail || 'N/A'}</Text>
        </View>

        {/* Order Items */}
        {data?.items && data.items.length > 0 && (
          <View style={{ marginBottom: 24 }}>
            <Text variant="titleMedium" style={{ marginBottom: 8 }}>Order Items</Text>
            {data.items.map((item: any, index: number) => (
              <View key={index} style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                paddingVertical: 8,
                borderBottomWidth: 1,
                borderBottomColor: theme.colors.outline + '20'
              }}>
                <View style={{ flex: 1 }}>
                  <Text variant="bodyMedium">{item.name}</Text>
                  <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                    Qty: {item.quantity} × ${item.price}
                  </Text>
                </View>
                <Text variant="bodyMedium" style={{ fontWeight: '600' }}>
                  ${(item.quantity * item.price).toFixed(2)}
                </Text>
              </View>
            ))}
          </View>
        )}

        {/* Total */}
        <View style={{
          marginBottom: 24,
          paddingTop: 16,
          borderTopWidth: 2,
          borderTopColor: theme.colors.primary
        }}>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            <Text variant="titleMedium">Total Amount:</Text>
            <Text variant="titleMedium" style={{ fontWeight: '700', color: theme.colors.primary }}>
              ${data?.total || '0.00'}
            </Text>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={{ flexDirection: 'row', gap: 12, marginTop: 16 }}>
          <Button
            mode="outlined"
            onPress={() => onAction?.('print')}
            style={{ flex: 1 }}
            icon="printer"
          >
            Print
          </Button>
          <Button
            mode="outlined"
            onPress={() => onAction?.('download')}
            style={{ flex: 1 }}
            icon="download"
          >
            Download
          </Button>
          <Button
            mode="contained"
            onPress={() => onAction?.('share')}
            style={{ flex: 1 }}
            icon="share"
            buttonColor={theme.colors.primary}
          >
            Share
          </Button>
        </View>
      </View>
    </ScrollView>
  );

  const renderPaymentAnalytics = () => (
    <ScrollView style={{ flex: 1 }}>
      <View style={{ padding: 16 }}>
        <Text variant="headlineSmall" style={{ marginBottom: 16 }}>Payment Analytics</Text>

        {/* Summary Cards */}
        <View style={{ marginBottom: 24 }}>
          <Text variant="titleMedium" style={{ marginBottom: 12 }}>Summary</Text>
          <View style={{ flexDirection: 'row', gap: 12 }}>
            <View style={{
              flex: 1,
              padding: 16,
              backgroundColor: theme.colors.primaryContainer,
              borderRadius: 12
            }}>
              <Text variant="bodySmall" style={{ color: theme.colors.onPrimaryContainer }}>
                Total Revenue
              </Text>
              <Text variant="titleLarge" style={{ color: theme.colors.onPrimaryContainer, fontWeight: '700' }}>
                ${data?.totalRevenue || '0.00'}
              </Text>
            </View>
            <View style={{
              flex: 1,
              padding: 16,
              backgroundColor: theme.colors.secondaryContainer,
              borderRadius: 12
            }}>
              <Text variant="bodySmall" style={{ color: theme.colors.onSecondaryContainer }}>
                Total Orders
              </Text>
              <Text variant="titleLarge" style={{ color: theme.colors.onSecondaryContainer, fontWeight: '700' }}>
                {data?.totalOrders || '0'}
              </Text>
            </View>
          </View>
        </View>

        {/* Export Button */}
        <Button
          mode="contained"
          onPress={() => onAction?.('export')}
          icon="download"
          buttonColor={theme.colors.primary}
        >
          Export Analytics
        </Button>
      </View>
    </ScrollView>
  );

  const renderProfitLoss = () => (
    <ScrollView style={{ flex: 1 }}>
      <View style={{ padding: 16 }}>
        <Text variant="headlineSmall" style={{ marginBottom: 16 }}>Profit & Loss Statement</Text>

        {/* Revenue Section */}
        <View style={{ marginBottom: 24 }}>
          <Text variant="titleMedium" style={{ marginBottom: 12 }}>Revenue</Text>
          <View style={{
            padding: 16,
            backgroundColor: theme.colors.surfaceVariant,
            borderRadius: 12
          }}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
              <Text variant="bodyMedium">Sales Revenue</Text>
              <Text variant="bodyMedium" style={{ fontWeight: '600' }}>
                ${data?.salesRevenue || '0.00'}
              </Text>
            </View>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text variant="titleMedium" style={{ fontWeight: '700' }}>Total Revenue</Text>
              <Text variant="titleMedium" style={{ fontWeight: '700', color: theme.colors.primary }}>
                ${data?.totalRevenue || '0.00'}
              </Text>
            </View>
          </View>
        </View>

        {/* Expenses Section */}
        <View style={{ marginBottom: 24 }}>
          <Text variant="titleMedium" style={{ marginBottom: 12 }}>Expenses</Text>
          <View style={{
            padding: 16,
            backgroundColor: theme.colors.surfaceVariant,
            borderRadius: 12
          }}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
              <Text variant="bodyMedium">Operating Expenses</Text>
              <Text variant="bodyMedium" style={{ fontWeight: '600' }}>
                ${data?.operatingExpenses || '0.00'}
              </Text>
            </View>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text variant="titleMedium" style={{ fontWeight: '700' }}>Total Expenses</Text>
              <Text variant="titleMedium" style={{ fontWeight: '700', color: theme.colors.error }}>
                ${data?.totalExpenses || '0.00'}
              </Text>
            </View>
          </View>
        </View>

        {/* Net Profit */}
        <View style={{
          padding: 16,
          backgroundColor: theme.colors.primaryContainer,
          borderRadius: 12,
          marginBottom: 24
        }}>
          <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
            <Text variant="titleLarge" style={{ fontWeight: '700', color: theme.colors.onPrimaryContainer }}>
              Net Profit
            </Text>
            <Text variant="titleLarge" style={{ fontWeight: '700', color: theme.colors.onPrimaryContainer }}>
              ${data?.netProfit || '0.00'}
            </Text>
          </View>
        </View>

        {/* Export Button */}
        <Button
          mode="contained"
          onPress={() => onAction?.('export')}
          icon="download"
          buttonColor={theme.colors.primary}
        >
          Export P&L Report
        </Button>
      </View>
    </ScrollView>
  );

  const renderTaxSummary = () => (
    <ScrollView style={{ flex: 1 }}>
      <View style={{ padding: 16 }}>
        <Text variant="headlineSmall" style={{ marginBottom: 16 }}>Tax Summary</Text>

        {/* Tax Breakdown */}
        <View style={{ marginBottom: 24 }}>
          <Text variant="titleMedium" style={{ marginBottom: 12 }}>Tax Breakdown</Text>

          <View style={{
            padding: 16,
            backgroundColor: theme.colors.surfaceVariant,
            borderRadius: 12,
            marginBottom: 12
          }}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 }}>
              <Text variant="bodyMedium">Sales Tax (15%)</Text>
              <Text variant="bodyMedium" style={{ fontWeight: '600' }}>
                ${data?.salesTax || '0.00'}
              </Text>
            </View>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text variant="bodyMedium">Income Tax</Text>
              <Text variant="bodyMedium" style={{ fontWeight: '600' }}>
                ${data?.incomeTax || '0.00'}
              </Text>
            </View>
          </View>

          <View style={{
            padding: 16,
            backgroundColor: theme.colors.primaryContainer,
            borderRadius: 12
          }}>
            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text variant="titleMedium" style={{ fontWeight: '700', color: theme.colors.onPrimaryContainer }}>
                Total Tax Liability
              </Text>
              <Text variant="titleMedium" style={{ fontWeight: '700', color: theme.colors.onPrimaryContainer }}>
                ${data?.totalTaxLiability || '0.00'}
              </Text>
            </View>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={{ flexDirection: 'row', gap: 12 }}>
          <Button
            mode="outlined"
            onPress={() => onAction?.('export')}
            style={{ flex: 1 }}
            icon="download"
          >
            Export
          </Button>
          <Button
            mode="contained"
            onPress={() => onAction?.('file-return')}
            style={{ flex: 1 }}
            icon="file-document"
            buttonColor={theme.colors.primary}
          >
            File Return
          </Button>
        </View>
      </View>
    </ScrollView>
  );

  // Render appropriate content based on type
  switch (type) {
    case 'add-customer':
    case 'edit-customer':
      return renderCustomerForm();

    case 'add-product':
    case 'edit-product':
      return renderProductForm();

    case 'quick-actions':
      return renderQuickActions();

    case 'order-details':
      return renderOrderDetails();

    case 'security-settings':
      return renderSecuritySettings();

    case 'edit-profile':
      return renderEditProfile();

    case 'outlet':
      return renderOutletForm();

    case 'pdf-invoice':
      return renderPDFInvoice();

    case 'payment-analytics':
      return renderPaymentAnalytics();

    case 'profit-loss':
      return renderProfitLoss();

    case 'tax-summary':
      return renderTaxSummary();

    default:
      return (
        <ScrollView style={{ flex: 1 }}>
          <View style={{ padding: 16 }}>
            <Text>Content for {type} not implemented yet</Text>
          </View>
        </ScrollView>
      );
  }
};

export default BottomSheetContentFactory; 