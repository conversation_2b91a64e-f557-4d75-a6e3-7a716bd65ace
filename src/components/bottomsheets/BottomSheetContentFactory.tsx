import React from 'react';
import { ScrollView } from 'react-native';
import { Button, Text } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { BottomSheetType } from './UnifiedBottomSheetTypes';
import FormSection from '../forms/FormSection';
import { FormField } from './UnifiedBottomSheetTypes';

interface BottomSheetContentFactoryProps {
  type: BottomSheetType;
  data: any;
  mode: 'add' | 'edit' | 'view';
  onSave?: (data: any) => void;
  onAction?: (actionId: string) => void;
}

const BottomSheetContentFactory: React.FC<BottomSheetContentFactoryProps> = ({
  type,
  data,
  mode,
  onSave,
  onAction,
}) => {
  const theme = useTheme();

  // Customer form fields
  const getCustomerFields = (): FormField[] => [
    {
      name: 'name',
      key: 'name',
      label: 'Name',
      type: 'text',
      value: data.name || '',
      onChange: (val: string) => console.log('name changed:', val),
      required: true,
    },
    {
      name: 'phone',
      key: 'phone',
      label: 'Phone',
      type: 'phone',
      value: data.phone || '',
      onChange: (val: string) => console.log('phone changed:', val),
      required: true,
    },
    {
      name: 'email',
      key: 'email',
      label: 'Email',
      type: 'email',
      value: data.email || '',
      onChange: (val: string) => console.log('email changed:', val),
      required: false,
    },
  ];

  // Product form fields
  const getProductFields = (): FormField[] => [
    {
      name: 'name',
      key: 'name',
      label: 'Product Name',
      type: 'text',
      value: data.name || '',
      onChange: (val: string) => console.log('name changed:', val),
      required: true,
    },
    {
      name: 'price',
      key: 'price',
      label: 'Price',
      type: 'number',
      value: data.price || '',
      onChange: (val: string) => console.log('price changed:', val),
      required: true,
    },
    {
      name: 'stock',
      key: 'stock',
      label: 'Stock',
      type: 'number',
      value: data.stock || '',
      onChange: (val: string) => console.log('stock changed:', val),
      required: true,
    },
  ];

  // Quick actions
  const getQuickActions = () => [
    {
      id: 'add-product',
      title: 'Add Product',
      icon: 'plus-circle',
      color: theme.colors.primary,
      backgroundColor: theme.colors.primaryContainer,
    },
    {
      id: 'add-order',
      title: 'New Order',
      icon: 'clipboard-plus',
      color: theme.colors.secondary,
      backgroundColor: theme.colors.secondaryContainer,
    },
    {
      id: 'add-customer',
      title: 'Add Customer',
      icon: 'account-plus',
      color: theme.colors.tertiary,
      backgroundColor: theme.colors.tertiaryContainer,
    },
  ];

  const renderCustomerForm = () => (
    <ScrollView style={{ flex: 1 }}>
      <FormSection
        fields={getCustomerFields()}
        errors={{}}
        onFieldError={() => {}}
        sectionTitle="Customer Details"
      />
      <Button
        mode="contained"
        onPress={() => onSave?.(data)}
        style={{ marginTop: 16 }}
        buttonColor={theme.colors.primary}
      >
        {mode === 'edit' ? 'Update Customer' : 'Save Customer'}
      </Button>
    </ScrollView>
  );

  const renderProductForm = () => (
    <ScrollView style={{ flex: 1 }}>
      <FormSection
        fields={getProductFields()}
        errors={{}}
        onFieldError={() => {}}
        sectionTitle="Product Details"
      />
      <Button
        mode="contained"
        onPress={() => onSave?.(data)}
        style={{ marginTop: 16 }}
        buttonColor={theme.colors.primary}
      >
        {mode === 'edit' ? 'Update Product' : 'Save Product'}
      </Button>
    </ScrollView>
  );

  const renderQuickActions = () => (
    <ScrollView style={{ flex: 1 }}>
      {getQuickActions().map((action) => (
        <Button
          key={action.id}
          mode="outlined"
          onPress={() => onAction?.(action.id)}
          style={{ marginVertical: 8 }}
          icon={action.icon}
        >
          {action.title}
        </Button>
      ))}
    </ScrollView>
  );

  const renderOrderDetails = () => (
    <ScrollView style={{ flex: 1 }}>
      <Text variant="titleMedium">Order #{data.id}</Text>
      <Text variant="bodyMedium">Customer: {data.customer}</Text>
      <Text variant="bodyMedium">Total: ${data.total}</Text>
      <Text variant="bodyMedium">Status: {data.status}</Text>
    </ScrollView>
  );

  // Render appropriate content based on type
  switch (type) {
    case 'add-customer':
    case 'edit-customer':
      return renderCustomerForm();
    
    case 'add-product':
    case 'edit-product':
      return renderProductForm();
    
    case 'quick-actions':
      return renderQuickActions();
    
    case 'order-details':
      return renderOrderDetails();
    
    default:
      return (
        <ScrollView style={{ flex: 1 }}>
          <Text>Content for {type} not implemented yet</Text>
        </ScrollView>
      );
  }
};

export default BottomSheetContentFactory; 