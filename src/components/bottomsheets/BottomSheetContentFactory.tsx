import React from 'react';
import { <PERSON><PERSON>View, View } from 'react-native';
import { <PERSON><PERSON>, Text, Card, IconButton } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { BottomSheetType } from './UnifiedBottomSheetTypes';
import FormSection from '../forms/FormSection';
import { FormField } from './UnifiedBottomSheetTypes';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

interface BottomSheetContentFactoryProps {
  type: BottomSheetType;
  data: any;
  mode: 'add' | 'edit' | 'view';
  onSave?: (data: any) => void;
  onAction?: (actionId: string) => void;
}

const BottomSheetContentFactory: React.FC<BottomSheetContentFactoryProps> = ({
  type,
  data,
  mode,
  onSave,
  onAction,
}) => {
  const theme = useTheme();

  // Customer form fields
  const getCustomerFields = (): FormField[] => [
    {
      name: 'name',
      key: 'name',
      label: 'Name',
      type: 'text',
      value: data.name || '',
      onChange: (val: string) => console.log('name changed:', val),
      required: true,
    },
    {
      name: 'phone',
      key: 'phone',
      label: 'Phone',
      type: 'phone',
      value: data.phone || '',
      onChange: (val: string) => console.log('phone changed:', val),
      required: true,
    },
    {
      name: 'email',
      key: 'email',
      label: 'Email',
      type: 'email',
      value: data.email || '',
      onChange: (val: string) => console.log('email changed:', val),
      required: false,
    },
  ];

  // Product form fields
  const getProductFields = (): FormField[] => [
    {
      name: 'name',
      key: 'name',
      label: 'Product Name',
      type: 'text',
      value: data.name || '',
      onChange: (val: string) => console.log('name changed:', val),
      required: true,
    },
    {
      name: 'price',
      key: 'price',
      label: 'Price',
      type: 'number',
      value: data.price || '',
      onChange: (val: string) => console.log('price changed:', val),
      required: true,
    },
    {
      name: 'stock',
      key: 'stock',
      label: 'Stock',
      type: 'number',
      value: data.stock || '',
      onChange: (val: string) => console.log('stock changed:', val),
      required: true,
    },
  ];

  // Quick actions
  const getQuickActions = () => [
    {
      id: 'add-product',
      title: 'Add Product',
      icon: 'plus-circle',
      color: theme.colors.primary,
      backgroundColor: theme.colors.primaryContainer,
    },
    {
      id: 'add-order',
      title: 'New Order',
      icon: 'clipboard-plus',
      color: theme.colors.secondary,
      backgroundColor: theme.colors.secondaryContainer,
    },
    {
      id: 'add-customer',
      title: 'Add Customer',
      icon: 'account-plus',
      color: theme.colors.tertiary,
      backgroundColor: theme.colors.tertiaryContainer,
    },
  ];

  // Security settings form fields
  const getSecurityFields = (): FormField[] => [
    {
      name: 'currentPassword',
      key: 'currentPassword',
      label: 'Current Password',
      type: 'text',
      value: data.currentPassword || '',
      onChange: (val: string) => console.log('currentPassword changed:', val),
      required: true,
    },
    {
      name: 'newPassword',
      key: 'newPassword',
      label: 'New Password',
      type: 'text',
      value: data.newPassword || '',
      onChange: (val: string) => console.log('newPassword changed:', val),
      required: true,
    },
    {
      name: 'confirmPassword',
      key: 'confirmPassword',
      label: 'Confirm New Password',
      type: 'text',
      value: data.confirmPassword || '',
      onChange: (val: string) => console.log('confirmPassword changed:', val),
      required: true,
    },
  ];

  // Profile form fields
  const getProfileFields = (): FormField[] => [
    {
      name: 'businessName',
      key: 'businessName',
      label: 'Business Name',
      type: 'text',
      value: data.businessName || '',
      onChange: (val: string) => console.log('businessName changed:', val),
      required: true,
    },
    {
      name: 'ownerName',
      key: 'ownerName',
      label: 'Owner Name',
      type: 'text',
      value: data.ownerName || '',
      onChange: (val: string) => console.log('ownerName changed:', val),
      required: true,
    },
    {
      name: 'email',
      key: 'email',
      label: 'Email',
      type: 'email',
      value: data.email || '',
      onChange: (val: string) => console.log('email changed:', val),
      required: true,
    },
    {
      name: 'phone',
      key: 'phone',
      label: 'Phone',
      type: 'phone',
      value: data.phone || '',
      onChange: (val: string) => console.log('phone changed:', val),
      required: false,
    },
  ];

  // Outlet form fields
  const getOutletFields = (): FormField[] => [
    {
      name: 'name',
      key: 'name',
      label: 'Outlet Name',
      type: 'text',
      value: data.name || '',
      onChange: (val: string) => console.log('outlet name changed:', val),
      required: true,
    },
    {
      name: 'address',
      key: 'address',
      label: 'Address',
      type: 'textarea',
      value: data.address || '',
      onChange: (val: string) => console.log('outlet address changed:', val),
      required: true,
    },
    {
      name: 'phone',
      key: 'phone',
      label: 'Phone',
      type: 'phone',
      value: data.phone || '',
      onChange: (val: string) => console.log('outlet phone changed:', val),
      required: false,
    },
    {
      name: 'email',
      key: 'email',
      label: 'Email',
      type: 'email',
      value: data.email || '',
      onChange: (val: string) => console.log('outlet email changed:', val),
      required: false,
    },
  ];

  const renderCustomerForm = () => (
    <ScrollView style={{ flex: 1 }}>
      <FormSection
        fields={getCustomerFields()}
        errors={{}}
        onFieldError={() => {}}
        sectionTitle="Customer Details"
      />
      <Button
        mode="contained"
        onPress={() => onSave?.(data)}
        style={{ marginTop: 16 }}
        buttonColor={theme.colors.primary}
      >
        {mode === 'edit' ? 'Update Customer' : 'Save Customer'}
      </Button>
    </ScrollView>
  );

  const renderProductForm = () => (
    <ScrollView style={{ flex: 1 }}>
      <FormSection
        fields={getProductFields()}
        errors={{}}
        onFieldError={() => {}}
        sectionTitle="Product Details"
      />
      <Button
        mode="contained"
        onPress={() => onSave?.(data)}
        style={{ marginTop: 16 }}
        buttonColor={theme.colors.primary}
      >
        {mode === 'edit' ? 'Update Product' : 'Save Product'}
      </Button>
    </ScrollView>
  );

  const renderQuickActions = () => (
    <ScrollView style={{ flex: 1 }}>
      {getQuickActions().map((action) => (
        <Button
          key={action.id}
          mode="outlined"
          onPress={() => onAction?.(action.id)}
          style={{ marginVertical: 8 }}
          icon={action.icon}
        >
          {action.title}
        </Button>
      ))}
    </ScrollView>
  );

  const renderOrderDetails = () => (
    <ScrollView style={{ flex: 1 }}>
      <View style={{ padding: 16 }}>
        <Text variant="titleMedium" style={{ marginBottom: 8 }}>Order #{data.id || 'N/A'}</Text>
        <Text variant="bodyMedium" style={{ marginBottom: 4 }}>Customer: {data.customer || 'N/A'}</Text>
        <Text variant="bodyMedium" style={{ marginBottom: 4 }}>Total: ${data.total || '0.00'}</Text>
        <Text variant="bodyMedium" style={{ marginBottom: 4 }}>Status: {data.status || 'Unknown'}</Text>
        <Text variant="bodyMedium" style={{ marginBottom: 4 }}>Date: {data.date || 'N/A'}</Text>

        {data.items && data.items.length > 0 && (
          <View style={{ marginTop: 16 }}>
            <Text variant="titleSmall" style={{ marginBottom: 8 }}>Items:</Text>
            {data.items.map((item, index) => (
              <Text key={index} variant="bodySmall" style={{ marginBottom: 2 }}>
                • {item.name} x{item.quantity} - ${item.price}
              </Text>
            ))}
          </View>
        )}
      </View>
    </ScrollView>
  );

  const renderSecuritySettings = () => (
    <ScrollView style={{ flex: 1 }}>
      <FormSection
        fields={getSecurityFields()}
        errors={{}}
        onFieldError={() => {}}
        sectionTitle="Change Password"
      />
      <Button
        mode="contained"
        onPress={() => onSave?.(data)}
        style={{ marginTop: 16 }}
        buttonColor={theme.colors.primary}
      >
        Update Password
      </Button>
    </ScrollView>
  );

  const renderEditProfile = () => (
    <ScrollView style={{ flex: 1 }}>
      <FormSection
        fields={getProfileFields()}
        errors={{}}
        onFieldError={() => {}}
        sectionTitle="Profile Information"
      />
      <Button
        mode="contained"
        onPress={() => onSave?.(data)}
        style={{ marginTop: 16 }}
        buttonColor={theme.colors.primary}
      >
        Save Profile
      </Button>
    </ScrollView>
  );

  const renderOutletForm = () => (
    <ScrollView style={{ flex: 1 }}>
      <FormSection
        fields={getOutletFields()}
        errors={{}}
        onFieldError={() => {}}
        sectionTitle="Outlet Details"
      />
      <Button
        mode="contained"
        onPress={() => onSave?.(data)}
        style={{ marginTop: 16 }}
        buttonColor={theme.colors.primary}
      >
        {mode === 'edit' ? 'Update Outlet' : 'Save Outlet'}
      </Button>
    </ScrollView>
  );

  // Render appropriate content based on type
  switch (type) {
    case 'add-customer':
    case 'edit-customer':
      return renderCustomerForm();

    case 'add-product':
    case 'edit-product':
      return renderProductForm();

    case 'quick-actions':
      return renderQuickActions();

    case 'order-details':
      return renderOrderDetails();

    case 'security-settings':
      return renderSecuritySettings();

    case 'edit-profile':
      return renderEditProfile();

    case 'outlet':
      return renderOutletForm();

    default:
      return (
        <ScrollView style={{ flex: 1 }}>
          <View style={{ padding: 16 }}>
            <Text>Content for {type} not implemented yet</Text>
          </View>
        </ScrollView>
      );
  }
};

export default BottomSheetContentFactory; 