import React, { useReducer, forwardRef, useImperativeHandle } from 'react';
import { View } from 'react-native';
import { Text } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { BottomSheetState, BottomSheetAction, BottomSheetType } from './UnifiedBottomSheetTypes';
import { bottomSheetReducer, initialState } from './UnifiedBottomSheetReducer';
import BottomSheet from './BottomSheet';

interface UnifiedBottomSheetProps {
  onSave?: (data: any) => void;
  onClose?: () => void;
  onAction?: (actionId: string) => void;
}

export interface UnifiedBottomSheetRef {
  open: (type: BottomSheetType, data?: any, mode?: 'add' | 'edit' | 'view') => void;
  close: () => void;
}

const UnifiedBottomSheet = forwardRef<UnifiedBottomSheetRef, UnifiedBottomSheetProps>(
  ({ onSave, onClose, onAction }, ref) => {
    const theme = useTheme();
    const [state, dispatch] = useReducer(bottomSheetReducer, initialState);

    useImperativeHandle(ref, () => ({
      open: (type: BottomSheetType, data = {}, mode = 'view') => {
        dispatch({
          type: 'OPEN',
          payload: { type, data, mode }
        });
      },
      close: () => {
        dispatch({ type: 'CLOSE' });
        onClose?.();
      }
    }));

    const handleClose = () => {
      dispatch({ type: 'CLOSE' });
      onClose?.();
    };

    if (!state.isVisible || !state.type) {
      return null;
    }

    return (
      <BottomSheet
        title={state.title}
        snapPoints={state.snapPoints}
        onClose={handleClose}
      >
        <View style={{ padding: 16 }}>
          <Text>Content for {state.type}</Text>
          <Text>Mode: {state.mode}</Text>
        </View>
      </BottomSheet>
    );
  }
);

UnifiedBottomSheet.displayName = 'UnifiedBottomSheet';

export default UnifiedBottomSheet; 