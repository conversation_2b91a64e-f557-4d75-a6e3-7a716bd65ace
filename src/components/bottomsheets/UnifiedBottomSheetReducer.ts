import { BottomSheetState, BottomSheetAction } from './UnifiedBottomSheetTypes';

export const initialState: BottomSheetState = {
  isVisible: false,
  type: null,
  title: '',
  snapPoints: ['50%'],
  data: {},
  mode: 'view',
};

export const bottomSheetReducer = (
  state: BottomSheetState,
  action: BottomSheetAction
): BottomSheetState => {
  switch (action.type) {
    case 'OPEN':
      return {
        ...state,
        isVisible: true,
        type: action.payload.type,
        title: action.payload.title || 'Bottom Sheet',
        snapPoints: action.payload.snapPoints || ['50%'],
        data: action.payload.data || {},
        mode: action.payload.mode || 'view',
      };

    case 'CLOSE':
      return {
        ...state,
        isVisible: false,
        type: null,
        title: '',
        snapPoints: ['50%'],
        data: {},
        mode: 'view',
      };

    case 'UPDATE_DATA':
      return {
        ...state,
        data: { ...state.data, ...action.payload },
      };

    case 'SET_MODE':
      return {
        ...state,
        mode: action.payload,
      };

    default:
      return state;
  }
}; 
 