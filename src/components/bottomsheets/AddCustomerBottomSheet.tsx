import React, { forwardRef, useState, useImperativeHandle, useRef, useMemo, useCallback } from 'react';
import { ScrollView, View, StyleSheet } from 'react-native';
import { Button, Text, Portal } from 'react-native-paper';
import BottomSheet, { BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';

import { useTheme } from '../../context/ThemeContext';
import TextInput from '../ui/TextInput';

interface CustomerData {
  name: string;
  email: string;
  phone: string;
  address: string;
  gender: string;
}

interface AddCustomerBottomSheetProps {
  onSave?: (data: CustomerData) => void;
  onClose?: () => void;
  isEditing?: boolean;
  initialData?: Partial<CustomerData>;
}

const AddCustomerBottomSheet = forwardRef<any, AddCustomerBottomSheetProps>(({ onSave, onClose, isEditing = false, initialData = {} }, ref) => {
  const theme = useTheme();
  const bottomSheetRef = useRef<BottomSheet>(null);
  const [customerData, setCustomerData] = useState<CustomerData>({
    name: initialData.name || '',
    email: initialData.email || '',
    phone: initialData.phone || '',
    address: initialData.address || '',
    gender: initialData.gender || 'male',
  });
  const [errors, setErrors] = useState<Partial<Record<keyof CustomerData, string>>>({});

  const snapPoints = useMemo(() => ['60%', '90%'], []);

  useImperativeHandle(ref, () => ({
    expand: (data: Partial<CustomerData> = {}) => {
      setCustomerData({
        name: data.name || '',
        email: data.email || '',
        phone: data.phone || '',
        address: data.address || '',
        gender: data.gender || 'male',
      });
      bottomSheetRef.current?.expand();
    },
    close: () => bottomSheetRef.current?.close(),
  }));

  const handleFieldChange = (field: keyof CustomerData, value: string) => {
    setCustomerData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateField = (field: keyof CustomerData, value: string): string => {
    switch (field) {
      case 'name':
        return value.trim() !== '' ? '' : 'Name is required';
      case 'phone':
        return value && /^\d{8,15}$/.test(value) ? '' : 'Invalid phone number';
      case 'email':
        return value === '' || (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) ? '' : 'Invalid email address';
      default:
        return '';
    }
  };

  const handleSave = () => {
    const newErrors: Partial<Record<keyof CustomerData, string>> = {};
    Object.keys(customerData).forEach((key) => {
      const fieldKey = key as keyof CustomerData;
      const error = validateField(fieldKey, customerData[fieldKey]);
      if (error) {
        newErrors[fieldKey] = error;
      }
    });

    setErrors(newErrors);
    if (Object.keys(newErrors).length > 0) {
      return;
    }

    if (onSave) onSave(customerData);
    bottomSheetRef.current?.close();
  };

  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="close"
      />
    ),
    []
  );

  return (
    <Portal>
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        keyboardBehavior="extend"
        keyboardBlurBehavior="restore"
        android_keyboardInputMode="adjustResize"
        enablePanDownToClose={true}
        backgroundStyle={{ backgroundColor: theme.colors.surface }}
        handleIndicatorStyle={{ backgroundColor: theme.colors.onSurfaceVariant }}
      >
        <BottomSheetView style={styles.container}>
          {/* Scrollable Content */}
          <ScrollView 
            style={styles.scrollContent} 
            contentContainerStyle={styles.scrollContentContainer}
            showsVerticalScrollIndicator={false}
          >
            <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Customer Information
            </Text>
            
            {/* Name */}
            <TextInput
              label="Full Name"
              value={customerData.name}
              onChangeText={(val: string) => handleFieldChange('name', val)}
              type="text"
              required
              minLength={1}
              maxLength={100}
              validate={() => ''}
              error={errors.name}
              onBlur={() => {}}
              style={styles.input}
            />

            {/* Phone */}
            <TextInput
              label="Phone Number"
              value={customerData.phone}
              onChangeText={(val: string) => handleFieldChange('phone', val)}
              type="phone"
              required
              minLength={1}
              maxLength={15}
              validate={() => ''}
              error={errors.phone}
              onBlur={() => {}}
              style={styles.input}
            />

            {/* Email */}
            <TextInput
              label="Email Address (optional)"
              value={customerData.email}
              onChangeText={(val: string) => handleFieldChange('email', val)}
              type="email"
              minLength={0}
              maxLength={100}
              validate={() => ''}
              error={errors.email}
              onBlur={() => {}}
              style={styles.input}
            />

            {/* Gender */}
            <Text style={[styles.genderLabel, { color: theme.colors.onSurfaceVariant }]}>
              Gender
            </Text>
            <View style={styles.genderButtons}>
              <Button
                mode={customerData.gender === 'male' ? 'contained' : 'outlined'}
                onPress={() => handleFieldChange('gender', 'male')}
                style={styles.genderButton}
                buttonColor={theme.colors.primary}
              >
                Male
              </Button>
              <Button
                mode={customerData.gender === 'female' ? 'contained' : 'outlined'}
                onPress={() => handleFieldChange('gender', 'female')}
                style={styles.genderButton}
                buttonColor={theme.colors.primary}
              >
                Female
              </Button>
              <Button
                mode={customerData.gender === 'other' ? 'contained' : 'outlined'}
                onPress={() => handleFieldChange('gender', 'other')}
                style={styles.genderButton}
                buttonColor={theme.colors.primary}
              >
                Other
              </Button>
            </View>

            {/* Address */}
            <TextInput
              label="Address (optional)"
              value={customerData.address}
              onChangeText={(val: string) => handleFieldChange('address', val)}
              type="text"
              minLength={0}
              maxLength={500}
              validate={() => ''}
              error=""
              onBlur={() => {}}
              style={styles.input}
              multiline
              numberOfLines={3}
            />
          </ScrollView>

          {/* Sticky Save Button */}
          <View style={styles.stickyButtonContainer}>
            <Button
              mode="contained"
              onPress={handleSave}
              style={styles.saveButton}
              buttonColor={theme.colors.primary}
            >
              {isEditing ? 'Update Customer' : 'Save Customer'}
            </Button>
          </View>
        </BottomSheetView>
      </BottomSheet>
    </Portal>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flex: 1,
  },
  scrollContentContainer: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 24,
  },
  input: {
    marginBottom: 20,
  },
  genderLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
    marginTop: 20,
  },
  genderButtons: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 20,
  },
  genderButton: {
    flex: 1,
  },
  stickyButtonContainer: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
    backgroundColor: 'rgba(255,255,255,0.95)',
  },
  saveButton: {
    paddingVertical: 8,
  },
});

AddCustomerBottomSheet.displayName = 'AddCustomerBottomSheet';

export default AddCustomerBottomSheet; 