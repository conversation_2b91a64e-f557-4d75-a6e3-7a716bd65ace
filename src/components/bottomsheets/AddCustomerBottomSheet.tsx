import React, { forwardRef, useState, useImperativeHandle, useRef } from 'react';
import { ScrollView } from 'react-native';
import { Button } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { FormField } from '../../types';
import FormSection from '../forms/FormSection';

import BottomSheet from './BottomSheet';

interface CustomerData {
  name: string;
  email: string;
  phone: string;
  address: string;
  gender: string;
}

interface AddCustomerBottomSheetProps {
  onSave?: (data: CustomerData) => void;
  onClose?: () => void;
  isEditing?: boolean;
  initialData?: Partial<CustomerData>;
}

const AddCustomerBottomSheet = forwardRef<any, AddCustomerBottomSheetProps>(({ onSave, onClose, isEditing = false, initialData = {} }, ref) => {
  const theme = useTheme();
  const bottomSheetRef = useRef(null);
  const [customerData, setCustomerData] = useState<CustomerData>({
    name: initialData.name || '',
    email: initialData.email || '',
    phone: initialData.phone || '',
    address: initialData.address || '',
    gender: initialData.gender || 'male',
  });
  const [errors, setErrors] = useState<Partial<Record<keyof CustomerData, string>>>({});

  useImperativeHandle(ref, () => ({
    expand: (data: Partial<CustomerData> = {}) => {
      setCustomerData({
        name: data.name || '',
        email: data.email || '',
        phone: data.phone || '',
        address: data.address || '',
        gender: data.gender || 'male',
      });
      (bottomSheetRef.current as any)?.expand();
    },
    close: () => (bottomSheetRef.current as any)?.close(),
  }));

  const fields: FormField[] = [
    {
      name: 'name',
      key: 'name',
      label: 'Name',
      type: 'text' as const,
      value: customerData.name,
      onChange: (val: string) => setCustomerData(prev => ({ ...prev, name: val })),
      required: true,
      validation: (val: string) => val.trim() !== '' ? '' : 'Name is required',
    },
    {
      name: 'phone',
      key: 'phone',
      label: 'Phone',
      type: 'phone' as const,
      value: customerData.phone,
      onChange: (val: string) => setCustomerData(prev => ({ ...prev, phone: val })),
      required: true,
      validation: (val: string) => val && /^\d{8,15}$/.test(val) ? '' : 'Invalid phone number',
    },
    {
      name: 'email',
      key: 'email',
      label: 'Email',
      type: 'email' as const,
      value: customerData.email,
      onChange: (val: string) => setCustomerData(prev => ({ ...prev, email: val })),
      required: false,
      validation: (val: string) => val === '' || (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val)) ? '' : 'Invalid email address',
    },
    {
      name: 'address',
      key: 'address',
      label: 'Address',
      type: 'text' as const,
      value: customerData.address,
      onChange: (val: string) => setCustomerData(prev => ({ ...prev, address: val })),
      required: false,
    },
    {
      name: 'gender',
      key: 'gender',
      label: 'Gender',
      type: 'select' as const,
      options: [
        { label: 'Male', value: 'male' },
        { label: 'Female', value: 'female' },
      ],
      value: customerData.gender,
      onChange: (val: string) => setCustomerData(prev => ({ ...prev, gender: val })),
      required: false,
    },
  ];

  const handleSave = () => {
    const hasErrors = Object.values(errors).some(error => error);
    if (hasErrors) return;
    if (onSave) onSave(customerData);
    (bottomSheetRef.current as any)?.close();
  };

  return (
    <BottomSheet
      ref={bottomSheetRef}
      title={isEditing ? 'Edit Customer' : 'Add Customer'}
      onClose={onClose}
      snapPoints={["60%", "90%"]}
    >
      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
        <FormSection
          fields={fields}
          errors={errors}
          onFieldError={(key, error) => setErrors(prev => ({ ...prev, [key]: error }))}
          sectionTitle="Customer Details"
        />
        <Button
          mode="contained"
          onPress={handleSave}
          style={{ marginTop: 16 }}
          buttonColor={theme.colors.primary}
        >
          {isEditing ? 'Update Customer' : 'Save Customer'}
        </Button>
      </ScrollView>
    </BottomSheet>
  );
});

AddCustomerBottomSheet.displayName = 'AddCustomerBottomSheet';

export default AddCustomerBottomSheet; 