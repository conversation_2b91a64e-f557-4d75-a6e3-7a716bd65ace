/**
 * BottomSheet - Reusable bottom sheet component
 * Uses @gorhom/bottom-sheet for proper keyboard handling
 */

import GorhomBottomSheet, { BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import React, { forwardRef, useImperativeHandle, useRef, useCallback, useMemo } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Portal } from 'react-native-paper';


import { useTheme } from '../../context/ThemeContext';
import { SPACING } from '../../theme/theme';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

interface BottomSheetProps {
  children: React.ReactNode;
  title?: string;
  snapPoints?: string[];
  onClose?: () => void;
  showCloseButton?: boolean;
}

export interface BottomSheetRef {
  open: () => void;
  close: () => void;
  expand: () => void;
  collapse: () => void;
}

const BottomSheet = forwardRef<BottomSheetRef, BottomSheetProps>(({
  children,
  title,
  snapPoints = ['50%'],
  onClose,
  showCloseButton = true,
}, ref) => {
  const theme = useTheme();
  const bottomSheetRef = useRef<GorhomBottomSheet>(null);

  // Convert snap points to the format expected by @gorhom/bottom-sheet
  const convertedSnapPoints = useMemo(() => {
    return snapPoints.map(snapPoint => {
      if (snapPoint.endsWith('%')) {
        const percentage = parseInt(snapPoint.slice(0, -1));
        return `${percentage}%`;
      }
      return snapPoint;
    });
  }, [snapPoints]);

  const handleSheetChanges = useCallback(() => {
    // Handle sheet changes if needed
  }, []);

  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="close"
      />
    ),
    []
  );

  useImperativeHandle(ref, () => ({
    open: () => bottomSheetRef.current?.expand(),
    close: () => {
      bottomSheetRef.current?.close();
      onClose?.();
    },
    expand: () => bottomSheetRef.current?.expand(),
    collapse: () => bottomSheetRef.current?.collapse(),
  }));

  return (
    <Portal>
      <GorhomBottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={convertedSnapPoints}
        onChange={handleSheetChanges}
        backdropComponent={renderBackdrop}
        keyboardBehavior="extend"
        keyboardBlurBehavior="restore"
        android_keyboardInputMode="adjustResize"
        enablePanDownToClose={true}
        backgroundStyle={{ backgroundColor: theme.colors.surface }}
        handleIndicatorStyle={{ backgroundColor: theme.colors.onSurfaceVariant }}
        style={styles.bottomSheet}
      >
        <BottomSheetView style={styles.container}>
          {/* Header */}
          {title && (
            <View style={styles.header}>
              <Text style={[styles.title, { color: theme.colors.onSurface }]}>
                {title}
              </Text>
              {showCloseButton && (
                <View style={styles.closeButton}>
                  <PhosphorIcon 
                    name="x" 
                    size={24} 
                    color={theme.colors.onSurfaceVariant} 
                  />
                </View>
              )}
            </View>
          )}

          {/* Content */}
          <View style={styles.content}>
            {children}
          </View>
        </BottomSheetView>
      </GorhomBottomSheet>
    </Portal>
  );
});

const styles = StyleSheet.create({
  bottomSheet: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
  },
  closeButton: {
    padding: SPACING.xs,
  },
  content: {
    flex: 1,
  },
});

BottomSheet.displayName = 'BottomSheet';

export default BottomSheet;
