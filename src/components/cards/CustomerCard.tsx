import React, { useMemo } from 'react';
import { View, StyleSheet, TouchableOpacity, Pressable, Platform } from 'react-native';
import { Text, Surface } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';


interface CustomerCardProps {
  customer: {
    id: string | number;
    name: string;
    email?: string;
    phone?: string;
    totalSpent?: number;
    totalOrders?: number;
    activeOrders?: number;
    isVIP?: boolean;
    date?: string;
    createdAt?: string;
    lastOrderDate?: Date | null;
    [key: string]: any;
  };
  onPress: (customer: CustomerCardProps['customer']) => void;
  onLongPress?: (customer: CustomerCardProps['customer']) => void;
  onCheckboxPress: () => void;
  selected?: boolean;
  showSelectionIndicator?: boolean;
}

const CustomerCard = React.memo<CustomerCardProps>(({ 
  customer, 
  onPress, 
  onLongPress, 
  onCheckboxPress,
  selected = false, 
  showSelectionIndicator = false 
}) => {
  const theme = useTheme();

  // Memoized calculations to prevent recalculation on every render
  const cardStyles = useMemo(() => ({
    backgroundColor: selected ? theme.colors.primaryContainer : theme.colors.surface,
  }), [selected, theme.colors.primaryContainer, theme.colors.surface]);



  const textStyles = useMemo(() => ({
    name: {
      color: selected ? theme.colors.onPrimaryContainer : theme.colors.onSurface
    },
    meta: {
      color: selected ? `${theme.colors.onPrimaryContainer  }CC` : theme.colors.onSurfaceVariant
    }
  }), [selected, theme.colors.onPrimaryContainer, theme.colors.onSurface, theme.colors.onSurfaceVariant]);

  // Format the join date to be "Mmm d, yyyy"
  const joinedDate = useMemo(() => {
    const dateValue = customer.createdAt || customer.date;
    if (!dateValue) return 'Unknown';
    return new Date(dateValue).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  }, [customer.createdAt, customer.date]);

  // Determine the text for total and active orders
  const orderTexts = useMemo(() => {
    const totalOrdersText = customer.totalOrders === 1 ? '1 total order' : `${customer.totalOrders || 0} total orders`;
    const activeOrdersText = customer.activeOrders === 1 ? '1 Active order' : `${customer.activeOrders || 0} Active orders`;
    const activeOrdersColor = (customer.activeOrders || 0) > 0 ? '#2dd4bf' : theme.colors.onSurfaceVariant;
    
    return { totalOrdersText, activeOrdersText, activeOrdersColor };
  }, [customer.totalOrders, customer.activeOrders, theme.colors.onSurfaceVariant]);

  // Determine if a status badge for "New" or "VIP" should be shown.
  const customerStatus = useMemo(() => {
    if (customer.isVIP || (customer.totalSpent || 0) > 50000) {
      return { text: 'VIP', backgroundColor: '#4d443a', color: '#ffb74d' };
    }
    if ((customer.totalOrders || 0) === 0) {
      return { text: 'New', backgroundColor: theme.colors.primaryContainer || '#E3F2FD', color: theme.colors.onPrimaryContainer || '#0D47A1' };
    }
    return null;
  }, [customer.isVIP, customer.totalSpent, customer.totalOrders, theme.colors.primaryContainer, theme.colors.onPrimaryContainer]);

  const handlePress = useMemo(() => () => {
    onPress(customer);
  }, [onPress, customer]);

  const handleLongPress = useMemo(() => () => {
    if (onLongPress) {
      onLongPress(customer);
    }
  }, [onLongPress, customer]);

  const CardWrapper = Platform.OS === 'android' ? Pressable : TouchableOpacity;
  const cardProps = useMemo(() => Platform.OS === 'android' ? {
    onPress: handlePress,
    onLongPress: handleLongPress,
    android_ripple: { color: `${theme.colors.primary  }22`, borderless: false },
  } : {
    onPress: handlePress,
    onLongPress: handleLongPress,
    activeOpacity: 0.8,
  }, [handlePress, handleLongPress, theme.colors.primary]);

  return (
    <CardWrapper {...cardProps}>
      <Surface style={[styles.card, cardStyles]} elevation={1}>
        <View style={styles.cardContent}>
          {/* Selection Checkbox */}
          {showSelectionIndicator && (
            <TouchableOpacity 
              onPress={(e) => {
                e.stopPropagation();
                onCheckboxPress();
              }}
              style={[styles.selectionCheckbox, { marginRight: 12 }]}
            >
              <PhosphorIcon 
                name={selected ? "checkmark-circle" : "x-circle"}
                size={24}
                color={selected ? theme.colors.primary : theme.colors.outline}
              />
            </TouchableOpacity>
          )}

          <View style={styles.detailsContainer}>
            <View style={styles.topRow}>
          <View style={{ flex: 1 }}>
            <Text style={[styles.name, textStyles.name]}>
              {customer.name}
            </Text>
            {(customer.totalOrders || 0) > 0 ? (
              <Text style={[styles.metaText, textStyles.meta, { marginTop: 2 }]}>
                Joined: {joinedDate}
              </Text>
            ) : (
              customer.email ? (
                <Text style={[styles.metaText, textStyles.meta, { marginTop: 2 }]}>
                  {customer.email}
                </Text>
              ) : null
            )}
          </View>
          {customerStatus && (
            <View style={[styles.badge, { backgroundColor: customerStatus.backgroundColor }]}>
              <Text style={[styles.badgeText, { color: customerStatus.color }]}>{customerStatus.text}</Text>
            </View>
          )}
            </View>
            <View style={styles.bottomRow}>
              <View style={styles.leftColumn}>
                <Text style={[styles.phone, textStyles.meta]}>
                  {customer.phone}
                </Text>
              </View>
              <View style={styles.rightColumn}>
                {(customer.totalOrders || 0) > 0 && (
                  <>
                    <Text style={[styles.metaText, textStyles.meta, { fontWeight: 'bold' }]}>
                      ৳{customer.totalSpent?.toLocaleString() || 0} spent
                    </Text>
                    <Text style={[styles.metaText, textStyles.meta]}>
                      {orderTexts.totalOrdersText}
                    </Text>
                    <Text style={[
                      styles.activeOrders, 
                      { color: selected ? `${theme.colors.onPrimaryContainer  }CC` : orderTexts.activeOrdersColor }
                    ]}>
                      {orderTexts.activeOrdersText}
                    </Text>
                  </>
                )}
              </View>
            </View>
          </View>
        </View>
      </Surface>
    </CardWrapper>
  );
});

CustomerCard.displayName = 'CustomerCard';

const styles = StyleSheet.create({
  card: {
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 16,
    position: 'relative',
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  detailsContainer: {
    flex: 1,
  },
  selectionCheckbox: {
    margin: 0,
    padding: 0,
  },
  topRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  leftColumn: {
    flex: 1,
    justifyContent: 'center',
  },
  rightColumn: {
    alignItems: 'flex-end',
    justifyContent: 'center',
    marginLeft: 16,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
  },
  phone: {
    fontSize: 14,
    marginBottom: 4,
  },
  metaText: {
    fontSize: 12,
  },
  activeOrders: {
    fontSize: 12,
    fontWeight: '500',
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  badgeText: {
    fontSize: 10,
    fontWeight: '600',
  },
});

export default CustomerCard; 