import React from 'react';
import { View, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Text, Surface, IconButton } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import { SPACING, BORDER_RADIUS } from '../../theme/theme';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

interface ProductCardProps {
  product: {
    id: string | number;
    name: string;
    price: number;
    stock: number;
    category: string;
    image?: string;
    [key: string]: any;
  };
  onPress: () => void;
  onLongPress: () => void;
  onCheckboxPress: () => void;
  selected: boolean;
  showSelectionIndicator: boolean;
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  onPress,
  onLongPress,
  onCheckboxPress,
  selected,
  showSelectionIndicator,
}) => {
  const theme = useTheme();

  const handlePress = () => {
    onPress();
  };

  const handleLongPress = () => {
    onLongPress();
  };

  const handleCheckboxPress = (e: any) => {
    e.stopPropagation();
    onCheckboxPress();
  };

  return (
    <Surface style={[styles.card, { backgroundColor: theme.colors.surface }]} elevation={1}>
      <TouchableOpacity
        style={[
          styles.content,
          selected && { backgroundColor: theme.colors.primaryContainer }
        ]}
        onPress={handlePress}
        onLongPress={handleLongPress}
        activeOpacity={0.7}
      >
        {/* Selection Checkbox */}
        {showSelectionIndicator && (
          <TouchableOpacity
            style={styles.checkboxContainer}
            onPress={handleCheckboxPress}
            activeOpacity={0.8}
          >
            <PhosphorIcon
              name={selected ? 'checkmark-circle' : 'x-circle'}
              size={24}
              color={selected ? theme.colors.primary : theme.colors.onSurfaceVariant}
            />
          </TouchableOpacity>
        )}

        {/* Product Image */}
        <View style={styles.imageContainer}>
          {product.image ? (
            <Image source={{ uri: product.image }} style={styles.productImage} />
          ) : (
            <View style={[styles.placeholderImage, { backgroundColor: theme.colors.surfaceVariant }]}>
              <PhosphorIcon name="package" size={32} color={theme.colors.onSurfaceVariant} />
            </View>
          )}
        </View>

        {/* Product Info */}
        <View style={styles.productInfo}>
          <Text 
            variant="titleMedium" 
            style={[
              styles.productName, 
              { color: selected ? theme.colors.onPrimaryContainer : theme.colors.onSurface }
            ]}
            numberOfLines={2}
          >
            {product.name}
          </Text>
          
          <Text 
            variant="bodySmall" 
            style={[
              styles.productCategory, 
              { color: selected ? theme.colors.onPrimaryContainer : theme.colors.onSurfaceVariant }
            ]}
          >
            {product.category}
          </Text>
          
          <Text 
            variant="titleLarge" 
            style={[
              styles.productPrice, 
              { color: selected ? theme.colors.onPrimaryContainer : theme.colors.primary }
            ]}
          >
            ৳{product.price.toFixed(2)}
          </Text>
          
          <View style={styles.stockContainer}>
            <Text 
              variant="bodySmall" 
              style={[
                styles.productStock, 
                { 
                  color: selected ? theme.colors.onPrimaryContainer : theme.colors.onSurfaceVariant,
                  fontWeight: '500'
                }
              ]}
            >
              Stock: {product.stock}
            </Text>
            
            {/* Stock indicator */}
            <View style={[
              styles.stockIndicator,
              {
                backgroundColor: product.stock === 0 
                  ? theme.colors.error 
                  : product.stock <= 10 
                    ? theme.colors.tertiary 
                    : theme.colors.primary
              }
            ]} />
          </View>
        </View>

        {/* Action Button */}
        {!showSelectionIndicator && (
          <IconButton
            icon="dots-vertical"
            size={20}
            onPress={handlePress}
            iconColor={selected ? theme.colors.onPrimaryContainer : theme.colors.onSurfaceVariant}
            style={styles.actionButton}
          />
        )}
      </TouchableOpacity>
    </Surface>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
  },
  content: {
    flexDirection: 'row',
    padding: SPACING.md,
    alignItems: 'center',
  },
  checkboxContainer: {
    marginRight: SPACING.sm,
    padding: SPACING.xs,
  },
  imageContainer: {
    marginRight: SPACING.md,
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: BORDER_RADIUS.md,
  },
  placeholderImage: {
    width: 60,
    height: 60,
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontWeight: '600',
    marginBottom: 4,
  },
  productCategory: {
    marginBottom: 4,
  },
  productPrice: {
    fontWeight: '700',
    marginBottom: 4,
  },
  stockContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
  },
  productStock: {
    fontStyle: 'italic',
  },
  stockIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  actionButton: {
    margin: 0,
  },
});

export default ProductCard; 