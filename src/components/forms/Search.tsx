/**
 * Search - Single search component to replace all search implementations
 * Provides consistent search UX across the entire app
 */

import React, { useState, useCallback, useEffect, useRef} from 'react';
import { View, StyleSheet, TouchableOpacity, Modal, ViewStyle } from 'react-native';
import {
  Searchbar,
  Text,
  Surface,
  Chip,
  IconButton,
  Portal,
  Button,
} from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';


import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import navigationService from '../../services/NavigationService';
import { StorageService } from '../../services/storageService';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';

// Search types
type SearchType = 'global' | 'orders' | 'products' | 'activities' | 'customers';
type SearchMode = 'icon' | 'bar' | 'modal';

// Search item interface
interface SearchItem {
  id?: string | number;
  name?: string;
  title?: string;
  description?: string;
  price?: number;
  customerName?: string;
  customer?: string;
  action?: string;
  type?: string;
  email?: string;
  phone?: string;
  [key: string]: any;
}

// Enhanced search item with scoring
interface ScoredSearchItem extends SearchItem {
  _searchScore: number;
  _matchedFields: string[];
  _bestMatch: string;
  matchedField?: string;
}

// Props interface
interface UnifiedSearchProps {
  // Search configuration
  type?: SearchType;
  data?: SearchItem[];
  searchFields?: string[];
  placeholder?: string;

  // Display options
  mode?: SearchMode;
  showSuggestions?: boolean;
  showRecentSearches?: boolean;
  showFilters?: boolean;
  filters?: string[];

  // Callbacks
  onSearch?: (query: string, filter?: string) => void;
  onResultSelect?: (item: SearchItem) => void;
  onFilterChange?: (filter: string) => void;

  // Styling
  style?: ViewStyle;
  iconSize?: number;
  iconColor?: string;
}

const Search: React.FC<UnifiedSearchProps> = ({
  // Search configuration
  type = 'global',
  data = [],
  searchFields = ['name'],
  placeholder = 'Search...',

  // Display options
  mode = 'icon',
  showSuggestions = true,
  showRecentSearches = true,
  showFilters = false,
  filters = [],

  // Callbacks
  onSearch,
  onResultSelect,
  onFilterChange,

  // Styling
  style,
  iconSize = 24,
  iconColor,
}) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();

  // Safety check for theme - return null if theme is not properly initialized
  if (!theme || !theme.colors || !theme.colors.surface) {
    return null;
  }

  const [query, setQuery] = useState<string>('');
  const [suggestions, setSuggestions] = useState<ScoredSearchItem[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [selectedFilter, setSelectedFilter] = useState<string>('All');
  const [showModal, setShowModal] = useState<boolean>(false);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Load recent searches on mount
  useEffect(() => {
    loadRecentSearches();
  }, [type]);

  const loadRecentSearches = async (): Promise<void> => {
    try {
      const storageKey = `recentSearches_${type}`;
      const recent = await StorageService.get<string[]>(storageKey, true) || [];
      setRecentSearches(recent.slice(0, 5));
    } catch (error) {
      LoggingService.warn('Failed to load recent searches', 'SEARCH', error as Error);
    }
  };

  const saveRecentSearch = async (searchQuery: string): Promise<void> => {
    if (!searchQuery.trim() || !showRecentSearches) return;

    try {
      const storageKey = `recentSearches_${type}`;
      const recent = await StorageService.get<string[]>(storageKey, true) || [];
      const updated = [
        searchQuery,
        ...recent.filter(item => item !== searchQuery)
      ].slice(0, 5);

      await StorageService.set(storageKey, updated);
      setRecentSearches(updated);
    } catch (error) {
      LoggingService.warn('Failed to save recent search', 'SEARCH', error as Error);
    }
  };

  // Enhanced search algorithm with scoring and fuzzy matching
  const generateSuggestions = useCallback((searchQuery: string): void => {
    if (!searchQuery.trim() || !showSuggestions) {
      setSuggestions([]);
      return;
    }

    const query = searchQuery.toLowerCase();

    // Enhanced search algorithm with scoring
    const scored = data.map(item => {
      let score = 0;
      const matchedFields: string[] = [];
      let bestMatch = '';

      searchFields.forEach(field => {
        const value = item[field];
        if (value) {
          const fieldValue = value.toString().toLowerCase();

          // Exact match gets highest score
          if (fieldValue === query) {
            score += 100;
            matchedFields.push(field);
            bestMatch = fieldValue;
          }
          // Starts with query gets high score
          else if (fieldValue.startsWith(query)) {
            score += 50;
            matchedFields.push(field);
            if (!bestMatch) bestMatch = fieldValue;
          }
          // Contains query gets medium score
          else if (fieldValue.includes(query)) {
            score += 25;
            matchedFields.push(field);
            if (!bestMatch) bestMatch = fieldValue;
          }
          // Fuzzy match for typos (simple implementation)
          else if (query.length > 2 && fieldValue.includes(query.slice(0, -1))) {
            score += 10;
            matchedFields.push(field);
            if (!bestMatch) bestMatch = fieldValue;
          }
        }
      });

      return {
        ...item,
        _searchScore: score,
        _matchedFields: matchedFields,
        _bestMatch: bestMatch,
        matchedField: matchedFields[0] // Keep for backward compatibility
      } as ScoredSearchItem;
    })
    .filter(item => item._searchScore > 0)
    .sort((a, b) => b._searchScore - a._searchScore)
    .slice(0, 8); // Increased from 5 to 8 for better results

    setSuggestions(scored);
  }, [data, searchFields, showSuggestions]);

  const handleQueryChange = (searchQuery: string): void => {
    setQuery(searchQuery);
    setShowDropdown(true);

    // Debounce suggestions generation
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      generateSuggestions(searchQuery);
    }, 300);
  };

  const handleSearch = async (searchQuery: string = query): Promise<void> => {
    if (!searchQuery.trim()) return;

    setShowDropdown(false);
    setShowModal(false);

    try {
      await saveRecentSearch(searchQuery);
      onSearch?.(searchQuery, selectedFilter);
    } catch (error) {
      LoggingService.warn('Search failed', 'SEARCH', error as Error);
    }
  };

  // Navigate to appropriate screen based on item type
  const handleNavigateToResult = (item: SearchItem): void => {
    try {
      if (item.price !== undefined) {
        // It's a product - navigate to products screen
        LoggingService.debug('Navigating to product', 'SEARCH', item);
        navigationService.navigate('Products');
      } else if (item.customerName || item.customer) {
        // It's an order - navigate to orders screen
        LoggingService.debug('Navigating to order', 'SEARCH', item);
        navigationService.navigate('Orders');
      } else if (item.action || item.type) {
        // It's an activity - navigate to activity log
        LoggingService.debug('Navigating to activity', 'SEARCH', item);
        navigationService.navigate('ActivityLog');
      } else if (item.email || item.phone) {
        // It's a customer - navigate to customers (or profile for now)
        LoggingService.debug('Navigating to customer', 'SEARCH', item);
        navigationService.navigate('MyProfile');
      }
    } catch (error) {
      LoggingService.error('Failed to navigate to search result', 'SEARCH', error as Error);
    }
  };

  const handleSuggestionSelect = (item: ScoredSearchItem): void => {
    const searchTerm = item.name || item[searchFields[0]] || '';
    setQuery(searchTerm);
    setShowDropdown(false);
    setShowModal(false);
    onResultSelect?.(item);
    saveRecentSearch(searchTerm);

    // Navigate to the appropriate screen
    handleNavigateToResult(item);
  };

  const handleFilterChange = (filter: string): void => {
    setSelectedFilter(filter);
    onFilterChange?.(filter);
  };

  const clearRecentSearches = async (): Promise<void> => {
    try {
      const storageKey = `recentSearches_${type}`;
      await StorageService.remove(storageKey);
      setRecentSearches([]);
    } catch (error) {
      LoggingService.warn('Failed to clear recent searches', 'SEARCH', error as Error);
    }
  };

  // Get placeholder based on type
  const getPlaceholder = (): string => {
    switch (type) {
      case 'orders':
        return 'Search orders...';
      case 'products':
        return 'Search products...';
      case 'activities':
        return 'Search activities...';
      case 'customers':
        return 'Search customers...';
      default:
        return placeholder;
    }
  };

  // Get appropriate icon for search result item
  const getSearchIcon = (item: SearchItem): string => {
    if (item.price !== undefined) return 'package'; // Product
    if (item.customerName || item.customer) return 'receipt'; // Order
    if (item.action || item.type) return 'clock'; // Activity
    if (item.email || item.phone) return 'user'; // Customer
    return 'magnifying-glass'; // Default
  };

  // Get color based on search score
  const getScoreColor = (score: number): string => {
    if (score >= 80) return theme.colors.tertiary; // Green for high relevance
    if (score >= 50) return theme.colors.secondary; // Orange for medium relevance
    return theme.colors.onSurfaceVariant; // Gray for low relevance
  };

  // Render search icon (for icon mode)
  const renderSearchIcon = (): React.ReactNode => (
    <IconButton
      icon="magnify"
      size={iconSize}
      iconColor={iconColor || theme.colors.onSurface}
      onPress={() => {
        try {
          navigationService.navigate('Search', {
            type,
            data,
            searchFields,
            placeholder,
            showFilters,
            filters,
          });
        } catch (error) {
          LoggingService.error('Failed to navigate to Search screen', 'SEARCH', error as Error);
        }
      }}
      style={[style, { backgroundColor: `${theme.colors.surfaceVariant  }40` }]}
    />
  );

  // Render search bar (for bar mode)
  const renderSearchBar = (): React.ReactNode => (
    <View style={[styles.searchBarContainer, style]}>
      <Searchbar
        placeholder={getPlaceholder()}
        onChangeText={handleQueryChange}
        value={query}
        onSubmitEditing={() => handleSearch()}
        style={[
          styles.searchBar,
          {
            backgroundColor: theme.colors.surfaceVariant,
            borderColor: theme.colors.outline,
            borderWidth: 1,
          }
        ]}
        inputStyle={{ color: theme.colors.onSurface }}
        placeholderTextColor={theme.colors.onSurfaceVariant}
        iconColor={theme.colors.onSurfaceVariant}
        onFocus={() => setShowDropdown(true)}
        onBlur={() => setTimeout(() => setShowDropdown(false), 200)}
      />

      {showDropdown && (showRecentSearches || suggestions.length > 0) && (
        <Surface
          style={[
            styles.dropdown,
            {
              backgroundColor: theme.colors.surface,
              borderColor: theme.colors.outline,
              borderWidth: 1,
            }
          ]}
          elevation={4}
        >
          {renderDropdownContent()}
        </Surface>
      )}
    </View>
  );

  // Render dropdown content
  const renderDropdownContent = (): React.ReactNode => (
    <>
      {showRecentSearches && recentSearches.length > 0 && (
        <View>
          <View style={styles.sectionHeader}>
            <Text variant="labelMedium" style={{ color: theme.colors.onSurfaceVariant }}>
              Recent Searches
            </Text>
            <TouchableOpacity onPress={clearRecentSearches}>
              <Text variant="labelSmall" style={{ color: theme.colors.primary }}>
                Clear
              </Text>
            </TouchableOpacity>
          </View>
          {recentSearches.map((search, index) => (
            <TouchableOpacity
              key={index}
              style={styles.dropdownItem}
              onPress={() => {
                setQuery(search);
                handleSearch(search);
              }}
            >
              <PhosphorIcon name="clock" size={16} color={theme.colors.onSurfaceVariant} />
              <Text variant="bodyMedium" style={styles.dropdownText}>
                {search}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {suggestions.length > 0 && (
        <View>
          {recentSearches.length > 0 && (
            <View style={[styles.separator, { backgroundColor: `${theme.colors.outline  }30` }]} />
          )}
          <View style={styles.sectionHeader}>
            <Text variant="labelMedium" style={{ color: theme.colors.onSurfaceVariant }}>
              Suggestions
            </Text>
          </View>
          {suggestions.map((item) => {
            const displayText = item.name || item[searchFields[0]] || 'Unknown';
            const matchedField = item._matchedFields?.[0] || searchFields[0];
            const fieldValue = item[matchedField];

            return (
              <TouchableOpacity
                key={item.id || item.name || Math.random()}
                style={styles.dropdownItem}
                onPress={() => handleSuggestionSelect(item)}
              >
                <PhosphorIcon
                  name={getSearchIcon(item) as any}
                  size={16}
                  color={theme.colors.onSurfaceVariant}
                />
                <View style={styles.dropdownText}>
                  <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                    {displayText}
                  </Text>
                  {fieldValue && fieldValue !== displayText && (
                    <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginTop: 2 }}>
                      in {matchedField}: {fieldValue.toString()}
                    </Text>
                  )}
                </View>
                {item._searchScore && (
                  <View style={[styles.scoreIndicator, { backgroundColor: getScoreColor(item._searchScore) }]}>
                    <Text variant="labelSmall" style={{
                      color: theme.colors.onPrimary,
                      fontSize: 10,
                      fontWeight: '600'
                    }}>
                      {Math.round(item._searchScore)}
                    </Text>
                  </View>
                )}
              </TouchableOpacity>
            );
          })}
        </View>
      )}
    </>
  );

  // Render search modal (for modal mode)
  const renderSearchModal = (): React.ReactNode => (
    <Portal>
      <Modal
        visible={showModal}
        onDismiss={() => setShowModal(false)}
        style={styles.modal}
      >
        <TouchableOpacity
          style={styles.modalContainer}
          activeOpacity={1}
          onPress={() => setShowModal(false)}
        >
          <TouchableOpacity
            activeOpacity={1}
            onPress={(e) => e.stopPropagation()}
            style={[
              styles.modalContent,
              {
                backgroundColor: theme.colors.surface,
                borderColor: theme.colors.outline,
              }
            ]}
          >
            <View style={[styles.modalHeader, { borderBottomColor: `${theme.colors.outline  }20` }]}>
              <Text variant="titleLarge" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                Search {type.charAt(0).toUpperCase() + type.slice(1)}
              </Text>
              <IconButton
                icon="close"
                size={24}
                onPress={() => setShowModal(false)}
                iconColor={theme.colors.onSurfaceVariant}
                style={{ backgroundColor: theme.colors.surfaceVariant }}
              />
            </View>

            <Searchbar
              placeholder={getPlaceholder()}
              onChangeText={handleQueryChange}
              value={query}
              onSubmitEditing={() => handleSearch()}
              style={[
                styles.modalSearchBar,
                {
                  backgroundColor: theme.colors.surfaceVariant,
                  borderColor: theme.colors.outline,
                }
              ]}
              inputStyle={{ color: theme.colors.onSurface }}
              placeholderTextColor={theme.colors.onSurfaceVariant}
              iconColor={theme.colors.onSurfaceVariant}
              autoFocus
            />

            {showFilters && filters.length > 0 && (
              <View style={styles.filtersContainer}>
                {filters.map((filter) => (
                  <Chip
                    key={filter}
                    selected={selectedFilter === filter}
                    onPress={() => handleFilterChange(filter)}
                    style={[
                      styles.filterChip,
                      {
                        backgroundColor: selectedFilter === filter
                          ? theme.colors.primaryContainer
                          : theme.colors.surface,
                        borderColor: selectedFilter === filter
                          ? theme.colors.primary
                          : theme.colors.outline,
                      }
                    ]}
                    textStyle={{
                      color: selectedFilter === filter
                        ? theme.colors.onPrimaryContainer
                        : theme.colors.onSurface,
                    }}
                    mode={selectedFilter === filter ? 'flat' : 'outlined'}
                  >
                    {filter}
                  </Chip>
                ))}
              </View>
            )}

            <View style={[styles.modalResultsContainer, { backgroundColor: theme.colors.background }]}>
              {renderDropdownContent()}
            </View>

            <View style={[styles.modalActions, { borderTopColor: `${theme.colors.outline  }20` }]}>
              <Button
                mode="outlined"
                onPress={() => setShowModal(false)}
                style={[styles.modalButton, { borderColor: theme.colors.outline }]}
                textColor={theme.colors.onSurface}
              >
                Cancel
              </Button>
              <Button
                mode="contained"
                onPress={() => handleSearch()}
                style={styles.modalButton}
                disabled={!query.trim()}
                buttonColor={theme.colors.primary}
                textColor={theme.colors.onPrimary}
              >
                Search
              </Button>
            </View>
          </TouchableOpacity>
        </TouchableOpacity>
      </Modal>
    </Portal>
  );

  // Render based on mode
  switch (mode) {
    case 'icon':
      return (
        <>
          {renderSearchIcon()}
          {renderSearchModal()}
        </>
      );
    case 'bar':
      return renderSearchBar();
    case 'modal':
      return renderSearchModal();
    default:
      return renderSearchIcon();
  }
};

const styles = StyleSheet.create({
  searchBarContainer: {
    position: 'relative',
  },
  searchBar: {
    borderRadius: 12,
    elevation: 2,
  },
  dropdown: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    zIndex: 1000,
    borderRadius: 12,
    marginTop: 4,
    maxHeight: 300,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    marginHorizontal: 8,
    marginVertical: 2,
  },
  dropdownText: {
    marginLeft: 12,
    flex: 1,
  },
  scoreIndicator: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    minWidth: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  separator: {
    height: 1,
    marginVertical: 8,
  },
  modal: {
    flex: 1,
    margin: 0,
    justifyContent: 'flex-start',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'transparent',
    paddingTop: 60,
    paddingHorizontal: 16,
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    maxHeight: '85%',
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
  },
  modalSearchBar: {
    marginBottom: 16,
    borderRadius: 12,
  },
  filtersContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  filterChip: {
    marginRight: 8,
    marginBottom: 8,
  },
  modalResultsContainer: {
    flex: 1,
    marginBottom: 16,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 12,
    paddingTop: 12,
    borderTopWidth: 1,
  },
  modalButton: {
    minWidth: 80,
  },
});

export default Search;