/**
 * ImagePicker Component - Handles image selection from camera or gallery
 *
 * @component
 * @description Provides a unified interface for selecting images from camera or gallery
 * with proper permissions handling and image optimization.
 *
 * @param {Object} props - Component props
 * @param {Function} props.onImageSelected - Callback when image is selected
 * @param {string} [props.currentImage] - Current image URI to display
 * @param {Object} [props.style] - Additional styles
 * @param {string} [props.placeholder] - Placeholder text when no image
 *
 * @example
 * ```jsx
 * <ImagePicker
 *   onImageSelected={(imageUri) => setFormData({...formData, image: imageUri})}
 *   currentImage={formData.image}
 *   placeholder="Add Product Image"
 * />
 * ```
 */

import * as ImageManipulator from 'expo-image-manipulator';
import * as ExpoImagePicker from 'expo-image-picker';
import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Image,
  Dimensions,
} from 'react-native';
import { Text, Surface } from 'react-native-paper';

import { useTheme } from '../../context/ThemeContext';
import LoggingService from '../../services/LoggingService';
import { SPACING, BORDER_RADIUS } from '../../theme/theme';
import { PhosphorIcon } from '../../utils/phosphorIconRegistry';


interface ImagePickerProps {
  onImageSelected: (imageUri: string) => void;
  currentImage?: string | null;
  placeholder?: string;
  size?: 'small' | 'medium' | 'large';
  aspectRatio?: number;
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  allowsEditing?: boolean;
  showCamera?: boolean;
  showGallery?: boolean;
  enableOptimization?: boolean;
  optimizationQuality?: number;
}

const ImagePicker: React.FC<ImagePickerProps> = ({
  onImageSelected,
  currentImage,
  placeholder = 'Add Image',
  size = 'medium',
  aspectRatio = 1,
  maxWidth = 800,
  maxHeight = 800,
  quality = 0.8,
  allowsEditing = true,
  showCamera = true,
  showGallery = true,
  enableOptimization = true,
  optimizationQuality = 0.8,
}) => {
  const theme = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [isPressed, setIsPressed] = useState(false);

  const getFileSize = async (uri: string): Promise<number> => {
    try {
      const response = await fetch(uri);
      const blob = await response.blob();
      return blob.size;
    } catch (error) {
      console.error('Failed to get file size:', error);
      return 0;
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))  } ${  sizes[i]}`;
  };

  const optimizeImage = async (imageUri: string): Promise<string> => {
    if (!enableOptimization) {
      return imageUri;
    }

    try {
      setIsLoading(true);
      
      // Get original file size
      const originalSize = await getFileSize(imageUri);
      
      // Get image info first to determine optimal resize dimensions
      const imageInfo = await ImageManipulator.manipulateAsync(
        imageUri,
        [],
        { format: ImageManipulator.SaveFormat.JPEG }
      );

      // Calculate optimal dimensions while maintaining aspect ratio
      const { width: originalWidth, height: originalHeight } = imageInfo;
      let targetWidth = maxWidth;
      let targetHeight = maxHeight;

      if (originalWidth > maxWidth || originalHeight > maxHeight) {
        const aspectRatio = originalWidth / originalHeight;
        if (aspectRatio > 1) {
          // Landscape
          targetWidth = maxWidth;
          targetHeight = maxWidth / aspectRatio;
        } else {
          // Portrait
          targetHeight = maxHeight;
          targetWidth = maxHeight * aspectRatio;
        }
      } else {
        // Image is smaller than max dimensions, keep original size
        targetWidth = originalWidth;
        targetHeight = originalHeight;
      }

      // Optimize image using expo-image-manipulator
      const result = await ImageManipulator.manipulateAsync(
        imageUri,
        [
          {
            resize: {
              width: Math.round(targetWidth),
              height: Math.round(targetHeight),
            },
          },
        ],
        {
          compress: optimizationQuality,
          format: ImageManipulator.SaveFormat.WEBP,
          base64: false,
        }
      );

              // Get optimized file size
        const optimizedSize = await getFileSize(result.uri);
        const sizeReduction = originalSize > 0 ? ((originalSize - optimizedSize) / originalSize * 100).toFixed(1) : 0;
        
        LoggingService.debug(`Image optimized: ${originalWidth}x${originalHeight} → ${Math.round(targetWidth)}x${Math.round(targetHeight)} (WebP)`, 'IMAGE_PICKER');
        LoggingService.debug(`File size: ${formatFileSize(originalSize)} → ${formatFileSize(optimizedSize)} (${sizeReduction}% reduction)`, 'IMAGE_PICKER');
        
        return result.uri;
    } catch (error) {
      console.error('Image optimization failed:', error);
      // Return original URI if optimization fails
      return imageUri;
    } finally {
      setIsLoading(false);
    }
  };



  const handleImagePickerResult = async (result: ExpoImagePicker.ImagePickerResult) => {
    if (result.canceled) {
      return;
    }

    if (result.assets && result.assets[0]) {
      const asset = result.assets[0];
      if (asset.uri) {
        try {
          // Optimize the image before passing it to the parent component
          const optimizedUri = await optimizeImage(asset.uri);
          onImageSelected(optimizedUri);
        } catch (error) {
          console.error('Failed to process image:', error);
          // Fallback to original image if optimization fails
          onImageSelected(asset.uri);
        }
      }
    }
  };

  const openCamera = async () => {
    const { status } = await ExpoImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission Denied', 'Camera permission is required to take photos.');
      return;
    }

    try {
      const result = await ExpoImagePicker.launchCameraAsync({
        mediaTypes: ExpoImagePicker.MediaTypeOptions.Images,
        allowsEditing,
        aspect: [aspectRatio, 1],
        quality: 1, // Use full quality for camera, we'll optimize it ourselves
      });
      await handleImagePickerResult(result);
    } catch (error) {
      Alert.alert('Error', 'Failed to open camera. Please try again.');
    }
  };

  const openGallery = async () => {
    const { status } = await ExpoImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission Denied', 'Gallery permission is required to select images.');
      return;
    }

    try {
      const result = await ExpoImagePicker.launchImageLibraryAsync({
        mediaTypes: ExpoImagePicker.MediaTypeOptions.Images,
        allowsEditing,
        aspect: [aspectRatio, 1],
        quality: 1, // Use full quality for gallery, we'll optimize it ourselves
        selectionLimit: 1,
      });
      await handleImagePickerResult(result);
    } catch (error) {
      Alert.alert('Error', 'Failed to open gallery. Please try again.');
    }
  };

  const showImageOptions = () => {
    const options = [];
    
    if (showCamera) {
      options.push({ text: 'Take Photo', onPress: openCamera });
    }
    
    if (showGallery) {
      options.push({ text: 'Choose from Gallery', onPress: openGallery });
    }
    
    if (currentImage) {
      options.push({ 
        text: 'Remove Image', 
        onPress: () => onImageSelected(''),
        style: 'destructive' as const
      });
    }

    if (options.length === 0) {
      Alert.alert('No Options', 'No image picker options available.');
      return;
    }

    Alert.alert(
      'Select Image',
      'Choose how you want to add an image',
      options,
      { cancelable: true }
    );
  };

  const getSizeStyles = () => {
    const { width: screenWidth } = Dimensions.get('window');
    const isSmallDevice = screenWidth < 375;
    const isLargeDevice = screenWidth > 768;
    
    // Larger base sizes for better visibility
    const baseSizes = {
      small: 80,
      medium: 100,
      large: 140,
    };
    
    // Responsive scaling
    const scale = isSmallDevice ? 0.9 : isLargeDevice ? 1.3 : 1.1;
    
    switch (size) {
      case 'small':
        return { 
          width: Math.round(baseSizes.small * scale), 
          height: Math.round(baseSizes.small * scale) 
        };
      case 'large':
        return { 
          width: Math.round(baseSizes.large * scale), 
          height: Math.round(baseSizes.large * scale) 
        };
      default:
        return { 
          width: Math.round(baseSizes.medium * scale), 
          height: Math.round(baseSizes.medium * scale) 
        };
    }
  };

  const sizeStyles = getSizeStyles();

  return (
    <TouchableOpacity
      style={[
        styles.container,
        sizeStyles,
        { borderRadius: BORDER_RADIUS.lg }
      ]}
      onPress={showImageOptions}
      onPressIn={() => setIsPressed(true)}
      onPressOut={() => setIsPressed(false)}
      activeOpacity={0.7}
    >
      {currentImage ? (
        <Surface style={[styles.imageContainer, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <View style={[styles.imageWrapper, sizeStyles]}>
            <Image 
              source={{ uri: currentImage }}
              style={[
                styles.selectedImage, 
                sizeStyles,
                isPressed && { opacity: 0.8 }
              ]}
              resizeMode="cover"
            />
            {isLoading && (
              <View style={[styles.loadingOverlay, { backgroundColor: `${theme.colors.surface}80` }]}>
                <View style={styles.loadingContent}>
                  <PhosphorIcon 
                    name="refresh" 
                    size={24} 
                    color={theme.colors.primary} 
                  />
                  <Text style={[styles.loadingText, { color: theme.colors.primary }]}>
                    Optimizing...
                  </Text>
                </View>
              </View>
            )}
            <View style={[
              styles.overlay, 
              { 
                backgroundColor: `${theme.colors.primary}15`,
                opacity: isPressed ? 1 : 0
              }
            ]}>
              <PhosphorIcon 
                name="camera" 
                size={20} 
                color={theme.colors.primary} 
              />
            </View>
            
          </View>
          
          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[styles.actionButton, styles.editButton, { backgroundColor: 'rgba(0, 0, 0, 0.5)' }]}
              onPress={(e) => {
                e.stopPropagation();
                showImageOptions();
              }}
              activeOpacity={0.8}
            >
              <PhosphorIcon 
                name="edit" 
                size={20} 
                color="#FFFFFF" 
              />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.actionButton, styles.deleteButton, { backgroundColor: 'rgba(0, 0, 0, 0.5)' }]}
              onPress={(e) => {
                e.stopPropagation();
                Alert.alert(
                  'Remove Image',
                  'Are you sure you want to remove this image?',
                  [
                    { text: 'Cancel', style: 'cancel' },
                    { 
                      text: 'Remove', 
                      style: 'destructive',
                      onPress: () => onImageSelected('')
                    }
                  ]
                );
              }}
              activeOpacity={0.8}
            >
              <PhosphorIcon 
                name="trash" 
                size={20} 
                color="#FF3B30" 
              />
            </TouchableOpacity>
          </View>
        </Surface>
      ) : (
        <Surface style={[styles.placeholderContainer, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <View style={[styles.placeholder, sizeStyles]}>
            <PhosphorIcon 
              name="plus" 
              size={size === 'small' ? 24 : size === 'large' ? 36 : 30} 
              color={theme.colors.primary} 
            />
            <Text 
              variant="bodySmall" 
              style={[
                styles.placeholderText, 
                { color: theme.colors.onSurfaceVariant }
              ]}
            >
              {placeholder}
            </Text>
          </View>
        </Surface>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    alignSelf: 'flex-start',
  },
  imageContainer: {
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
  },
  imageWrapper: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imagePlaceholder: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    opacity: 0,
  },
  selectedImage: {
    width: '100%',
    height: '100%',
    borderRadius: BORDER_RADIUS.lg,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: BORDER_RADIUS.lg,
  },
  loadingContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: SPACING.xs,
    fontSize: 12,
    fontWeight: '500',
  },
  actionButtons: {
    position: 'absolute',
    top: SPACING.sm,
    right: SPACING.sm,
    flexDirection: 'row',
    gap: SPACING.sm,
    zIndex: 10,
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  editButton: {
    // Styles applied via inline style
  },
  deleteButton: {
    // Styles applied via inline style
  },
  placeholderContainer: {
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
  },
  placeholder: {
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderStyle: 'dashed',
    borderColor: 'rgba(0,0,0,0.1)',
  },
  placeholderText: {
    marginTop: SPACING.xs,
    textAlign: 'center',
    fontWeight: '500',
  },
});

export default ImagePicker;
