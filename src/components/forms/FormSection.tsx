import DateTimePicker from '@react-native-community/datetimepicker';
import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Surface, Text, Button, Menu } from 'react-native-paper';


import { useTheme } from '../../context/ThemeContext';
import { UnifiedFormSectionProps } from '../../types';
import Switch from '../ui/Switch';
import TextInput from '../ui/TextInput';

const FormSection: React.FC<UnifiedFormSectionProps> = ({
  fields = [],
  errors = {},
  onFieldError = (_key: string, _error: string) => {},
  sectionTitle = '',
  style = {},
}) => {
  const [menuVisible, setMenuVisible] = React.useState<Record<string, boolean>>({});
  const [datePickerVisible, setDatePickerVisible] = React.useState<Record<string, boolean>>({});
  const theme = useTheme();

  // Handle field change and validation
  const handleChange = (field: any, value: any): void => {
    field.onChange(value);
    if (field.validation) {
      const error = field.validation(value);
      onFieldError(field.key, error);
    } else if (field.required && !value) {
      onFieldError(field.key, `${field.label} is required`);
    } else {
      onFieldError(field.key, '');
    }
  };

  return (
    <Surface style={[styles.section, style]} elevation={1}>
      {sectionTitle ? (
        <Text variant="titleMedium" style={styles.sectionTitle}>{sectionTitle}</Text>
      ) : null}
      {fields.map((field) => {
        let keyboardType;
        if (field.type === 'numeric') keyboardType = 'numeric';
        else if (field.type === 'phone') keyboardType = 'phone-pad';
        else if (field.type === 'email') keyboardType = 'email-address';
        else keyboardType = 'default';

        // Dropdown/select
        if (field.type === 'select' && Array.isArray(field.options)) {
          return (
            <View key={field.key} style={styles.fieldWrapper}>
              <Text style={{ marginBottom: 8 }}>{field.label}</Text>
              <Menu
                visible={!!menuVisible[field.key]}
                onDismiss={() => setMenuVisible(prev => ({ ...prev, [field.key]: false }))}
                anchor={
                  <Button
                    mode="outlined"
                    onPress={() => setMenuVisible(prev => ({ ...prev, [field.key]: true }))}
                    style={{ marginBottom: 8 }}
                  >
                    {field.options!.find((opt: any) => opt.value === field.value)?.label || 'Select...'}
                  </Button>
                }
              >
                {field.options!.map((option: any) => (
                  <Menu.Item
                    key={option.value}
                    onPress={() => {
                      setMenuVisible(prev => ({ ...prev, [field.key]: false }));
                      handleChange(field, option.value);
                    }}
                    title={option.label}
                  />
                ))}
              </Menu>
              {errors[field.key] ? (
                <Text variant="bodySmall" style={styles.errorText}>{errors[field.key]}</Text>
              ) : null}
            </View>
          );
        }

        // Date picker
        if (field.type === 'date') {
          return (
            <View key={field.key} style={styles.fieldWrapper}>
              <Text style={{ marginBottom: 8 }}>{field.label}</Text>
              <Button
                mode="outlined"
                onPress={() => setDatePickerVisible(prev => ({ ...prev, [field.key]: true }))}
                style={{ marginBottom: 8 }}
              >
                {field.value || 'Select date'}
              </Button>
              {datePickerVisible[field.key] && (
                <DateTimePicker
                  value={field.value ? new Date(field.value) : new Date()}
                  mode="date"
                  display="default"
                  onChange={(event, selectedDate) => {
                    setDatePickerVisible(prev => ({ ...prev, [field.key]: false }));
                    if (selectedDate) {
                      handleChange(field, selectedDate.toISOString().split('T')[0]);
                    }
                  }}
                />
              )}
              {errors[field.key] ? (
                <Text variant="bodySmall" style={styles.errorText}>{errors[field.key]}</Text>
              ) : null}
            </View>
          );
        }

        // Switch/toggle
        if (field.type === 'switch') {
          return (
            <View key={field.key} style={styles.fieldWrapper}>
              <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                <Text style={{ flex: 1 }}>{field.label}</Text>
                <Switch
                  value={!!field.value}
                  onValueChange={(val: any) => handleChange(field, val)}
                  disabled={field.disabled}
                  style={{}}
                />
              </View>
              {errors[field.key] ? (
                <Text variant="bodySmall" style={styles.errorText}>{errors[field.key]}</Text>
              ) : null}
            </View>
          );
        }

        // Toggle button group for gender or similar fields
        if (field.type === 'toggle' && Array.isArray(field.options)) {
          return (
            <View key={field.key} style={styles.fieldWrapper}>
              <Text style={{ marginBottom: 8 }}>{field.label}</Text>
              <View style={styles.toggleGroup}>
                {field.options!.map((option: any) => (
                  <Button
                    key={option.value}
                    mode={field.value === option.value ? 'contained' : 'outlined'}
                    onPress={() => handleChange(field, option.value)}
                    style={styles.toggleButton}
                  >
                    {option.label}
                  </Button>
                ))}
              </View>
              {errors[field.key] ? (
                <Text variant="bodySmall" style={styles.errorText}>{errors[field.key]}</Text>
              ) : null}
            </View>
          );
        }

        // Default input
        return (
          <View key={field.key} style={styles.fieldWrapper}>
            <TextInput
              label={field.label}
              value={field.value}
              onChangeText={(val: string) => handleChange(field, val)}
              type={field.type || 'text'}
              required={field.required}
              error={!!errors[field.key]}
              rightAffix={field.rightAffix}
              left={field.left}
              multiline={field.multiline}
              keyboardType={keyboardType}
              {...(field.inputProps || {})}
            />
            {errors[field.key] ? (
              <Text variant="bodySmall" style={styles.errorText}>{errors[field.key]}</Text>
            ) : null}
          </View>
        );
      })}
    </Surface>
  );
};

const styles = StyleSheet.create({
  section: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: 16,
  },
  fieldWrapper: {
    marginBottom: 12,
  },
  errorText: {
    color: '#B00020',
    marginTop: -8,
    marginBottom: 8,
  },
  toggleGroup: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 8,
  },
  toggleButton: {
    flex: 1,
    marginHorizontal: 2,
  },
});

export default FormSection; 