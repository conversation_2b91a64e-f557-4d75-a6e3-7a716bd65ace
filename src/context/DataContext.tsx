import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { createContext, useContext, useReducer, useEffect, useMemo, useCallback, ReactNode } from 'react';


import HealthMonitor from '../services/HealthMonitor';
import LoggingService from '../services/LoggingService';
import PerformanceOptimizer from '../services/PerformanceOptimizer';

// InventoryManagementService removed
import { 
  Product, 
  Order, 
  Customer, 
  Outlet, 
  CreateOutletData, 
  UpdateOutletData,
  Staff,
  StaffRole
} from '../types';

// Settings interfaces
interface StoreHours {
  open: string;
  close: string;
  closed: boolean;
}

interface PaymentMethod {
  enabled: boolean;
  processingFee: number;
}

interface Settings {
  storeName: string;
  ownerName: string;
  email: string;
  phone: string;
  address: string;
  taxRate: number;
  currency: string;
  notifications: boolean;
  darkMode: boolean;
  autoBackup: boolean;
  storeHours: {
    monday: StoreHours;
    tuesday: StoreHours;
    wednesday: StoreHours;
    thursday: StoreHours;
    friday: StoreHours;
    saturday: StoreHours;
    sunday: StoreHours;
  };
  paymentMethods: {
    cash: PaymentMethod;
    card: PaymentMethod;
    digitalWallet: PaymentMethod;
    bankTransfer: PaymentMethod;
    giftCard: PaymentMethod;
  };
}

// State interface
interface DataState {
  products: Product[];
  orders: Order[];
  customers: Customer[];
  settings: Settings;
  nextProductId: number;
  nextOrderId: number;
  nextCustomerId: number;
  isDataLoaded: boolean;
  // Outlet-related state
  outlets: Outlet[];
  activeOutlet: Outlet | null;
  outletInventory: any[]; // Simplified
  isOutletServiceInitialized: boolean;
  // Tailor shop state
  staff: Staff[];
  loading: boolean;
  error: string | null;
}

// Action types
enum ActionTypes {
  // Products
  ADD_PRODUCT = 'ADD_PRODUCT',
  UPDATE_PRODUCT = 'UPDATE_PRODUCT',
  DELETE_PRODUCT = 'DELETE_PRODUCT',
  UPDATE_STOCK = 'UPDATE_STOCK',

  // Orders
  ADD_ORDER = 'ADD_ORDER',
  UPDATE_ORDER = 'UPDATE_ORDER',
  DELETE_ORDER = 'DELETE_ORDER',
  UPDATE_ORDER_STATUS = 'UPDATE_ORDER_STATUS',

  // Customers
  ADD_CUSTOMER = 'ADD_CUSTOMER',
  UPDATE_CUSTOMER = 'UPDATE_CUSTOMER',
  DELETE_CUSTOMER = 'DELETE_CUSTOMER',

  // Settings
  UPDATE_SETTINGS = 'UPDATE_SETTINGS',

  // Data
  LOAD_DATA = 'LOAD_DATA',
  SET_DATA_LOADED = 'SET_DATA_LOADED',
  CLEAR_DATA = 'CLEAR_DATA',

  // Outlets
  LOAD_OUTLETS = 'LOAD_OUTLETS',
  ADD_OUTLET = 'ADD_OUTLET',
  UPDATE_OUTLET = 'UPDATE_OUTLET',
  SET_ACTIVE_OUTLET = 'SET_ACTIVE_OUTLET',
  LOAD_OUTLET_INVENTORY = 'LOAD_OUTLET_INVENTORY',
  SET_OUTLET_SERVICE_INITIALIZED = 'SET_OUTLET_SERVICE_INITIALIZED',



  // Staff
  LOAD_STAFF = 'LOAD_STAFF',
  ADD_STAFF = 'ADD_STAFF',
  UPDATE_STAFF = 'UPDATE_STAFF',
  DELETE_STAFF = 'DELETE_STAFF',

  // Loading and Error States
  SET_LOADING = 'SET_LOADING',
  SET_ERROR = 'SET_ERROR',
}

// Action interfaces
interface ProductAction {
  type: ActionTypes.ADD_PRODUCT | ActionTypes.UPDATE_PRODUCT;
  payload: Product;
}

interface DeleteProductAction {
  type: ActionTypes.DELETE_PRODUCT;
  payload: string;
}

interface UpdateStockAction {
  type: ActionTypes.UPDATE_STOCK;
  payload: { id: string; change: number };
}

interface OrderAction {
  type: ActionTypes.ADD_ORDER | ActionTypes.UPDATE_ORDER;
  payload: Order;
}

interface DeleteOrderAction {
  type: ActionTypes.DELETE_ORDER;
  payload: string;
}

interface UpdateOrderStatusAction {
  type: ActionTypes.UPDATE_ORDER_STATUS;
  payload: { id: string; status: string };
}

interface CustomerAction {
  type: ActionTypes.ADD_CUSTOMER | ActionTypes.UPDATE_CUSTOMER;
  payload: Customer;
}

interface DeleteCustomerAction {
  type: ActionTypes.DELETE_CUSTOMER;
  payload: string;
}

interface UpdateSettingsAction {
  type: ActionTypes.UPDATE_SETTINGS;
  payload: Partial<Settings>;
}

interface LoadDataAction {
  type: ActionTypes.LOAD_DATA;
  payload: Partial<DataState>;
}

interface SetDataLoadedAction {
  type: ActionTypes.SET_DATA_LOADED;
  payload: boolean;
}

interface ClearDataAction {
  type: ActionTypes.CLEAR_DATA;
}

interface LoadOutletsAction {
  type: ActionTypes.LOAD_OUTLETS;
  payload: Outlet[];
}

interface AddOutletAction {
  type: ActionTypes.ADD_OUTLET;
  payload: Outlet;
}

interface UpdateOutletAction {
  type: ActionTypes.UPDATE_OUTLET;
  payload: Outlet;
}

interface SetActiveOutletAction {
  type: ActionTypes.SET_ACTIVE_OUTLET;
  payload: Outlet | null;
}

interface LoadOutletInventoryAction {
  type: ActionTypes.LOAD_OUTLET_INVENTORY;
  payload: any[];
}

interface SetOutletServiceInitializedAction {
  type: ActionTypes.SET_OUTLET_SERVICE_INITIALIZED;
  payload: boolean;
}



// Staff Actions
interface StaffAction {
  type: ActionTypes.ADD_STAFF | ActionTypes.UPDATE_STAFF;
  payload: Staff;
}

interface DeleteStaffAction {
  type: ActionTypes.DELETE_STAFF;
  payload: string;
}

interface LoadStaffAction {
  type: ActionTypes.LOAD_STAFF;
  payload: Staff[];
}

// Loading and Error Actions
interface SetLoadingAction {
  type: ActionTypes.SET_LOADING;
  payload: boolean;
}

interface SetErrorAction {
  type: ActionTypes.SET_ERROR;
  payload: string | null;
}

type DataAction =
  | ProductAction
  | DeleteProductAction
  | UpdateStockAction
  | OrderAction
  | DeleteOrderAction
  | UpdateOrderStatusAction
  | CustomerAction
  | DeleteCustomerAction
  | UpdateSettingsAction
  | LoadDataAction
  | SetDataLoadedAction
  | ClearDataAction
  | LoadOutletsAction
  | AddOutletAction
  | UpdateOutletAction
  | SetActiveOutletAction
  | LoadOutletInventoryAction
  | SetOutletServiceInitializedAction
  | StaffAction
  | DeleteStaffAction
  | LoadStaffAction
  | SetLoadingAction
  | SetErrorAction;

// Initial state with completely empty arrays - no dummy data
const initialState: DataState = {
  products: [],
  orders: [],
  customers: [],
  settings: {
    storeName: 'TailorZa',
    ownerName: 'Business Owner',
    email: '<EMAIL>',
    phone: '******-000-0000',
    address: 'Business Address',
    taxRate: 0.08,
    currency: 'BDT',
    notifications: true,
    darkMode: false,
    autoBackup: true,
    storeHours: {
      monday: { open: '09:00', close: '18:00', closed: false },
      tuesday: { open: '09:00', close: '18:00', closed: false },
      wednesday: { open: '09:00', close: '18:00', closed: false },
      thursday: { open: '09:00', close: '18:00', closed: false },
      friday: { open: '09:00', close: '18:00', closed: false },
      saturday: { open: '09:00', close: '17:00', closed: false },
      sunday: { open: '10:00', close: '16:00', closed: true },
    },
    paymentMethods: {
      cash: { enabled: true, processingFee: 0 },
      card: { enabled: true, processingFee: 2.9 },
      digitalWallet: { enabled: false, processingFee: 2.5 },
      bankTransfer: { enabled: false, processingFee: 1.0 },
      giftCard: { enabled: true, processingFee: 0 },
    },
  },
  nextProductId: 1,
  nextOrderId: 1,
  nextCustomerId: 1,
  isDataLoaded: false,
  // Outlet-related initial state
  outlets: [],
  activeOutlet: null,
  outletInventory: [],
  isOutletServiceInitialized: false,
  // Tailor shop state
  staff: [],
  loading: false,
  error: null,
};

// Reducer function
const dataReducer = (state: DataState, action: DataAction): DataState => {
  switch (action.type) {
    case ActionTypes.ADD_PRODUCT:
      return {
        ...state,
        products: [...state.products, { ...action.payload, id: state.nextProductId.toString() }],
        nextProductId: state.nextProductId + 1,
      };

    case ActionTypes.UPDATE_PRODUCT:
      return {
        ...state,
        products: state.products.map(product =>
          product.id === action.payload.id ? { ...product, ...action.payload } : product
        ),
      };

    case ActionTypes.DELETE_PRODUCT:
      return {
        ...state,
        products: state.products.filter(product => product.id !== action.payload),
      };

    case ActionTypes.UPDATE_STOCK:
      return {
        ...state,
        products: state.products.map(product =>
          product.id === action.payload.id
            ? { ...product, stock: Math.max(0, product.stock + action.payload.change) }
            : product
        ),
      };

    case ActionTypes.ADD_ORDER:
      const newOrderId = String(state.nextOrderId).padStart(3, '0');
      return {
        ...state,
        orders: [...state.orders, { ...action.payload, id: newOrderId }],
        nextOrderId: state.nextOrderId + 1,
      };

    case ActionTypes.UPDATE_ORDER:
      return {
        ...state,
        orders: state.orders.map(order =>
          order.id === action.payload.id ? { ...order, ...action.payload } : order
        ),
      };

    case ActionTypes.DELETE_ORDER:
      return {
        ...state,
        orders: state.orders.filter(order => order.id !== action.payload),
      };

    case ActionTypes.UPDATE_ORDER_STATUS:
      return {
        ...state,
        orders: state.orders.map(order =>
          order.id === action.payload.id ? { ...order, status: action.payload.status as any } : order
        ),
      };

    case ActionTypes.ADD_CUSTOMER:
      return {
        ...state,
        customers: [...state.customers, { ...action.payload, id: state.nextCustomerId.toString() }],
        nextCustomerId: state.nextCustomerId + 1,
      };

    case ActionTypes.UPDATE_CUSTOMER:
      return {
        ...state,
        customers: state.customers.map(customer =>
          customer.id === action.payload.id ? { ...customer, ...action.payload } : customer
        ),
      };

    case ActionTypes.DELETE_CUSTOMER:
      return {
        ...state,
        customers: state.customers.filter(customer => customer.id !== action.payload),
      };

    case ActionTypes.UPDATE_SETTINGS:
      return {
        ...state,
        settings: { ...state.settings, ...action.payload },
      };

    case ActionTypes.LOAD_DATA:
      return {
        ...state,
        ...action.payload,
        isDataLoaded: true,
      };

    case ActionTypes.SET_DATA_LOADED:
      return {
        ...state,
        isDataLoaded: action.payload,
      };

    case ActionTypes.CLEAR_DATA:
      return {
        ...initialState,
        isDataLoaded: false,
      };

    case ActionTypes.LOAD_OUTLETS:
      return {
        ...state,
        outlets: action.payload,
      };

    case ActionTypes.ADD_OUTLET:
      return {
        ...state,
        outlets: [...state.outlets, action.payload],
      };

    case ActionTypes.UPDATE_OUTLET:
      return {
        ...state,
        outlets: state.outlets.map(outlet =>
          outlet.id === action.payload.id ? action.payload : outlet
        ),
        activeOutlet: state.activeOutlet?.id === action.payload.id ? action.payload : state.activeOutlet,
      };

    case ActionTypes.SET_ACTIVE_OUTLET:
      return {
        ...state,
        activeOutlet: action.payload,
      };

    case ActionTypes.LOAD_OUTLET_INVENTORY:
      return {
        ...state,
        outletInventory: action.payload,
      };

    case ActionTypes.SET_OUTLET_SERVICE_INITIALIZED:
      return {
        ...state,
        isOutletServiceInitialized: action.payload,
      };

    default:
      return state;
  }
};

// Context interface
interface DataContextType {
  state: DataState;
  actions: {
    // Products
    addProduct: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Product>;
    updateProduct: (product: Product) => Promise<Product>;
    deleteProduct: (id: string) => Promise<void>;
    updateStock: (id: string, change: number) => Promise<void>;

    // Orders
    addOrder: (order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Order>;
    updateOrder: (order: Order) => Promise<Order>;
    deleteOrder: (id: string) => Promise<void>;
    updateOrderStatus: (id: string, status: string) => Promise<void>;

    // Customers
    addCustomer: (customer: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Customer>;
    updateCustomer: (customer: Customer) => Promise<Customer>;
    deleteCustomer: (id: string) => Promise<void>;

    // Settings
    updateSettings: (settings: Partial<Settings>) => void;

    // Outlets
    createOutlet: (outlet: CreateOutletData) => Promise<Outlet>;
    updateOutlet: (id: string, updates: UpdateOutletData) => Promise<Outlet>;
    deactivateOutlet: (id: string) => Promise<void>;
    setActiveOutlet: (outletId: string) => Promise<void>;
    getActiveOutlet: () => Promise<Outlet | null>;
    getOutletInventory: (outletId: string) => Promise<any[]>;
    updateOutletInventory: (outletId: string, itemId: string, quantity: number) => Promise<void>;
    transferInventory: (fromOutletId: string, toOutletId: string, itemId: string, quantity: number) => Promise<void>;
    refreshOutlets: () => Promise<void>;

    // Outlet-specific data filtering
    getOutletSpecificData: () => {
      products: Product[];
      orders: Order[];
      customers: Customer[];
      inventory: any[];
    };
    applyOutletFilter: <T extends { outletId?: string }>(data: T[]) => T[];

    // Data Management
    importData: (importedData: any) => void;
    exportData: () => any;
    clearData: () => Promise<void>;
    forceSave: () => Promise<void>;
    reloadData: () => Promise<void>;

    // Enterprise monitoring methods
    getPerformanceScore: () => number;
    getHealthStatus: () => any;
    generateHealthReport: () => any;
    getOverallAppScore: () => any;
    generateSampleData: () => Promise<void>;



    // Measurement actions - simplified
    saveMeasurement: (measurement: any) => Promise<any>;
    updateMeasurement: (id: string, updates: any) => Promise<any>;
    deleteMeasurement: (id: string) => Promise<void>;
    getMeasurementHistory: (customerId: string, garmentType?: any) => Promise<any[]>;
    getTemplateByGarmentType: (garmentType: any) => Promise<any | null>;



    // Staff actions
    createStaff: (staff: any) => Promise<Staff>;
    updateStaff: (id: string, updates: any) => Promise<Staff>;
    deleteStaff: (id: string) => Promise<void>;
    updateWorkload: (staffId: string, workload: any) => Promise<void>;
    updatePerformance: (staffId: string, performance: any) => Promise<void>;
    getAvailableStaff: (outletId: string, skill?: string) => Promise<Staff[]>;
    analyzeWorkload: (staffId: string) => Promise<any>;
    generatePerformanceReport: (staffId: string) => Promise<any>;
  };
}

// Create context
const DataContext = createContext<DataContextType | undefined>(undefined);

// Provider component with performance optimizations
interface DataProviderProps {
  children: ReactNode;
}

export const DataProvider: React.FC<DataProviderProps> = ({ children }) => {
  LoggingService.debug('DataProvider rendered', 'DATA_CONTEXT');
  const [state, dispatch] = useReducer(dataReducer, initialState);

  // Load data from AsyncStorage on app start
  useEffect(() => {
    loadData();
  }, []);

  // Debounced save to prevent excessive writes - reduced to 500ms for snappier feel
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      saveData();
    }, 500); // Save after 500ms of inactivity for better responsiveness

    return () => clearTimeout(timeoutId);
  }, [state.products, state.orders, state.customers, state.settings]); // Only watch specific state properties

  const loadData = useCallback(async () => {
    const startTime = Date.now();
    try {
      LoggingService.info('Loading data with enterprise optimization...', 'DATA_CONTEXT');
      PerformanceOptimizer.measureRenderTime('DataContext.loadData', 0);

      // Load settings from AsyncStorage
      const savedSettings = await AsyncStorage.getItem('bakerySettings');
      const settings = savedSettings ?
        { ...initialState.settings, ...JSON.parse(savedSettings) } :
        initialState.settings;

      // Initialize with empty data (services removed)
      const products: Product[] = [];
      const orders: Order[] = [];
      const customers: Customer[] = [];
      const outlets: Outlet[] = [];
      const activeOutlet: Outlet | null = null;

      LoggingService.info(`Data loaded: ${products.length} products, ${orders.length} orders, ${customers.length} customers, ${outlets.length} outlets`, 'DATA_CONTEXT');

      // Calculate next IDs
      const nextProductId = 1;
      const nextOrderId = 1;
      const nextCustomerId = 1;

      const loadedData = {
        products,
        orders,
        customers,
        settings,
        nextProductId,
        nextOrderId,
        nextCustomerId,
        outlets,
        activeOutlet,
        outletInventory: [],
      };

      dispatch({ type: ActionTypes.LOAD_DATA, payload: loadedData });

      // Track performance metrics
      const loadTime = Date.now() - startTime;
      PerformanceOptimizer.measureRenderTime('DataContext.loadData', loadTime);
      LoggingService.info('Data loading completed successfully', 'DATA_CONTEXT', {
        loadTime,
        productsCount: products.length,
        ordersCount: orders.length,
        customersCount: customers.length,
        outletsCount: outlets.length,
        activeOutletId: (activeOutlet as Outlet | null)?.id || null,
      });
    } catch (error) {
      LoggingService.error('Error loading data', 'DATA_CONTEXT', error as Error);
      // Fallback to empty state on error
      dispatch({ type: ActionTypes.SET_DATA_LOADED, payload: true });
    }
  }, []);

  const saveData = useCallback(async () => {
    // Only save if data has been loaded to prevent overwriting with empty state
    if (!state.isDataLoaded) {
      return;
    }

    try {
      // Save settings to AsyncStorage
      await AsyncStorage.setItem('bakerySettings', JSON.stringify(state.settings));

      // Note: Products, orders, and customers are now handled in memory only
      // This function now mainly handles settings.

      LoggingService.info('Settings saved successfully', 'DATA_CONTEXT');
    } catch (error) {
      LoggingService.error('Error saving settings', 'DATA_CONTEXT', error as Error);
      // Don't crash the app on save errors
    }
  }, [state.settings, state.isDataLoaded]);

  // Memoized action creators for performance with SQLite optimization
  const actions = useMemo(() => ({
    // Products - now in memory only
    addProduct: async (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<Product> => {
      const savedProduct: Product = {
        ...product,
        id: state.nextProductId.toString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      dispatch({ type: ActionTypes.ADD_PRODUCT, payload: savedProduct });
      return savedProduct;
    },

    updateProduct: async (product: Product): Promise<Product> => {
      const updatedProduct: Product = {
        ...product,
        updatedAt: new Date().toISOString()
      };
      dispatch({ type: ActionTypes.UPDATE_PRODUCT, payload: updatedProduct });
      return updatedProduct;
    },

    deleteProduct: async (id: string): Promise<void> => {
      // Remove from state only
      dispatch({ type: ActionTypes.DELETE_PRODUCT, payload: id });
    },

    updateStock: async (id: string, change: number): Promise<void> => {
      // Get current product from state, update stock
      const product = state.products.find(p => p.id === id);
      if (product) {
        const updatedProduct = {
          ...product,
          stock: Math.max(0, product.stock + change),
          updatedAt: new Date().toISOString()
        };
        dispatch({ type: ActionTypes.UPDATE_PRODUCT, payload: updatedProduct });
      }
    },

    // Orders - now in memory only
    addOrder: async (order: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>): Promise<Order> => {
      const savedOrder: Order = {
        ...order,
        id: String(state.nextOrderId).padStart(3, '0'),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      dispatch({ type: ActionTypes.ADD_ORDER, payload: savedOrder });
      return savedOrder;
    },

    updateOrder: async (order: Order): Promise<Order> => {
      const updatedOrder: Order = {
        ...order,
        updatedAt: new Date().toISOString()
      };
      dispatch({ type: ActionTypes.UPDATE_ORDER, payload: updatedOrder });
      return updatedOrder;
    },

    deleteOrder: async (id: string): Promise<void> => {
      // Remove from state only
      dispatch({ type: ActionTypes.DELETE_ORDER, payload: id });
    },

    updateOrderStatus: async (id: string, status: string): Promise<void> => {
      const orders: Order[] = [];
      const order = orders.find(o => o.id === id);
      if (order) {
        const updatedOrder = {
          ...order,
          status: status as any,
          updatedAt: new Date().toISOString()
        };
        // Order update not implemented
        dispatch({ type: ActionTypes.UPDATE_ORDER_STATUS, payload: { id, status } });
      }
    },

    // Customers - now in memory only
    addCustomer: async (customer: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>): Promise<Customer> => {
      const savedCustomer: Customer = {
        ...customer,
        id: state.nextCustomerId.toString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      dispatch({ type: ActionTypes.ADD_CUSTOMER, payload: savedCustomer });
      return savedCustomer;
    },

    updateCustomer: async (customer: Customer): Promise<Customer> => {
      const updatedCustomer: Customer = {
        ...customer,
        updatedAt: new Date().toISOString()
      };
      dispatch({ type: ActionTypes.UPDATE_CUSTOMER, payload: updatedCustomer });
      return updatedCustomer;
    },

    deleteCustomer: async (id: string): Promise<void> => {
      // Remove from state only
      dispatch({ type: ActionTypes.DELETE_CUSTOMER, payload: id });
    },

    // Outlets
    createOutlet: async (outletData: CreateOutletData): Promise<Outlet> => {
      const outlet: Outlet = {
        ...outletData,
        id: `outlet_${Date.now()}`,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      dispatch({ type: ActionTypes.ADD_OUTLET, payload: outlet });
      return outlet;
    },

    updateOutlet: async (id: string, updates: UpdateOutletData): Promise<Outlet> => {
      // This would need to be implemented with state updates
      const outlet: Outlet = {
        id,
        name: 'Updated Outlet',
        address: 'Updated Address',
        phone: 'Updated Phone',
        email: '<EMAIL>',
        isActive: true,
        operatingHours: {
          monday: '09:00-18:00',
          tuesday: '09:00-18:00',
          wednesday: '09:00-18:00',
          thursday: '09:00-18:00',
          friday: '09:00-18:00',
          saturday: '09:00-18:00',
          sunday: '09:00-18:00'
        },
        settings: {
          taxRate: 0.1,
          currency: 'USD',
          timezone: 'UTC',
          defaultMeasurementUnit: 'cm'
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      dispatch({ type: ActionTypes.UPDATE_OUTLET, payload: outlet });
      return outlet;
    },

    deactivateOutlet: async (id: string): Promise<void> => {
      // This would need to be implemented with state updates
      LoggingService.info('Outlet deactivation not implemented', 'DATA_CONTEXT');
    },

    setActiveOutlet: async (outletId: string): Promise<void> => {
      // This would need to be implemented with state updates
      LoggingService.info('Set active outlet not implemented', 'DATA_CONTEXT');
    },

    getActiveOutlet: async (): Promise<Outlet | null> => {
      return state.activeOutlet;
    },

    getOutletInventory: async (outletId: string): Promise<any[]> => {
      return state.outletInventory;
    },

    updateOutletInventory: async (outletId: string, itemId: string, quantity: number): Promise<void> => {
      // This would need to be implemented with state updates
      LoggingService.info('Update outlet inventory not implemented', 'DATA_CONTEXT');
    },

    transferInventory: async (fromOutletId: string, toOutletId: string, itemId: string, quantity: number): Promise<void> => {
      // This would need to be implemented with state updates
      LoggingService.info('Transfer inventory not implemented', 'DATA_CONTEXT');
    },

    refreshOutlets: async (): Promise<void> => {
      // This would need to be implemented with state updates
      LoggingService.info('Refresh outlets not implemented', 'DATA_CONTEXT');
    },

    // Outlet-specific data filtering
    getOutletSpecificData: () => {
      const activeOutletId = state.activeOutlet?.id;

      return {
        products: activeOutletId
          ? state.products.filter(product =>
            (product as any).outletId === activeOutletId || !(product as any).outletId
          )
          : state.products,
        orders: activeOutletId
          ? state.orders.filter(order =>
            (order as any).outletId === activeOutletId || !(order as any).outletId
          )
          : state.orders,
        customers: activeOutletId
          ? state.customers.filter(customer =>
            (customer as any).outletId === activeOutletId || !(customer as any).outletId
          )
          : state.customers,
        inventory: state.outletInventory,
      };
    },

    applyOutletFilter: <T extends { outletId?: string }>(data: T[]): T[] => {
      const activeOutletId = state.activeOutlet?.id;
      if (!activeOutletId) {
        return data;
      }
      return data.filter(item => item.outletId === activeOutletId || !item.outletId);
    },

    // Settings
    updateSettings: (settings: Partial<Settings>) => dispatch({ type: ActionTypes.UPDATE_SETTINGS, payload: settings }),

    // Data Management
    importData: (importedData: any) => {
      try {
        // Validate imported data
        const validatedData = {
          products: Array.isArray(importedData.products) ? importedData.products : [],
          orders: Array.isArray(importedData.orders) ? importedData.orders : [],
          customers: Array.isArray(importedData.customers) ? importedData.customers : [],
          settings: importedData.settings || state.settings,
        };

        // Update next IDs based on imported data
        const nextProductId = validatedData.products.length > 0 ? Math.max(...validatedData.products.map((p: Product) => parseInt(p.id))) + 1 : 1;
        const nextOrderId = validatedData.orders.length > 0 ? Math.max(...validatedData.orders.map((o: Order) => parseInt(o.id))) + 1 : 1;
        const nextCustomerId = validatedData.customers.length > 0 ? Math.max(...validatedData.customers.map((c: Customer) => parseInt(c.id))) + 1 : 1;

        dispatch({
          type: ActionTypes.LOAD_DATA,
          payload: {
            ...validatedData,
            nextProductId,
            nextOrderId,
            nextCustomerId,
          }
        });

        LoggingService.info(`Data imported successfully: ${validatedData.products.length} products, ${validatedData.orders.length} orders, ${validatedData.customers.length} customers`, 'DATA_CONTEXT');
      } catch (error) {
        LoggingService.error('Error importing data', 'DATA_CONTEXT', error as Error);
      }
    },

    exportData: () => {
      return {
        products: state.products,
        orders: state.orders,
        customers: state.customers,
        settings: state.settings,
        exportDate: new Date().toISOString(),
        version: '1.0.0',
      };
    },

    clearData: async () => {
      try {
        await AsyncStorage.removeItem('bakeryData');
        dispatch({ type: ActionTypes.CLEAR_DATA });
        LoggingService.info('All data cleared successfully', 'DATA_CONTEXT');
      } catch (error) {
        LoggingService.error('Error clearing data', 'DATA_CONTEXT', error as Error);
      }
    },

    // Force save data immediately (for critical operations)
    forceSave: async () => {
      await saveData();
    },

    // Reload data from database
    reloadData: async () => {
      await loadData();
    },

    // Enterprise monitoring methods
    getPerformanceScore: () => {
      return PerformanceOptimizer.getScore();
    },

    getHealthStatus: () => {
      return HealthMonitor.getHealthStatus();
    },

    generateHealthReport: () => {
      return HealthMonitor.generateHealthReport();
    },

    getOverallAppScore: () => {
      const performanceScore = PerformanceOptimizer.getScore();
      const healthScore = HealthMonitor.getOverallScore();

      const overallScore = (performanceScore + healthScore) / 2;

      LoggingService.info('Overall app score calculated', 'ENTERPRISE', {
        performanceScore,
        healthScore,
        overallScore,
      });

      return {
        overallScore: Math.round(overallScore * 100) / 100,
        breakdown: {
          performance: performanceScore,
          health: healthScore,
        },
        grade: overallScore >= 95 ? 'A+' : overallScore >= 90 ? 'A' : overallScore >= 85 ? 'B+' : 'B',
        status: overallScore >= 95 ? 'Excellent' : overallScore >= 90 ? 'Very Good' : overallScore >= 85 ? 'Good' : 'Fair',
      };
    },

    // Generate sample data (100 products and 100 customers)
    generateSampleData: async () => {
      try {
        // Sample data generation not implemented
        // Reload data after generation
        await loadData();
        LoggingService.info('Sample data generated and reloaded successfully', 'DATA_CONTEXT');
      } catch (error) {
        LoggingService.error('Failed to generate sample data', 'DATA_CONTEXT', error as Error);
      }
    },



    // Measurement actions - simplified stubs
    saveMeasurement: async (measurement: any) => {
      LoggingService.info('Measurement functionality not implemented', 'DATA_CONTEXT');
      return { id: 'stub', ...measurement };
    },

    updateMeasurement: async (id: string, updates: any) => {
      LoggingService.info('Measurement functionality not implemented', 'DATA_CONTEXT');
      return { id, ...updates };
    },

    deleteMeasurement: async (id: string) => {
      LoggingService.info('Measurement functionality not implemented', 'DATA_CONTEXT');
    },

    getMeasurementHistory: async (customerId: string, garmentType?: any) => {
      LoggingService.info('Measurement functionality not implemented', 'DATA_CONTEXT');
      return [];
    },

    getTemplateByGarmentType: async (garmentType: any) => {
      LoggingService.info('Measurement functionality not implemented', 'DATA_CONTEXT');
      return null;
    },



    // Staff actions
    createStaff: async (staff: any) => {
      try {
        const newStaff: Staff = {
          ...staff,
          id: `staff_${Date.now()}`,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        dispatch({ type: ActionTypes.ADD_STAFF, payload: newStaff });
        return newStaff;
      } catch (error) {
        LoggingService.error('Failed to create staff', 'DATA_CONTEXT', error as Error);
        throw error;
      }
    },

    updateStaff: async (id: string, updates: any) => {
      try {
        const updatedStaff: Staff = {
          id,
          ...updates,
          updatedAt: new Date().toISOString()
        } as Staff;
        dispatch({ type: ActionTypes.UPDATE_STAFF, payload: updatedStaff });
        return updatedStaff;
      } catch (error) {
        LoggingService.error('Failed to update staff', 'DATA_CONTEXT', error as Error);
        throw error;
      }
    },

    deleteStaff: async (id: string) => {
      try {
        dispatch({ type: ActionTypes.DELETE_STAFF, payload: id });
      } catch (error) {
        LoggingService.error('Failed to delete staff', 'DATA_CONTEXT', error as Error);
        throw error;
      }
    },

    updateWorkload: async (staffId: string, workload: any) => {
      try {
        LoggingService.info('Update workload not implemented', 'DATA_CONTEXT');
      } catch (error) {
        LoggingService.error('Failed to update workload', 'DATA_CONTEXT', error as Error);
        throw error;
      }
    },

    updatePerformance: async (staffId: string, performance: any) => {
      try {
        LoggingService.info('Update performance not implemented', 'DATA_CONTEXT');
      } catch (error) {
        LoggingService.error('Failed to update performance', 'DATA_CONTEXT', error as Error);
        throw error;
      }
    },

    getAvailableStaff: async (outletId: string, skill?: string) => {
      try {
        return state.staff.filter(s => s.outletId === outletId);
      } catch (error) {
        LoggingService.error('Failed to get available staff', 'DATA_CONTEXT', error as Error);
        throw error;
      }
    },

    analyzeWorkload: async (staffId: string) => {
      try {
        LoggingService.info('Analyze workload not implemented', 'DATA_CONTEXT');
        return { staffId, analysis: 'Not implemented' };
      } catch (error) {
        LoggingService.error('Failed to analyze workload', 'DATA_CONTEXT', error as Error);
        throw error;
      }
    },

    generatePerformanceReport: async (staffId: string) => {
      try {
        LoggingService.info('Generate performance report not implemented', 'DATA_CONTEXT');
        return { staffId, report: 'Not implemented' };
      } catch (error) {
        LoggingService.error('Failed to generate performance report', 'DATA_CONTEXT', error as Error);
        throw error;
      }
    },

  }), [dispatch, state.products, state.orders, state.customers, state.settings, state.outlets, state.activeOutlet, state.nextOrderId, saveData, loadData]);

  return (
    <DataContext.Provider value={{ state, actions }}>
      {children}
    </DataContext.Provider>
  );
};

// Custom hook to use the context
export const useData = (): DataContextType => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

export default DataContext;